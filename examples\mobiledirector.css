body{
	zoom: 75%;
}
button[data-action-type='solo-chat'] {
	display:none! important;
}

button[data-action-type] {
	padding: 20px 10px;
}

#controlButtons{
	display:none! important;
}

button[data-cluster='2'] {
	display:none! important;
}

button[data-action-type='solo-video'] {
	display:unset!important;
	visibility: visible;
	width:unset;
	height:unset;
	opacity:  1;
}

div > a.soloLink{
	display:none! important;
}

div.shift{
	display:none! important;
}

div.streamID{
	display:none! important;
}

button[data-action-type='forward'] {
	display:none! important;
}

button[data-action-type='direct-chat'] {
	display:none! important;
}

button[data-action-type='hangup'] {
	display:none! important;
}

button[data-action-type='solo-chat'] {
	display:none! important;
}

button[data-action-type='solo-chat'] {
	display:none! important;
}

button[data-cluster='1'] {
	display:none! important;
}


button[data-action-type='recorder-local'] {
	display:none! important;
}
span[data-action-type='ordering'] {
	display:none! important;
}
button[data-action-type='open-file-share'] {
	display:none! important;
}

button[data-action-type='add-channel']{
	display:none! important;
}
button[data-action-type='toggle-remote-speaker']{
	display:none! important;
}
button[data-action-type='toggle-remote-display']{
	display:none! important;
}
button[data-action-type='hide-guest']{
	display:none! important;
}
button[data-action-type='create-timer']{
	display:none! important;
}
button[data-action-type='change-url']{
	display:none! important;
}
button[data-action-type='change-params']{
	display:none! important;
}
span[data-action-type='change-quality']{
	display:none! important;
}

span[data-action-type='sceneCluster2']{
	display:none! important;
}
span[data-action-type='sceneCluster1']{
	display:none! important;
}

.orderspan{
	display:none! important;
}
#roomHeader{
	display:none! important;
}
.directorContainer {
	display:none!important;
}

.hideDropMenu{
	display:none!important;
}

#header{
	display:none!important;
}
body {
	background-color: #000;
}

button[class="pull-right"]{
	display:none! important;
}

div#guestFeeds {
	padding: 1px!important;
	margin: 1px!important;
}
div[class="vidcon directorMargins"] {
	padding: 1px!important;
	margin: 1px!important;
	width: 260px;
}
button[data-action-type]{
	margin: 1px!important;
}




