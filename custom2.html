<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VDO.Ninja - App</title>
    <link rel="stylesheet" href="./main.css" />
    <script type="text/javascript" crossorigin="anonymous" src="./thirdparty/adapter.js"></script>
    <script type="text/javascript" crossorigin="anonymous"  src="./thirdparty/CodecsHandler.js"></script>
    <script type="text/javascript" crossorigin="anonymous" src="./thirdparty/aes.js"></script>
    <script type="text/javascript" crossorigin="anonymous" src="./webrtc.js"></script>
    <script type="text/javascript" crossorigin="anonymous" src="./main.js"></script>
    <style>
        body { font-family: sans-serif; background-color: #121212; color: #e0e0e0; display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; }
        .container { background-color: #1e1e1e; padding: 2em; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); text-align: center; width: 90%; max-width: 500px; }
        input[type="text"] { padding: 0.8em; margin: 1em 0; width: 80%; background-color: #333; border: 1px solid #555; color: #e0e0e0; border-radius: 4px; }
        button { padding: 0.8em 1.5em; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 1em; margin-top: 1em; }
        button:hover { background-color: #0056b3; }
        .hidden { display: none; }
        #media-selection video { width: 100%; max-width: 400px; border-radius: 4px; margin-bottom: 1em; background-color: #000; }
        #media-selection select { width: 80%; padding: 0.5em; margin-bottom: 1em; background-color: #333; border: 1px solid #555; color: #e0e0e0; border-radius: 4px;}

        /* Style for the main video display */
        #main-video-display {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            background-color: black;
        }
        #main-video-display video {
            width: 100%;
            height: 100%;
            object-fit: contain; /* or cover, depending on preference */
        }
    </style>
</head>
<body>

    <div id="join-room-container" class="container">
        <h2>Join a Room</h2>
        <input type="text" id="roomName" placeholder="Enter Room Name">
        <br>
        <button onclick="joinRoom()">Join</button>
    </div>

    <div id="media-selection-container" class="container hidden">
        <h2>Camera and Microphone</h2>
        <div id="media-selection">
            <video id="preview" autoplay muted playsinline></video>
            <select id="video-source"></select>
            <select id="audio-source"></select>
            <select id="audio-output"></select>
        </div>
        <button onclick="start()">Start</button>
    </div>

    <div id="main-video-display" class="hidden">
        <!-- The main video stream will be displayed here -->
        <video id="mainStreamVideo" autoplay playsinline></video>
    </div>

    <script>
        // Initialize session object from webrtc.js
        var session = WebRTC.Media;

        let roomName = '';
        let password = '';

        // This function will be called after all scripts are loaded.
        // It will hide the default UI elements that main.js might create.
        function initializeAppUI() {
            // Hide elements that main.js might make visible
            const elementsToHide = [
                'header', 'mainmenu', 'controlButtons', 'miniTaskBar',
                'directorlayout', 'popupSelector', 'roomSettings',
                'publishSettings', 'remoteOBSControl', 'transferSettingsTemplate',
                'connectUsers', 'callerMenu', 'progressContainer',
                'signalMeterTemplate', 'volumeControlTemplate', 'batteryMeterTemplate',
                'slotPicker', 'voiceMeterTemplate', 'voiceMeterTemplate2',
                'muteStateTemplate', 'videoMuteStateTemplate', 'raisedHandTemplate',
                'selectImage_contents', 'selectImageOverlay_contents', 'gridlayout',
                'testtone', 'dragImage', 'request_info_prompt', 'screenPopup',
                'messagePopup', 'languages', 'calendar', 'hangupTemplate', 'meshcastMenu'
            ];

            elementsToHide.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.classList.add('hidden');
                    element.style.display = 'none'; // Ensure it's hidden
                }
            });

            // Ensure my initial UI is visible
            document.getElementById('join-room-container').classList.remove('hidden');
            document.getElementById('join-room-container').style.display = 'block';
        }

        // Call main() from main.js to initialize the session object and other global states.
        // This needs to be done after main.js is loaded.
        // We will call it after the DOM is ready.
        document.addEventListener('DOMContentLoaded', async () => {
            // Call main() from main.js to initialize the session object and other global states.
            // This is crucial for session.startStream() to work.
            // However, main() also builds UI. So we need to hide it immediately after.
            if (typeof main === 'function') {
                await main(); // Call the main function from main.js
            }
            initializeAppUI(); // Hide unwanted UI elements
        });


        async function joinRoom() {
            roomName = document.getElementById('roomName').value;
            if (!roomName) {
                alert('Please enter a room name.');
                return;
            }

            password = prompt('Enter a password if provided, otherwise just click Cancel') || '';

            document.getElementById('join-room-container').classList.add('hidden');
            document.getElementById('media-selection-container').classList.remove('hidden');
            
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true, video: true });
                stream.getTracks().forEach(track => track.stop());
                await populateDeviceSelectors();
            } catch (e) {
                alert('Could not access camera and microphone. Please allow permissions in your browser settings.');
                console.error(e);
            }
        }

        async function populateDeviceSelectors() {
            const videoSelect = document.getElementById('video-source');
            const audioSelect = document.getElementById('audio-source');
            const audioOutputSelect = document.getElementById('audio-output');
            
            videoSelect.innerHTML = '';
            audioSelect.innerHTML = '';
            audioOutputSelect.innerHTML = '';

            const devices = await navigator.mediaDevices.enumerateDevices();
            const videoDevices = devices.filter(device => device.kind === 'videoinput');
            const audioDevices = devices.filter(device => device.kind === 'audioinput');
            const audioOutputDevices = devices.filter(device => device.kind === 'audiooutput');

            if (videoDevices.length === 0) {
                videoSelect.innerHTML = '<option value="">No cameras found</option>';
            } else {
                 videoDevices.forEach(device => {
                    const option = document.createElement('option');
                    option.value = device.deviceId;
                    option.text = device.label || `Camera ${videoSelect.length + 1}`;
                    videoSelect.appendChild(option);
                });
            }

            if (audioDevices.length === 0) {
                audioSelect.innerHTML = '<option value="">No microphones found</option>';
            } else {
                audioDevices.forEach(device => {
                    const option = document.createElement('option');
                    option.value = device.deviceId;
                    option.text = device.label || `Microphone ${audioSelect.length + 1}`;
                    audioSelect.appendChild(option);
                });
            }

            if (audioOutputDevices.length === 0) {
                audioOutputSelect.innerHTML = '<option value="">No audio output found</option>';
            } else {
                audioOutputDevices.forEach(device => {
                    const option = document.createElement('option');
                    option.value = device.deviceId;
                    option.text = device.label || `Speaker ${audioOutputSelect.length + 1}`;
                    audioOutputSelect.appendChild(option);
                });
            }
           
            videoSelect.onchange = startPreview;
            audioSelect.onchange = startPreview;
            
            await startPreview();
        }

        async function startPreview() {
            const videoSelect = document.getElementById('video-source');
            const audioSelect = document.getElementById('audio-source');
            const preview = document.getElementById('preview');

            if (window.stream) {
                window.stream.getTracks().forEach(track => track.stop());
            }

            const videoSource = videoSelect.value;
            const audioSource = audioSelect.value;

            const constraints = {
                video: videoSource ? { deviceId: { exact: videoSource } } : false,
                audio: audioSource ? { deviceId: { exact: audioSource } } : false
            };

            if (!constraints.video && !constraints.audio) {
                 preview.srcObject = null;
                 return;
            }
            if (!constraints.video) delete constraints.video;
            if (!constraints.audio) delete constraints.audio;

            try {
                const stream = await navigator.mediaDevices.getUserMedia(constraints);
                window.stream = stream;
                preview.srcObject = stream;
            } catch (e) {
                console.error('Error starting preview:', e);
                preview.srcObject = null;
            }
        }

        async function start() {
            const videoSource = document.getElementById('video-source').value;
            const audioSource = document.getElementById('audio-source').value;
            const audioOutput = document.getElementById('audio-output').value;

            // Set session properties
            session.roomid = roomName;
            session.password = password;
            session.videoDevice = videoSource;
            session.audioDevice = audioSource;
            session.sinkId = audioOutput;
            session.webcamonly = true; // This flag is important for publishWebcam to work as expected

            // Hide media selection UI
            document.getElementById('media-selection-container').classList.add('hidden');
            // Show main video display
            document.getElementById('main-video-display').classList.remove('hidden');

            const mainStreamVideo = document.getElementById('mainStreamVideo');

            try {
                // Get the stream again with the selected devices for the main display
                const constraints = {
                    video: videoSource ? { deviceId: { exact: videoSource } } : false,
                    audio: audioSource ? { deviceId: { exact: audioSource } } : false
                };

                if (!constraints.video && !constraints.audio) {
                    alert('No media devices selected to start stream.');
                    return;
                }
                if (!constraints.video) delete constraints.video;
                if (!constraints.audio) delete constraints.audio;

                const stream = await navigator.mediaDevices.getUserMedia(constraints);
                mainStreamVideo.srcObject = stream; // Display the stream in the main video element

                // Set the local stream in the session object
                session.localStream = stream;

                // Call session.startStream() to initiate the WebRTC connection and publish the stream
                session.startStream(stream);

            } catch (e) {
                console.error('Error starting main stream:', e);
                alert('Error starting main stream. Check console for details.');
            }
        }
    </script>
</body>
</html>