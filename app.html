<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VDO.Ninja - App</title>
    <link rel="stylesheet" href="./main.css" />
    <script type="text/javascript" crossorigin="anonymous" src="./thirdparty/adapter.js"></script>
    <script type="text/javascript" crossorigin="anonymous"  src="./thirdparty/CodecsHandler.js"></script>
    <script type="text/javascript" crossorigin="anonymous" src="./thirdparty/aes.js"></script>
    <script type="text/javascript" crossorigin="anonymous" src="./webrtc.js"></script>
    <script type="text/javascript" crossorigin="anonymous" src="./main.js"></script>
    <style>
        body { font-family: sans-serif; background-color: #121212; color: #e0e0e0; display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; }
        .container { background-color: #1e1e1e; padding: 2em; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); text-align: center; width: 90%; max-width: 500px; }
        input[type="text"] { padding: 0.8em; margin: 1em 0; width: 80%; background-color: #333; border: 1px solid #555; color: #e0e0e0; border-radius: 4px; }
        button { padding: 0.8em 1.5em; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 1em; margin-top: 1em; }
        button:hover { background-color: #0056b3; }
        .hidden { display: none; }
        #media-selection video { width: 100%; max-width: 400px; border-radius: 4px; margin-bottom: 1em; background-color: #000; }
        #media-selection select { width: 80%; padding: 0.5em; margin-bottom: 1em; background-color: #333; border: 1px solid #555; color: #e0e0e0; border-radius: 4px;}

        /* Style for the main video display */
        #main-video-display {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            background-color: black;
        }
        #main-video-display video {
            width: 100%;
            height: 100%;
            object-fit: contain; /* or cover, depending on preference */
        }
    </style>
</head>
<body>
		<input id="zoomSlider" type="range" style="display: none;" />
		<span id="electronDragZone" style="pointer-events: none; z-index:-10; position:absolute;top:0;left:0;width:100%;height:2%;-webkit-app-region: drag;min-height:20px;"></span>
<div id="head5" class="hidden"></div>
			<div id="head3" style="display: inline-block;" class="hidden">
				<span style="color: #888;" id="copythisurl" tabindex="1" > &nbsp; 
					<span data-translate="copy-this-url">Copy this URL into an OBS "Browser Source"</span> <i style="color: #CCC;" class="las la-long-arrow-alt-right"></i> &nbsp; 
				</span>
			</div>
			<div id="head3a" style="display: inline-block;" class="hidden">
				<a
					id="reshare"
					data-drag="1"
					onclick="copyFunction(this, event)"
					class="task grabLinks"
					data-menu="context-menu"
					style="font-weight: bold; color: #afa !important; cursor: grab; background-color: #0000;  font-size: 115%; min-width: 335px; max-width: 800px;"
				></a>
				<i class="las la-paperclip" style="color: #DDD;" title="Copy link to clipboard" onclick="copyFunction(document.getElementById('reshare'), event);" onmouseover="this.style.cursor='pointer'"></i>
				<span title="Save and ask to reload the current page on next site visit" style='font-size:92%;' onclick="saveRoom(this);" onmouseover="this.style.cursor='pointer'">💾</span>
			</div>
			<div id="head4" style="display: inline-block;" class="hidden">
				<span style="font-size: 68%; color: white;">
					<span data-translate="you-are-in-the-control-center">Control center for room:</span>
					
					<div id="dirroomid" style="font-size: 140%; color: #99c; display: inline-block;"></div>
					<span id="saveRoom" onclick="saveRoom(this)" style='cursor:pointer;margin-left:10px;' title="Will remember the room, prompting you the next time you visit if you wish to load this director's room again">💾</span>
					<span id="togglePreviewMode" onclick="switchModes()" style='cursor:pointer;margin-left:2px;' title="Toggle between the director control-room view and a scene preview-mode.">🪟</span>
				</span>
			</div>
			<div id="head2" class="hidden" style="display: inline-block; text-decoration: none; font-size: 60%; color: white;">
				<span data-translate="joining-room">You are in room</span>:
				<div id="roomid" style="display: inline-block;"></div>
			</div>
			<div id="head6" class="hidden" data-translate="only-director-can-hear-you">Only the director can hear you currently.</div>
			<div id="head7" class="hidden" data-translate="director-muted-you">The director has muted you.</div>
			<div id="head8" class="hidden" data-translate="director-video-muted-you">The director has disabled your camera temporarily.</div>
		</div>
		<div id="obsState" class="hidden" >ACTIVE</div>
		<div id="chatModule" class="hidden">
			<div class="chat-header">
				<span>Chat</span>
				<span>
					<a id="popOutChat" onclick="createPopoutChat();"><i class="las la-external-link-alt"></i></a>
					<a id="closeChat" onclick="toggleChat();">x</i></a>
				</span>
			</div>
			
			<div id="chatBody" class="resizable-div">
				<!-- Chat messages will be inserted here -->
			</div>
			
			<div class="chat-input-area">
				<input type="text" id="chatInput" placeholder="Enter chat message to send" onkeypress="EnterButtonChat(event)" />
				<button class="chatBarInputButton" onclick="sendChatMessage()">Send</button>
				<button onclick="toggleFileshare()"><i class="las la-file-upload"></i></button>
			</div>
			<div class="resizer"></div>
		</div>
		<div id="activeShares"></div>
		<div id="controlButtons" class="hidden">
			<div class="controlPositioning">
                <div id="subControlButtons">
					<div id="unmuteSelf" title="Hear yourself at 50% volume" alt="Hear yourself at 50% volume" aria-label="Hear Yourself" onmousedown="event.preventDefault(); event.stopPropagation();"  onclick="toggleDirectFeedback()"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="hidden float" style="cursor: pointer;" >
                        <i id="unmuteSelftoggle" class="toggleSize las la-podcast"></i>
                    </div>
					<div id="mediafileshare" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Select and stream a media file" aria-label="Stream a media file" alt="Stream a media file to others" onclick="getById('fileselector3').click();" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="float hidden" style="cursor: pointer;">
                        <i id="mediafilesharetoggle" onmousedown="event.preventDefault(); event.stopPropagation();"  class="toggleSize las la-file-video"></i>
						<input id="fileselector3" class="hidden" onchange="session.changePublishFile(this,event);" type="file" accept="video/*,audio/*" alt="Hold CTRL (or CMD) to select multiple files" title="Hold CTRL (or CMD) to select multiple files" multiple/>
                    </div>
				
                    <div id="blindAllGuests" title="Blind all guests in room (toggle)" alt="Blind all guests in room (toggle)" aria-label="Blind all guests in room" onmousedown="event.preventDefault(); event.stopPropagation();"  onclick="blindAllGuests(this, event)"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="hidden float" style="cursor: pointer;" >
                        <i class="toggleSize las la-eye"></i>
                    </div>
                
                    <div id="queuebutton" title="Load the next guest in queue" alt="Load the next guest in queue"  aria-label="Load next guest in queue" onmousedown="event.preventDefault(); event.stopPropagation();"  onclick="nextQueue()"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="hidden float" style="cursor: pointer;" >
                        <i id="queuetoggle" class="toggleSize las la-stream"></i>
                        <div id="queueNotification"></div>
                    </div>
                    <div id="sharefilebutton" title="Transfer any file to the group" alt="Transfer any file to the group" aria-label="Select file to transfer" onmousedown="event.preventDefault(); event.stopPropagation();"  onclick="toggleFileshare()"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="float hidden" style="cursor: pointer;" >
                        <i id="filesharetoggle" class="toggleSize las la-file-upload"></i> 
                        <div id="transferNotification"></div>
                    </div>
                    <div id="chatbutton" title="Toggle the Chat" alt="Toggle the Chat" aria-label="Text chat" onmousedown="event.preventDefault(); event.stopPropagation();"  onclick="toggleChat()"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="hidden float" style="cursor: pointer;" >
                        <i id="chattoggle" class="toggleSize las la-comment-alt"></i>
                        <div id="chatNotification"></div>
                    </div>
                    <div id="mutespeakerbutton" onmousedown="event.preventDefault(); event.stopPropagation();" alt="Toggle the speaker output." aria-label="Mute Speaker output" title="Mute the Speaker (ALT + A)" onclick="toggleSpeakerMute()"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="hidden float" style="cursor: pointer;" >
                        <i id="mutespeakertoggle" class="toggleSize las la-volume-up" style="position: relative; top: 0.5px;"></i>
                    </div>
                    <div id="mutebutton" onmousedown="toggleMute(false, event);event.preventDefault(); event.stopPropagation();" data-translate="mute-the-mic" title="Mute the Mic (CTRL/⌘ + M)" alt="Mute the Mic" aria-label="Mute Microphone" ontouchstart="toggleMute(false, event);event.preventDefault(); event.stopPropagation();" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="hidden float" style="cursor: pointer;">
                        <i id="mutetoggle" class="toggleSize las la-microphone" style="position: relative; top: 0.5px;"></i>
                    </div>
                    <div id="mutevideobutton" onmousedown="event.preventDefault(); event.stopPropagation();" data-translate="disable-the-camera" title="Disable the Camera (CTRL/⌘ + B)" alt="Disable the Camera" aria-label="Mute Camera" onclick="toggleVideoMute()"   tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="hidden float" style="cursor: pointer;">
                        <i id="mutevideotoggle" onmousedown="event.preventDefault(); event.stopPropagation();"  class="toggleSize las la-video"></i>
                    </div>
                    <div id="screensharebutton" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Share a Screen with others" alt="Share a Screen with others" aria-label="Share a screen" onclick="screenshareTypeDecider(1)" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="float hidden task" data-menu="context-menu-screen-share" style="cursor: pointer;">
                        <i id="screensharetoggle" onmousedown="event.preventDefault(); event.stopPropagation();"  class="toggleSize las la-desktop"></i>
                    </div>
                    <div id="screenshare2button" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Add a Screen Share" alt="Add a Screen Share" aria-label="Share a screen"  onclick="screenshareTypeDecider(2)" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="float hidden task" data-menu="context-menu-screen-share"  style="cursor: pointer;">
                        <i id="screenshare2toggle" onmousedown="event.preventDefault(); event.stopPropagation();"  class="toggleSize las  la-tv"></i>
                    </div>
                    <div id="screenshare3button" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Share a Screen with others" alt="Add a Screen Share" aria-label="Share a screen"  onclick="screenshareTypeDecider(3)" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="float hidden task" data-menu="context-menu-screen-share"  style="cursor: pointer;">
                        <i id="screenshare3toggle" onmousedown="event.preventDefault(); event.stopPropagation();"  class="toggleSize las  la-tv"></i>
                    </div>
                    <div id="websitesharebutton" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Share a website with your guests (IFRAME)" aria-label="Share a website" alt="Share a website with your guests (IFRAME)" onclick="shareWebsite(false, event)" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="float hidden" style="cursor: pointer;">
                        <i id="websitesharetoggle" onmousedown="event.preventDefault(); event.stopPropagation();"  class="toggleSize las la-window-maximize"></i>
                    </div>
                    <div id="websitesharebutton2" onmousedown="event.preventDefault(); event.stopPropagation();" title="Hold CTRL (or CMD) and click to spotlight this video" alt="Share a website as an embedded iFRAME" aria-label="Share a website" onclick="shareWebsite(false, event)" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" class="float2 orange shake hidden" style="cursor: pointer;max-width: 200px;margin: auto;padding: 0 10px;">
                        <i onmousedown="event.preventDefault(); event.stopPropagation();" class="toggleSize las la-window-close" style="display: inline-block;"></i>
                        <div style="display: inline-block;width: 85px;line-height: 1; font-size: 0.9em; background-color: unset;box-shadow: unset;">
                            Stop Sharing Website
                        </div>
                    </div>
                    <div id="fullscreenPage" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Full-screen the page" alt="Full-screen the page" aria-label="Full screen"  onclick="fullscreenPageToggle()" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="float hidden" style="cursor: pointer;">
                        <i id="fullscreenPageToggle" onmousedown="event.preventDefault(); event.stopPropagation();"  class="toggleSize las la-expand-arrows-alt"></i>
                    </div>
					<div id="PictureInPicturePage" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Picture-in-Picture the video mix" alt="Picture-in-Picture the page" aria-label="Picture-in-Picture"  onclick="PictureInPicturePageToggle()" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="float hidden" style="cursor: pointer;">
                        <i id="PictureInPicturePageToggle" onmousedown="event.preventDefault(); event.stopPropagation();"  class="toggleSize las la-external-link-square-alt"></i>
                    </div>
                    <div id="flipcamerabutton" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Cycle the Cameras"  onclick="cycleCameras()" class="hidden float"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" style="cursor: pointer;" aria-label="Cycle Cameras" alt="Cycle the Cameras">
                        <i id="settingstoggle" class="toggleSize las la-sync-alt"></i> 
                    </div>
					
					 <div id="blackoutmode" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Enter black-out mode"  onclick="blackoutMode()" class="float hidden"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" style="cursor: pointer;" aria-label="Black out mode" alt="Enter black-out mode">
                        <i id="blackouttoggle" class="toggleSize las la-moon"></i> 
                    </div>
					
                    <div id="obscontrolbutton" onmousedown="event.preventDefault(); event.stopPropagation();"  title="OBS Remote Controller; start/stop and change scenes." onclick="toggleOBSControls();" class="hidden float"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" style="cursor: pointer;" aria-label="Remote OBS control menu" alt="Toggle the Remote OBS Controls Menu">
                        <i id="obscontroltoggle" class="toggleSize las la-gamepad"></i>
                    </div>
                    
                    <div id="roomsettingsbutton" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Room Settings" onclick="toggleRoomSettings();" class="hidden float"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" style="cursor: pointer;" alt="Toggle the Room Settings Menu" aria-label="Room settings menu">
                        <i id="roomsettingstoggle" class="toggleSize las la-users-cog"></i>
                    </div>
                    
                    <div id="settingsbutton" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Your audio and video Settings"  onclick="toggleSettings()" class="hidden float"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" style="cursor: pointer;" alt="Toggle Settings Menu" aria-label="Settings menu">
                        <i id="settingstoggle" class="toggleSize las la-cog"></i>
                    </div>
                    
                    <div id="hangupbutton"  onmousedown="event.preventDefault(); event.stopPropagation();" title="Hangup the Call" aria-label="Hang up" alt="Hangup the Call" onclick="hangup()" class="hidden float"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" style="cursor: pointer;" >
                        <i class="toggleSize las la-phone rotate225" aria-hidden="true"></i>
                    </div>
                    <div id="raisehandbutton" onmousedown="event.preventDefault(); event.stopPropagation();"  data-raised="0" title="Alert the host you want to speak"  aria-label="Raise hand" alt="Alert the host you want to speak" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" onclick="raisehand()" class="hidden float" style="cursor: pointer;">
                        <i class="toggleSize las la-hand-paper" style="position: relative; right: 1px;" aria-hidden="true"></i>
                    </div>
                    
                    <div id="pptbackbutton" onmousedown="event.preventDefault(); event.stopPropagation();" title="Go back a slide" aria-label="Back a slide" alt="Go back a slide" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" onclick="gobackSlide()" class="hidden float red" style="cursor: pointer;">
                        <i class="toggleSize las la-chevron-left" style="position: relative; right: 1px;" aria-hidden="true"></i>
                    </div>
                    <div id="pptnextbutton" onmousedown="event.preventDefault(); event.stopPropagation();" title="Next slide" aria-label="Next slide" alt="Next slide" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" onclick="nextSlide()" class="hidden float" style="cursor: pointer;">
                        <i class="toggleSize las la-chevron-right" style="position: relative; right: 1px;" aria-hidden="true"></i>
                    </div>
                    
                    <div id="recordLocalbutton" onmousedown="event.preventDefault(); event.stopPropagation();"  data-state="0" title="Record your stream to disk" aria-label="Record your stream to disk" alt="Record your stream to disk" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" onclick="recordLocalVideoToggle();" class="hidden float" style="cursor: pointer;">
                        <i class="toggleSize las la-dot-circle" style="position: relative;" aria-hidden="true"></i>
                    </div>
                    <div id="recordLocalScreenbutton" onmousedown="event.preventDefault(); event.stopPropagation();"  data-state="0" title="Stop screen share recording" aria-label="Stop screen share recording" alt="Stop screen recording" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" onclick="recordLocalScreenStopRecord();" class="hidden float" style="cursor: pointer;">
                        <small>Stop<br>Screen<br>Record</small>
                    </div>
                    <span id="miniPerformer" style="pointer-events: auto;" class="hidden"></span>
                    <span id="rooms" class="hidden" style="padding-top:3px;padding-left:6px;pointer-events: auto;color:#fff;"></span>
                    <span id="groups" class="hidden" style="padding-top:3px;padding-left:6px;pointer-events: auto;color:#fff;text-align: center;"></span>
                    <div id="hangupbutton2" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Cancel the Director's Video/Audio"  onclick="hangup2()" class="hidden float"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" style="cursor: pointer;" aria-label="stop publishing audio and video" alt="Disconnect Direcotor's cam">
                        <i class="toggleSize las la-phone rotate225" aria-hidden="true"></i>
                    </div>
                </div>
            </div>
		</div>
    <div id="join-room-container" class="container">
        <h2>Join a Room</h2>
        <input type="text" id="roomName" placeholder="Enter Room Name">
        <br>
        <button onclick="joinRoom()">Join</button>
    </div>

    <div id="media-selection-container" class="container hidden">
        <h2>Camera and Microphone</h2>
        <div id="media-selection">
            <video id="preview" autoplay muted playsinline></video>
            <select id="video-source"></select>
            <select id="audio-source"></select>
            <select id="audio-output"></select>
        </div>
        <button onclick="start()">Start</button>
    </div>

    <div id="main-video-display" class="hidden">
        <!-- The main video stream will be displayed here -->
        <video id="mainStreamVideo" autoplay playsinline></video>
    </div>

    <script>
        // Initialize session object from webrtc.js
        var session = WebRTC.Media;

        let roomName = '';
        let password = '';

        // This function will be called after all scripts are loaded.
        // It will hide the default UI elements that main.js might create.
        function initializeAppUI() {
            // Hide elements that main.js might make visible
            

            elementsToHide.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.classList.add('hidden');
                    element.style.display = 'none'; // Ensure it's hidden
                }
            });

            // Ensure my initial UI is visible
            document.getElementById('join-room-container').classList.remove('hidden');
            document.getElementById('join-room-container').style.display = 'block';
        }

        // Call main() from main.js to initialize the session object and other global states.
        // This needs to be done after main.js is loaded.
        // We will call it after the DOM is ready.
        document.addEventListener('DOMContentLoaded', async () => {
            // Call main() from main.js to initialize the session object and other global states.
            // This is crucial for session.startStream() to work.
            // However, main() also builds UI. So we need to hide it immediately after.
            if (typeof main === 'function') {
                await main(); // Call the main function from main.js
            }
            initializeAppUI(); // Hide unwanted UI elements
        });


        async function joinRoom() {
            roomName = document.getElementById('roomName').value;
            if (!roomName) {
                alert('Please enter a room name.');
                return;
            }

            password = prompt('Enter a password if provided, otherwise just click Cancel') || '';

            document.getElementById('join-room-container').classList.add('hidden');
            document.getElementById('media-selection-container').classList.remove('hidden');
            
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true, video: true });
                stream.getTracks().forEach(track => track.stop());
                await populateDeviceSelectors();
            } catch (e) {
                alert('Could not access camera and microphone. Please allow permissions in your browser settings.');
                console.error(e);
            }
        }

        async function populateDeviceSelectors() {
            const videoSelect = document.getElementById('video-source');
            const audioSelect = document.getElementById('audio-source');
            const audioOutputSelect = document.getElementById('audio-output');
            
            videoSelect.innerHTML = '';
            audioSelect.innerHTML = '';
            audioOutputSelect.innerHTML = '';

            const devices = await navigator.mediaDevices.enumerateDevices();
            const videoDevices = devices.filter(device => device.kind === 'videoinput');
            const audioDevices = devices.filter(device => device.kind === 'audioinput');
            const audioOutputDevices = devices.filter(device => device.kind === 'audiooutput');

            if (videoDevices.length === 0) {
                videoSelect.innerHTML = '<option value="">No cameras found</option>';
            } else {
                 videoDevices.forEach(device => {
                    const option = document.createElement('option');
                    option.value = device.deviceId;
                    option.text = device.label || `Camera ${videoSelect.length + 1}`;
                    videoSelect.appendChild(option);
                });
            }

            if (audioDevices.length === 0) {
                audioSelect.innerHTML = '<option value="">No microphones found</option>';
            } else {
                audioDevices.forEach(device => {
                    const option = document.createElement('option');
                    option.value = device.deviceId;
                    option.text = device.label || `Microphone ${audioSelect.length + 1}`;
                    audioSelect.appendChild(option);
                });
            }

            if (audioOutputDevices.length === 0) {
                audioOutputSelect.innerHTML = '<option value="">No audio output found</option>';
            } else {
                audioOutputDevices.forEach(device => {
                    const option = document.createElement('option');
                    option.value = device.deviceId;
                    option.text = device.label || `Speaker ${audioOutputSelect.length + 1}`;
                    audioOutputSelect.appendChild(option);
                });
            }
           
            videoSelect.onchange = startPreview;
            audioSelect.onchange = startPreview;
            
            await startPreview();
        }

        async function startPreview() {
            const videoSelect = document.getElementById('video-source');
            const audioSelect = document.getElementById('audio-source');
            const preview = document.getElementById('preview');

            if (window.stream) {
                window.stream.getTracks().forEach(track => track.stop());
            }

            const videoSource = videoSelect.value;
            const audioSource = audioSelect.value;

            const constraints = {
                video: videoSource ? { deviceId: { exact: videoSource } } : false,
                audio: audioSource ? { deviceId: { exact: audioSource } } : false
            };

            if (!constraints.video && !constraints.audio) {
                 preview.srcObject = null;
                 return;
            }
            if (!constraints.video) delete constraints.video;
            if (!constraints.audio) delete constraints.audio;

            try {
                const stream = await navigator.mediaDevices.getUserMedia(constraints);
                window.stream = stream;
                preview.srcObject = stream;
            } catch (e) {
                console.error('Error starting preview:', e);
                preview.srcObject = null;
            }
        }

        async function start() {
            const videoSource = document.getElementById('video-source').value;
            const audioSource = document.getElementById('audio-source').value;
            const audioOutput = document.getElementById('audio-output').value;

            // Set session properties
            session.roomid = roomName;
            session.password = password;
            session.videoDevice = videoSource;
            session.audioDevice = audioSource;
            session.sinkId = audioOutput;
            session.webcamonly = true; // This flag is important for publishWebcam to work as expected

            // Hide media selection UI
            document.getElementById('media-selection-container').classList.add('hidden');
            // Show main video display
            document.getElementById('main-video-display').classList.remove('hidden');

            const mainStreamVideo = document.getElementById('mainStreamVideo');

            try {
                // Get the stream again with the selected devices for the main display
                const constraints = {
                    video: videoSource ? { deviceId: { exact: videoSource } } : false,
                    audio: audioSource ? { deviceId: { exact: audioSource } } : false
                };

                if (!constraints.video && !constraints.audio) {
                    alert('No media devices selected to start stream.');
                    return;
                }
                if (!constraints.video) delete constraints.video;
                if (!constraints.audio) delete constraints.audio;

                const stream = await navigator.mediaDevices.getUserMedia(constraints);
                mainStreamVideo.srcObject = stream; // Display the stream in the main video element

                // Set the local stream in the session object
                session.localStream = stream;

                // Call session.startStream() to initiate the WebRTC connection and publish the stream
                session.startStream(stream);

            } catch (e) {
                console.error('Error starting main stream:', e);
                alert('Error starting main stream. Check console for details.');
            }
        }
    </script>
    <script type="text/javascript" crossorigin="anonymous" id="lib-js" src="./lib.js?ver=1294"></script>
		<script type="text/javascript" crossorigin="anonymous" id="main-js" src="./main.js?ver=954"></script>
</body>
</html>