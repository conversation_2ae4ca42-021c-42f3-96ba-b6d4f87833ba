{"titles": {"100": "100", "enter-a-room-name-to-quick-join": "Enter a room name to quick join", "join-room": "Join room", "enter-the-url-to-load": "Enter the URL to load", "load-url": "Load URL", "number-of-outbound-connections": "Number of outbound connections", "number-of-outbound-audio-streams": "Number of outbound audio streams", "number-of-outbound-video-streams": "Number of outbound video streams", "number-of-scenes-": "Number of scenes.", "total-upload-bitrate": "Total upload bitrate", "copy-link-to-clipboard": "Copy link to clipboard", "save-and-ask-to-reload-the-current-page-on-next-site-visit": "Save and ask to reload the current page on next site visit", "will-remember-the-room-prompting-you-the-next-time-you-visit-if-you-wish-to-load-this-director-s-room-again": "Will remember the room, prompting you the next time you visit if you wish to load this director's room again", "toggle-between-the-director-control-room-view-and-a-scene-preview-mode-": "Toggle between the director control-room view and a scene preview-mode.", "stream-a-media-file": "Stream a media file", "hold-ctrl-or-cmd-to-select-multiple-files": "Hold CTRL (or CMD) to select multiple files", "blind-all-guests-in-room-toggle-": "Blind all guests in room (toggle)", "load-the-next-guest-in-queue": "<PERSON><PERSON> the next guest in queue", "transfer-any-file-to-the-group": "Transfer any file to the group", "toggle-the-chat": "Toggle the Chat", "mute-the-speaker-alt-a-": "Mute the Speaker (ALT + A)", "mute-the-mic-ctrl-m-": "Mute the Mic (CTRL/⌘ + M)", "disable-the-camera-ctrl-b-": "Disable the Camera (CTRL/⌘ + B)", "share-a-screen-with-others": "Share a Screen with others", "add-a-screen-share": "Add a Screen Share", "share-a-website-with-your-guests-iframe-": "Share a website with your guests (IFRAME)", "hold-ctrl-or-cmd-and-click-to-spotlight-this-video": "Hold CTRL (or CMD) and click to spotlight this video", "full-screen-the-page": "Full-screen the page", "picture-in-picture-the-video-mix": "Picture-in-Picture the video mix", "cycle-the-cameras": "Cycle the Cameras", "obs-remote-controller-start-stop-and-change-scenes-": "OBS Remote Controller; start/stop and change scenes.", "room-settings": "Room Settings", "your-audio-and-video-settings": "Your audio and video Settings", "hangup-the-call": "Hangup the Call", "alert-the-host-you-want-to-speak": "Alert the host you want to speak", "go-back-a-slide": "Go back a slide", "next-slide": "Next slide", "record-your-stream-to-disk": "Record your stream to disk", "stop-screen-share-recording": "Stop screen share recording", "this-is-the-preview-of-the-director-s-audio-and-video-output-": "This is the preview of the Director's audio and video output.", "cancel-the-director-s-video-audio": "Cancel the Director's Video/Audio", "submit-any-error-logs": "Submit any error logs", "show-help-contact-info": "Show help contact info", "language-options": "Language Options", "add-to-calendar": "Add to Calendar", "youtube-video-demoing-how-to-do-this": "Youtube Video demoing how to do this", "invite-a-guest-or-camera-source-to-publish-into-the-group-room": "Invite a guest or camera source to publish into the group room", "if-disabled-the-invited-guest-will-not-be-able-to-see-or-hear-anyone-in-the-room-": "If disabled, the invited guest will not be able to see or hear anyone in the room.", "use-this-link-in-the-obs-browser-source-to-capture-the-video-or-audio": "Use this link as a Browser Source to capture the video or audio", "if-disabled-you-must-manually-add-a-video-to-a-scene-for-it-to-appear-": "If disabled, you must manually add a video to a scene for it to appear.", "disables-echo-cancellation-and-improves-audio-quality": "Disables Echo Cancellation and improves audio quality", "audio-only-sources-are-visually-hidden-from-scenes": "Audio-only sources are visually hidden from scenes", "allow-for-remote-stat-monitoring-via-the-monitoring-tool": "Allow for remote stat monitoring via the monitoring tool", "the-guest-will-be-asked-if-they-want-to-reload-the-previous-link-when-revisiting": "The guest will be asked if they want to reload the previous link when revisiting", "guest-will-be-prompted-to-enter-a-display-name": "Guest will be prompted to enter a Display Name", "display-names-will-be-shown-in-the-bottom-left-corner-of-videos": "Display Names will be shown in the bottom-left corner of videos", "guests-not-actively-speaking-will-be-hidden": "Guests not actively speaking will be hidden", "show-a-custom-welcome-message-to-the-joining-guest-of-this-invite-link": "Show a custom welcome message to the joining guest of this invite link", "request-1080p60-from-the-guest-instead-of-720p60-if-possible": "Request 1080p60 from the <PERSON> instead of 720p60, if possible", "the-default-microphone-will-be-pre-selected-for-the-guest": "The default microphone will be pre-selected for the guest", "the-default-camera-device-will-selected-automatically": "The default camera device will selected automatically", "the-camera-will-load-in-a-default-safe-mode-that-may-work-if-other-modes-fail-": "The camera will load in a default safe-mode that may work if other modes fail.", "the-guest-won-t-have-access-to-changing-camera-settings-or-screenshare": "The guest won't have access to changing camera settings or screenshare", "allow-the-guests-to-pick-a-virtual-backscreen-effect": "Allow the guests to pick a virtual backscreen effect", "disable-animated-transitions-during-video-mixing": "Disable animated transitions during video mixing", "this-mode-encodes-the-video-and-audio-into-chunks-which-are-shared-with-multiple-viewers-limited-browser-support-can-potentially-reduce-cpu-and-improve-video-quality-but-will-rely-on-a-buffer-": "This mode encodes the video and audio into chunks, which are shared with multiple viewers. Limited browser support. Can potentially reduce CPU and improve video quality, but will rely on a buffer.", "increase-video-quality-that-guests-in-room-see-": "Increase video quality that guests in room see.", "the-guest-will-not-see-their-own-self-preview-after-joining": "The guest will not see their own self-preview after joining", "guests-will-have-an-option-to-poke-the-director-by-pressing-a-button": "Guests will have an option to poke the Director by pressing a button", "add-an-audio-compressor-to-the-guest-s-microphone": "Add an audio compressor to the guest's microphone", "add-an-equalizer-to-the-guest-s-microphone-that-the-director-can-control": "Add an Equalizer to the guest's microphone that the director can control", "show-some-prep-suggestions-to-the-guests-on-connect": "Show some prep suggestions to the guests on connect", "have-screen-shares-stream-id-s-use-a-predictable-prefixed-value-instead-of-a-random-one-": "Have screen-shares stream ID's use a predictable prefixed value instead of a random one.", "allow-the-guest-to-select-an-avatar-image-for-when-they-hide-their-camera": "Allow the guest to select an avatar image for when they hide their camera", "use-meshcast-servers-to-restream-video-data-from-this-guest-to-its-viewers-reducing-the-cpu-and-upload-load-in-some-cases-will-increase-latency-a-bit-": "Use Meshcast servers to restream video data from this guest to its viewers, reducing the CPU and upload load in some cases. Will increase latency a bit.", "the-guest-s-self-video-preview-will-appear-tiny-in-the-top-right": "The guest's self-video preview will appear tiny in the top right", "show-an-ovelaid-grid-on-the-guest-s-preview-video-to-help-with-self-centering-of-the-guest-": "Show an ovelaid grid on the guest's preview video to help with self-centering of the guest.", "the-guest-can-only-see-the-director-s-video-if-provided": "The guest can only see the Director's video, if provided", "the-guest-s-microphone-will-be-muted-on-joining-they-can-unmute-themselves-": "The guest's microphone will be muted on joining. They can unmute themselves.", "have-the-guest-join-muted-so-only-the-director-can-unmute-the-guest-": "Have the guest join muted, so only the director can Unmute the guest.", "the-guest-will-not-be-asked-for-a-video-device-on-connection": "The guest will not be asked for a video device on connection", "make-the-invite-url-encoded-so-parameters-are-harder-to-tinker-with-by-guests-this-also-debrands-the-interface-and-gives-it-a-new-domain-name-": "Make the invite URL encoded, so parameters are harder to tinker with by guests. This also debrands the interface and gives it a new domain name.", "the-active-speakers-are-made-visible-automatically": "The active speakers are made visible automatically", "set-the-background-color-to-bright-green": "Set the background color to bright green", "fade-videos-in-over-500ms": "Fade videos in over 500ms", "videos-use-an-animated-transition-when-being-remixed": "Videos use an animated transition when being remixed", "add-a-10px-margin-around-all-video-elements": "Add a 10px margin around all video elements", "disable-fit-to-window-optmized-video-scaling-for-added-sharpness-increases-cpu-network-load-though-": "Disable fit-to-window optmized video scaling for added sharpness; increases CPU / Network load though.", "playback-the-video-with-mono-channel-audio": "Playback the video with mono-channel audio", "have-the-videos-fit-their-respective-areas-even-if-it-means-cropping-a-bit": "Have the videos fit their respective areas, even if it means cropping a bit", "have-videos-be-aligned-with-sizing-designed-for-vertical-video": "Have videos be aligned with sizing designed for vertical video", "does-not-impact-scene-order-": "Does not impact scene order.", "copy-this-stream-id-to-the-clipboard": "Copy this Stream ID to the clipboard", "minimize-this-control-box": "Minimize this control box", "click-here-to-edit-the-label-for-this-stream-changes-will-propagate-to-all-viewers-of-this-stream": "Click here to edit the label for this stream. Changes will propagate to all viewers of this stream", "video-packet-loss-indicator-of-video-preview-green-is-good-red-is-bad-flame-implies-cpu-is-overloaded-may-not-reflect-the-packet-loss-seen-by-scenes-or-other-guests-": "Video packet loss indicator of video preview; green is good, red is bad. Flame implies CPU is overloaded. May not reflect the packet loss seen by scenes or other guests.", "100-charging": "100% charging", "hold-ctrl-or-cmd-while-clicking-the-video-to-open-detailed-stats": "Hold CTRL or CMD (⌘) while clicking the video to open detailed stats", "remotely-change-the-volume-of-this-guest-updates-on-release-dbl-click-to-reset-": "Remotely change the volume of this guest; updates on release. Dbl-click to reset.", "mute-this-guest-everywhere": "Mute this guest everywhere", "disable-video-preview": "Disable Video Preview", "low-quality-preview": "Low-Quality Preview", "high-quality-preview": "High-Quality Preview", "send-a-direct-message-to-this-user-": "Send a Direct Message to this user.", "toggle-between-the-message-appearing-as-a-large-overlay-and-as-normal-chat": "Toggle between the message appearing as a large overlay and as normal chat", "move-the-user-to-another-room-controlled-by-another-director": "Move the user to another room, controlled by another director", "force-the-user-to-disconnect-they-can-always-reconnect-": "Force the user to Disconnect. They can always reconnect.", "toggle-solo-voice-chat-or-hold-ctrl-cmd-when-selecting-to-make-it-two-way-private-": "Toggle solo voice chat or hold CTRL/CMD when selecting to make it two-way private.", "solo-this-video-everywhere-hold-ctrl-cmd-to-just-make-video-larger-": "Solo this video everywhere. (Hold CTRL/CMD to just make video larger)", "disable-this-guest-s-video-track": "Disable this guest's video track", "toggle-the-remote-guest-s-speaker-output": "Toggle the remote guest's speaker output", "hide-this-guest-everywhere": "Hide this guest everywhere", "toggle-the-remote-guest-s-display-output": "Toggle the remote guest's display output", "add-this-video-to-any-remote-scene-1-": "Add this Video to any remote '&scene=1'", "add-this-video-to-any-remote-scene-2-": "Add this Video to any remote '&scene=2'", "remotely-mute-this-audio-in-all-remote-scene-views": "Remotely Mute this Audio in all remote '&scene' views", "add-to-scene-3": "Add to Scene 3", "add-to-scene-4": "Add to Scene 4", "add-to-scene-5": "Add to Scene 5", "add-to-scene-6": "Add to Scene 6", "add-to-scene-7": "Add to Scene 7", "add-to-scene-8": "Add to Scene 8", "request-the-statistics-of-this-video-in-any-active-scene": "Request the statistics of this video in any active scene", "shift-this-video-down-in-order": "Shift this Video Down in Order", "current-index-order-of-this-video": "Current Index Order of this Video", "shift-this-video-up-in-order": "Shift this Video Up in Order", "set-a-countdown-timer-that-this-guest-sees-ctrl-cmd-click-to-pause-": "Set a countdown timer that this guest sees. CTRL (cmd) + click to pause.", "start-recording-this-remote-stream-to-this-local-drive-experimental-": "Start Recording this remote stream to this local drive. *experimental*'", "the-remote-guest-will-record-their-local-stream-to-their-local-drive-experimental-": "The Remote Guest will record their local stream to their local drive. *experimental*", "change-user-parameters": "Change user parameters", "remotely-reload-the-guest-s-page-with-a-new-url": "Remotely reload the guest's page with a new URL", "allow-the-guest-to-select-a-file-to-upload-to-the-director-once-shared-it-will-show-in-the-chat-as-a-download-link-": "Allow the guest to select a file to upload to the director. Once shared, it will show in the chat as a download link.", "mirror-the-video-of-this-guest-globally": "Mirror the video of this guest globally", "force-the-remote-sender-to-issue-a-keyframe-to-all-scenes-fixing-pixel-smearing-issues-": "Force the remote sender to issue a keyframe to all scenes, fixing Pixel Smearing issues.", "set-to-audio-channel-1": "Set to Audio Channel 1", "set-to-audio-channel-2": "Set to Audio Channel 2", "set-to-audio-channel-3": "Set to Audio Channel 3", "set-to-audio-channel-4": "Set to Audio Channel 4", "set-to-audio-channel-5": "Set to Audio Channel 5", "set-to-audio-channel-6": "Set to Audio Channel 6", "add-remove-from-group-1": "Add/remove from group 1", "add-remove-from-group-2": "Add/remove from group 2", "add-remove-from-group-3": "Add/remove from group 3", "add-remove-from-group-4": "Add/remove from group 4", "add-remove-from-group-5": "Add/remove from group 5", "add-remove-from-group-6": "Add/remove from group 6", "remote-audio-settings": "Remote Audio Settings", "advanced-video-settings": "Advanced Video Settings", "previously-was-0": "Previously was: 0", "this-will-refresh-the-current-device": "This will refresh the current device", "this-will-ask-the-remote-guest-for-permission-to-change": "This will ask the remote guest for permission to change", "a-direct-solo-view-of-the-video-audio-stream-with-nothing-else-its-audio-can-be-remotely-controlled-from-here": "A direct solo view of the video/audio stream with nothing else. Its audio can be remotely controlled from here", "this-guest-raised-their-hand-click-this-to-clear-notification-": "This guest raised their hand. Click this to clear notification.", "takes-the-guest-out-of-queue-mode-they-will-then-join-as-a-normal-guest-": "Takes the guest out of queue mode; they will then join as a normal guest.", "add-to-scene-2": "Add to Scene 2", "activate-or-reload-this-video-device-": "Activate or Reload this video device.", "tip-hold-ctrl-command-to-select-multiple": "tip: Hold CTRL (command) to select Multiple", "experimental": "experimental", "face-detection-api-not-detected-you-may-be-able-to-enable-it-here-chrome-flags-enable-experimental-web-platform-features": "Face Detection API not detected; you may be able to enable it here: chrome://flags/#enable-experimental-web-platform-features", "improve-performance-and-quality-with-this-tip": "Improve performance and quality with this tip", "adjust-the-amount-of-effect-applied": "Adjust the amount of effect applied", "the-solo-view-link-of-the-director-s-video-": "The solo view link of the Director's video.", "this-will-reduce-the-gain-80-when-there-is-no-one-talking-loudly": "This will reduce the gain ~80% when there is no one talking loudly", "only-one-option-available-so-can-t-be-changed": "Only one option available, so can't be changed", "previously-was-1-7777777777777777": "Previously was:  1.7777777777777777", "previously-was-1280": "Previously was:  1280", "hold-ctrl-or-cmd-to-lock-width-and-height-together-when-changing-them": "Hold CTRL (or cmd) to lock width and height together when changing them", "previously-was-720": "Previously was:  720", "previously-was-30-000030517578125": "Previously was:  30.000030517578125", "choose-a-hotkey-for-hold-to-talk-if-using-electron-capture-elevate-privilleges-to-have-it-become-global": "Choose a hotkey for Hold-to-Talk. If using Electron Capture, elevate privilleges to have it become global", "draw-on-the-screen": "Draw on the Screen", "ctrl-cmd-alt-d-to-toggle": "CTRL (cmd) + ALT + D to toggle", "audio-only-sources-can-be-stylized-in-different-ways": "Audio-only sources can be stylized in different ways", "clear-site-s-local-storage-and-settings": "Clear site's local storage and settings", "using-this-may-cause-audio-issues-on-some-systems": "Using this may cause audio-issues on some systems", "increase-this-at-your-peril-changes-the-total-inbound-video-bitrate-per-guest-mobile-devices-excluded-": "Increase this at your peril. Changes the total inbound video bitrate per guest; mobile devices excluded.", "sets-your-max-total-allowed-upload-bandwidth-across-all-connections": "Sets YOUR max total allowed upload bandwidth; across all connections", "only-the-director-s-video-will-be-visible-to-guests-and-within-group-scenes-hold-ctrl-cmd-to-just-make-video-larger-": "Only the director's video will be visible to guests and within group scenes. (Hold CTRL/CMD to just make video larger)", "set-a-count-up-timer-that-this-guest-sees-ctrl-cmd-click-to-pause-": "Set a count-up timer that this guest sees. CTRL (cmd) + click to pause.", "allow-for-remote-co-directors": "Allow for remote co-directors", "record-all-the-guests": "Record all the guests", "which-audio-bitrate-target-would-you-prefer-": "Which audio bitrate target would you prefer?", "constant-audio-bitrate-or-variable-audio-bitrate": "Constant audio bitrate or variable audio bitrate", "remove-background-noise-but-may-limit-audio-quality": "Remove background noise, but may limit audio quality", "let-the-browser-control-the-mic-s-gain-automatically": "Let the browser control the mic's gain automatically", "stereo-audio-or-mono-must-have-denoise-off-for-this-to-work-": "Stereo audio or mono; must have denoise off for this to work.", "which-video-bitrate-target-would-you-prefer-": "Which video bitrate target would you prefer?", "which-video-codec-would-you-prefer-to-be-used-if-available-": "Which video codec would you prefer to be used if available?", "cannot-see-videos": "Cannot see videos", "cannot-hear-others": "Cannot hear others", "see-director-only": "See director only", "show-mini-preview": "Show Mini preview", "raise-hand-button": "Raise hand button", "show-labels": "Show labels", "transfer-to-a-new-room": "Transfer to a new Room", "enable-custom-password": "Enable custom password", "hide-this-window": "Hide this window", "reload-the-page": "Reload the page", "select-a-location-that-is-closest-to-both-you-and-your-audience-": "Select a location that is closest to both you and your audience.", "add-group-chat-to-obs": "Add Group Chat", "generate-a-random-room-name": "Generate a random room name", "for-large-group-rooms-this-option-can-reduce-the-load-on-remote-guests-substantially": "For large group rooms, this option can reduce the load on remote guests substantially", "the-director-will-be-visible-in-scenes-as-if-a-performer-themselves-": "The director will be visible in scenes, as if a performer themselves.", "if-checked-the-director-can-be-added-to-scenes-as-if-a-guest-otherwise-the-director-will-never-appear-in-a-scene-": "If checked, the director can be added to scenes as if a guest. Otherwise, the director will never appear in a scene.", "which-video-codec-would-you-want-used-by-default-": "Which video codec would you want used by default?", "you-ll-enter-as-the-room-s-director": "You'll enter as the room's director", "add-your-camera-to-obs": "Add your Camera", "right-click-this-video-for-additional-options": "Right-click this video for additional options", "start-streaming-alt-s-": "Start streaming (Alt + s)", "video-source-list": "Video source list", "play-a-sound-out-of-the-selected-audio-playback-device": "Play a sound out of the selected audio playback device", "enable-the-chrome-experimental-features-flag-to-use-chrome-flags-enable-experimental-web-platform-features": "Enable the Chrome experimental features flag to use: chrome://flags/#enable-experimental-web-platform-features", "add-an-optional-password": "Add an optional password", "enter-an-optional-password-here": "Enter an optional password here", "remember-and-reuse-the-provided-stream-id-on-each-visit": "Remember and reuse the provided stream ID on each visit", "consider-using-chrome-instead-of-safari": "Consider using Chrome instead of Safari", "please-update-your-version-of-ios-for-best-performance": "Please update your version of iOS for best performance", "go-back": "Go back", "add-your-microphone-to-obs": "Add your Microphone to OBS", "remote-screenshare-into-obs": "Remote Screenshare", "create-reusable-invite": "Create Reusable Invite", "ideal-for-1080p60-gaming-if-your-computer-and-upload-are-up-for-it": "Ideal for 1080p60 gaming, if your computer and upload are up for it", "better-video-compression-and-quality-at-the-cost-of-increased-cpu-encoding-load": "Better video compression and quality at the cost of increased CPU encoding load", "disable-digital-audio-effects-and-increase-audio-bitrate": "Disable digital audio-effects and increase audio bitrate", "the-guest-will-be-able-to-select-digital-video-effects-to-apply-": "The guest will be able to select digital video effects to apply.", "the-guest-will-not-have-a-choice-over-audio-options": "The guest will not have a choice over audio-options", "the-guest-will-only-be-able-to-select-their-webcam-as-an-option": "The guest will only be able to select their webcam as an option", "encode-the-url-so-that-it-s-harder-for-a-guest-to-modify-the-settings-": "Encode the URL so that it's harder for a guest to modify the settings.", "add-a-password-to-make-the-stream-inaccessible-to-those-without-the-password": "Add a password to make the stream inaccessible to those without the password", "a-link-for-the-host-speaker-to-chat-with-the-guest-2-way-interview-chat-": "A link for the host speaker to chat with the guest; 2-way interview chat.", "add-the-guest-to-a-group-chat-room-it-will-be-created-automatically-if-needed-": "Add the guest to a group-chat room; it will be created automatically if needed.", "customize-the-room-settings-for-this-guest": "Customize the room settings for this guest", "more-options": "More Options", "transfer-any-file": "Transfer any file", "enter-an-https-url": "Enter an HTTPS URL", "open-a-youtube-video-demoing-the-basics-of-vdo-ninja": "Open a YouTube video demoing the basics of VDO.Ninja", "for-a-list-of-common-or-known-issues-click-here": "For a list of common or known issues, click here", "click-to-link-out-to-the-vdo-ninja-help-guide-for-common-obs-studio-problems": "Click to link out to the VDO.Ninja help guide for common OBS Studio problems", "open-a-page-with-recent-vdo-ninja-development-and-feature-updates": "Open a page with recent VDO.Ninja development and feature updates", "info-on-the-native-app-versions-of-vdo-ninja": "Info on the native app versions of VDO.Ninja", "mute-the-speaker": "Mute the Speaker", "mute-the-mic": "Mute the Mic", "disable-the-camera": "Disable the Camera", "show-help-info": "Show Help Info", "previously-was-0-5625": "Previously was:  0.5625", "join-by-room-name-here": "Enter a room name to quick join", "create-a-secondary-stream": "Create a Secondary Stream", "share-a-website-as-an-embedded-iframe": "Share a website as an embedded iFRAME", "you-can-also-enable-the-director-s-video-output-afterwards-by-clicking-the-setting-s-button": "You can also enable the director`s Video Output afterwards by clicking the Setting`s button", "this-low-fi-video-codec-uses-very-little-cpu-even-with-dozens-of-active-viewers-": "This low-fi video codec uses very little CPU, even with dozens of active viewers.", "make-the-invite-url-encoded-so-parameters-are-harder-to-tinker-with-by-guests": "Make the invite URL encoded, so parameters are harder to tinker with by guests", "toggle-solo-voice-chat": "Toggle Solo Voice Chat", "solo-this-video-everywhere": "Solo this video everywhere", "remotely-change-the-volume-of-this-guest": "Remotely change the volume of this guest", "increase-this-at-your-peril-changes-the-total-inbound-video-bitrate-per-guest-mobile-devices-excluded-webp-mode-also-excluded-": "Increase this at your peril. Changes the total inbound video bitrate per guest; mobile devices excluded. Webp-mode also excluded.", "useful-if-you-want-to-perform-and-direct-at-the-same-time": "Useful if you want to perform and direct at the same time", "start-streaming": "start streaming", "hold-ctrl-and-the-mouse-wheel-to-zoom-in-and-out-remotely-of-compatible-video-streams": "Hold CTRL and the mouse wheel to zoom in and out remotely of compatible video streams", "creative-commons-by-3-0": "Creative Commons BY 3.0", "set-a-countdown-timer-that-this-guest-sees": "Set a countdown timer that this guest sees", "create-a-third-stream": "Create a Third Stream", "add-group-chat": "Add Group Chat", "add-your-camera": "Add your Camera", "remote-screenshare": "Remote Screenshare", "use-this-link-as-a-browser-source-to-capture-the-video-or-audio": "Use this link as a Browser Source to capture the video or audio", "only-the-director-s-video-will-be-visible-to-guests-and-within-group-scenes": "Only the director's video will be visible to guests and within group scenes", "jump-to-the-documentation": "Jump to the documentation", "100-battery-remaining": "100% battery remaining", "for-more-known-issues-click-here": "For more known issues, click here", "previously-was-29-970029830932617": "Previously was:  29.970029830932617", "link-out-to-the-vdo-ninja-help-guide-for-obs-studio": "link out to the help guide", "will-slowly-pan-tilt-and-zoom-in-on-the-first-face-detected": "Will slowly pan, tilt, and zoom in on the first face detected", "load-a-website-url": "Enter the URL to load", "optional": "Enter an optional password here"}, "innerHTML": {"logo-header": "<font id=\"qos\">V</font>DO.Ninja ", "copy-this-url": "Copy this URL into your \"browser source\"", "you-are-in-the-control-center": "Control center for room:", "joining-room": "You are in room", "only-director-can-hear-you": "Only the director can hear you currently.", "director-muted-you": "The director has muted you.", "director-video-muted-you": "The director has disabled your camera temporarily.", "welcome-to-vdo-ninja-chat": "Welcome! You can send text messages directly to connected peers from here.", "send-chat": "Send", "upload-chat": "<i class=\"las la-file-upload\"></i> Upload File", "mute-the-mic": "\n<i id=\"mutetoggle\" class=\"toggleSize las la-microphone\" style=\"position: relative; top: 0.5px;\"></i>\n", "disable-the-camera": "\n<i id=\"mutevideotoggle\" onmousedown=\"event.preventDefault(); event.stopPropagation();\" class=\"toggleSize las la-video\"></i>\n", "hide-the-links": " LINKS (GUEST INVITES &amp; SCENES)", "click-here-for-help": "Click Here for a quick overview and help", "welcome-to-control-room": "<b>Welcome. This is the director's control-room for the group-chat.</b><br><br>You can host a group chat with friends using a room. Share the blue link to invite guests who will join the chat automatically.<br><br>A group room can handle normally around 6 to 20 guests, depending on numerous factors, including CPU and available bandwidth of all guests in the room", "invite-a-guest": "INVITE A GUEST", "invite-users-to-join": "Guests can use the link to join the group room", "guests-hear-others": "Guests hear others", "copy-link": "Copy link", "customize": "Customize", "capture-a-group-scene": "CAPTURE A GROUP SCENE", "this-is-obs-browser-source-link": "Use studio software to capture the group video mix", "auto-add-guests": "Auto-add guests", "pro-audio-mode": "Pro-audio mode", "hide-audio-only-sources": "Hide audio-only sources", "remote-monitoring": "Remote Monitoring", "invite-saved-to-cookie": "Invite saved to cookie", "ask-for-display-name": "Ask for display name", "show-display-names": "Show display names", "show-active-speaker": "Show active speakers", "show-welcome-message": "Show welcome message", "1080p60-if-available": "1080p60 Video if Available", "auto-select-microphone": "Auto-select default microphone", "auto-select-camera": "Auto-select default camera", "compatibility-mode": "Compatibility mode", "hide-setting-buttons": "Hide settings button", "virtual-backgrounds": "Virtual backgrounds", "disable-animated-mixing": "Disable animations", "chunked-mode": "P2P Chunked-mode", "powerful-computers-only": "Only use with powerful computers and small groups!!", "guests-see-HD-video": "Guests see HD video", "no-self-preview": "Disable self-preview", "raise-hand-button": "Display 'raise-hand' button", "enable-compressor": "Enable audio compressor", "enable-equalizer": "Enable equalizer as option", "show-guest-tips": "Show guest setup tips", "prefix-screenshare": "Prefix screenshare IDs", "avatar-selection": "Can select an Avatar image", "meshcast-mode": "Stream via server", "mini-self-preview": "Mini self-preview", "rule-of-thirds": "Show rule-of-thirds grid", "only-see-director-feed": "Only see the director's feed", "mute-microphone-by-default": "Muted; guest can unmute", "unmute-by-director-only": "Muted; director can unmute", "guest-joins-with-no-camera": "Guest joins with no camera", "obfuscate-link": "Obfuscate link and parameters", "this-can-reduce-packet-loss": "Can reduce packet loss video corruption", "use-h264-codec": "Use H264 codec", "show-active-speakers": "Show active speakers", "green-background": "Green background", "fade-videos-in": "Fade videos in", "animate-mixing": "Animate mixing", "add-margin": "Add margin to videos", "unlock-video-bitrate": "Unlock Video Bitrate (20mbps)", "disable-downscaling": "Increase sharpness", "force-mono-audio": "Force mono audio", "fill-video-space": "Crop video to fit", "vertical-aspect-ratio": "Vertical video mode", "learn-more-about-params": "Learn more about URL parameters at ", "add-a-label": "Add a label", "mute": "Mute", "send-direct-chat": "Message", "close": "close", "send-message": "send message", "forward-to-room": "Transfer", "disconnect-guest": "Hangup", "voice-chat": " Solo Talk", "solo-video": "Highlight", "mute-video-guest": "Video off", "toggle-remote-speaker": "Deafen", "hide-guest": "<PERSON>de", "toggle-remote-display": "Blind", "add-to-scene": "add to scene 1", "scene-options": "Scene options", "add-to-scene2": "add to scene 2", "mute-scene": "mute in scenes", "stats-remote": " Scene Stats", "additional-controls": "Additional controls", "order-down": "<i class=\"las la-minus\"></i>", "create-timer": "Create Timer", "record-local": " Record Local", "record-remote": " Record Remote", "google-drive-record": " Google Drive", "change-params": "URL Params", "change-url": "Change URL", "request-upload": " Request File", "mirror-guest": " Mirror Video", "force-keyframe": "<PERSON> Puke Fix", "advanced-audio-settings": "Audio Settings", "advanced-camera-settings": "Video Settings", "user-raised-hand": "Lower Raised Hand", "remove-from-queue": "Activate Guest", "record-director-local": " Record", "solo-video-director": "Highlight", "video-source": " Video Source ", "max-resolution": "Max Resolution", "balanced": "Balanced", "smooth-cool": "Smooth and Cool", "select-audio-source": " Audio Source(s) ", "select-output-source": " Audio Output Destination ", "select-avatar-image": " Default Avatar / Placeholder Image: ", "select-digital-effect": " Digital Video Effects ", "no-effects-applied": "No effects applied", "blurred-background": "Blurred background", "blurred-background-2": "Blurred background 2 🧪", "digital-greenscreen": "Digital greenscreen", "virtual-background": "Virtual background", "face-mesh": "Face mesh (slow load)", "digital-zoom": "Digital zoom", "anonymous-mask": "Anonymous mask", "dog-face": "Dog ears and nose", "face-tracker": "Face tracker", "close-settings": "Close Settings", "user": "User", "hold-to-talk": "Hold-to-Talk Hot-key", "clear": "Clear", "enable": "Enable", "stop": "Stop", "cycle-between-audio-visualizations": "Cycle between several audio-visualizations styles", "cleaer-sites-local-storage": "Clear site's local browser storage and saved settings", "open-in-new-tab": "Open in new Tab", "copy-to-clipboard": "Copy to Clipboard", "edit-url": "Edit URL manually", "publish-url": "Publish via WHIP", "show-qr-code": "Show as QR Code", "open-ss-in-new-tab": "Share from a new tab", "ss-mode-1": "Screen Share Mode 1", "ss-mode-2": "Screen Share Mode 2", "ss-mode-3": "Screen Share Mode 3", "detach-clock2-pip": "Pop-out clock toggle", "mirror-video": "Mirror", "show-controls-video": "Show control bar", "hide-controls-video": "Hide control bar", "picture-in-picture": "Picture-in-picture", "picture-in-picture-all": "Picture-in-picture all", "full-window": "Full-window", "shrink-window": "Shrink-window", "pause-stream": "Pause stream", "resume-stream": "Resume stream", "record-to-disk": "Record to disk", "stop-record-to-disk": "Stop Recording", "copy-to-clipboard-frame": "Snapshot to clipboard", "save-current-frame": "Save frame to disk", "show-video-stats": "Show Stats", "custom-audio-output": "Audio Destination", "remote-hangup-connection": "Remote Hang-up", "remote-reload-connection": "Remote Reload Page", "change-playout-buffer": "Buffer (ms): ", "hold-ctrl": "tip:  <b>CTRL</b>(⌘) + <b>Click</b> for alt-menu", "change-room-settings": "Change room settings", "change-room-video-quality": "Change room video quality:", "limit-total-bitrate-quality": "Your max allowed total video upload bandwidth:", "highlight-director-only-video-guests-will-see": "Highlight Director (only video guests will see)", "create-global-timer": "Create Global Count-down Timer", "create-clock-timer": "Toggle Room Clock", "allow-for-remote-co-directors": "Allow for remote co-directors", "allow-co-directors-to-transfer-guests": "Allow co-directors to transfer guests", "allow-co-directors-to-change-a-guests-url": "Allow co-directors to change a guest's URL", "basic-co-director-invite-link": "Basic co-director invite link:", "local-global-record-start": "Local record - start all", "local-global-record-stop": "Local record - stop all", "remote-global-record": "Remote record - stop all", "buffer-settings": "<PERSON><PERSON><PERSON>", "publish-settings": "Publishing setup", "remote-control-obs-menu": "Remote Controller for OBS Studio", "apply-new-guest-settings": "Apply settings", "cancel": "Cancel", "invisible-guests": "Not Visible", "select-local-image": "Select Local Image", "available-languages": "Available Languages", "add-more-here": "Add More Here!", "add-to-calendar": "Add details to your Calendar:", "add-to-google-calendar": "Add to Google Calendar", "add-to-outlook-calendar": "Add to Outlook Calendar", "add-to-yahoo-calendar": "Add to Yahoo Calendar", "reload-page": "Refresh", "add-group-chat": "Create a Room", "rooms-allow-for": "Rooms allow for group-chat and the tools to manage multiple guests.", "room-name": "Room Name", "password-input-field": "Password", "guests-only-see-director": "The guests can see the director, but not other guests' videos", "scenes-can-see-director": "The director will be performing as well, appearing in group scenes", "default-codec-select": "Preferred Video Codec:  ", "enter-the-rooms-control": "Enter the room's Control Center in the director's role", "show-tips": "Show me some tips..", "added-notes": "<u><i>Important Tips:</i><br><br></u><li>Disabling video sharing between guests will improve performance</li><li>Invite only guests to the room that you trust.</li><li>The \"Recording\" option is considered experimental.</li>", "looking-to-just-chat-and-not-direct": "Looking to just chat and not direct?", "join-the-room-basic": "Join the room as a Participant, rather than a director", "back": "Back", "add-your-camera": "Add your Camera", "ask-for-permissions": "Allow Access to Camera/Microphone", "start": "START", "privacy-disabled": "Privacy warning: The director will be able to remotely change your camera, microphone, and URL while this page is open, if you continue.", "for-the-best-possible-experience-make-sure": "For the best possible experience, make sure", "your-device-is-powered": "Your device is powered", "your-connection-is-hardwired-instead-of-wifi": "Your connection is hardwired instead of wifi", "you-are-using-headphones-earphones": "You are using headphones / earphones", "up-to-4k": "4K", "no-audio": "No Audio", "add-a-password": " Add a Password", "use-chrome-instead": "Consider using a Chromium-based browser instead.<br> Safari is more prone to having audio issues", "update-your-device": "We've detected that you are using an old version of Apple iOS, which is known to have many issues.<br><br>Please consider updating.", "add-your-microphone": "Add your Microphone to OBS", "remote-screenshare-obs": "Remote Screenshare", "select-screen-to-share": "SELECT SCREEN TO SHARE", "audio-sources": "Audio Sources", "application-audio-capture": "", "1080p-screen-capture-guide": "", "create-reusable-invite": "Create Reusable Invite", "here-you-can-pre-generate": "Here you can pre-generate a reusable Browser Source link and a related guest invite link.", "generate-invite-link": "GENERATE THE INVITE LINK", "quality-paramaters": "Quality settings", "force-vp9-video-codec": "Force VP9 Video Codec", "enable-stereo-and-pro": "Enable Stereo and Pro HD Audio", "video-resolution": "Video Resolution: ", "general-paramaters": "User options", "allow-effects-invite": "Allow video effects to be used", "hide-mic-selection": "Force Default Microphone", "hide-screen-share": "Hide Screenshare Option", "obfuscate_url": "Obfuscate the Invite URL", "add-a-password-to-stream": " Add a password:", "interview-paramaters": "Two-way chat", "generate-host-link": "Create a link for the host speaker", "add-the-guest-to-a-room": " Add the guest to a room:", "invite-group-chat-type": "This room guest can:", "can-see-and-hear": "Can see and hear the group chat", "can-hear-only": "Can only hear the group chat", "cant-see-or-hear": "Cannot hear or see the group chat", "share-local-video-file": "Stream Media File", "select-the-video-files-to-share": "SELECT THE VIDEO FILES TO SHARE", "share-website-iframe": "Share Website", "enter-the-website-URL-you-wish-to-share": "Enter the URL website you wish to share.", "run-a-speed-test": "Run a Speed Test", "try-the-mixer-out": "Try out the Mixer", "try-out-versus-cam": "Multi-Stream Monitor", "voice-comms-app": "Group Voice Comms", "read-the-guides": "Browse the Guides", "wizard-link-generator": "Wizard Link Generator", "get-full-documentation": "Full Documentation", "get-the-source-code": "Source Code", "show-your-support": "Show Your Support", "publish-via-whip": "Publish via WHIP", "share-whepsrc": "Share via WHEP", "enter-the-whep-URL-you-wish-to-share": "Enter the WHEP URL you wish to share.", "info-blob": "<h2>What is VDO.Ninja</h2><br><li>100% <b>free</b>; no downloads; no personal data collection; no sign-in</li><li>Bring live video from your smartphone, remote computer, or friends directly into OBS or other studio software.</li><li>We use cutting edge Peer-to-Peer forwarding technology that offers privacy and ultra-low latency</li><br><li>Youtube video <i class=\"lab la-youtube\"></i><a href=\"https://www.youtube.com/watch?v=QaA_6aOP9z8&amp;list=PLWodc2tCfAH1WHjl4WAOOoRSscJ8CHACe&amp;index=1\" alt=\"Youtube video demoing VDO.Ninja\">Demoing it here</a></li><br><h3>   🛠 For support, see the <a href=\"https://www.reddit.com/r/VDONinja/\">sub-reddit <i class=\"lab la-reddit-alien\"></i></a> or join the <a href=\"https://discord.vdo.ninja/\">Discord <i class=\"lab la-discord\"></i></a>. The <a href=\"https://docs.vdo.ninja/\">documentation is here</a> and my personal email is <i><EMAIL></i></h3> ", "guest-1": "Guest 1", "guest-2": "Guest 2", "guest-3": "Guest 3", "guest-4": "Guest 4", "waiting-for-camera": "Waiting for Camera to Load", "local-global-record": "Local record - stop all", "ok": "✔ OK", "join-room": "Join room", "join-room-with-mic": "Join Room with Microphone", "join-room-with-mic-only": "Join with just Microphone", "join-room-with-camera": "Join Room with Camera", "join-room-with-video": "Join Room with Video", "share-screen-with-room": "Screenshare with Room", "share-your-mic": "Share your Microphone", "share-your-camera": "Share your Camera", "share-your-screen": "Share your Screen", "click-start-to-join": "Click Start to Join", "waiting-for-mic-to-load": "Waiting for mic to load", "waiting-for-camera-to-load": "Waiting for Camera to load", "push-to-talk-enable": " enable director`s microphone or video<br>(only guests can see this feed)", "low-cpu=broadcast-codec": "Low-CPU broadcast codec", "mute-guest": "Mute", "More-scene-options": "Scene options", "unmute": "unmute", "unhide-guest": "unhide", "undeafen": "undeafen", "unblind": "unblind", "advanced": "Advanced ", "advanced-paramaters": "Advanced Options", "allow-remote-control": "Remote Control Camera Zoom (android)", "more-than-four-can-join": "These four guest slots are just for demonstration. More than four guests can actually join a room.", "toggle-control-video": "Toggle control bar", "chrome-cast": "Cast..", "please-select-option-to-join": "Please select an option to join.", "guest-toggle": "Guest Toggle", "settings": "Settings", "more": "More"}, "placeholders": {"join-by-room-name-here": "Join by Room Name here", "load-a-website-url": "Load a website URL", "enter-chat-message-to-send-here": "Enter chat message to send here", "enter-your-message-here": "Enter your message here", "press-a-key-here": "press a key here", "enter-a-url-for-the-sidebar-page": "Enter a URL for the sidebar page", "-whip-url-to-publish-to-goes-here": "➡️ WHIP URL to publish to goes here", "-authentication-bearer-token-optional-": "🗝️ Authentication <PERSON><PERSON> (optional)", "enter-the-remote-obs-password-here": "Enter the remote OBS password here", "enter-the-room-name-here": "Enter the room name here", "enter-the-room-password-here": "Enter the room password here", "enter-a-room-name-here": "Enter a Room Name here", "optional-room-password-here": "Optional room password here", "optional": "optional", "give-this-media-source-a-name-optional-": "Give this media source a name (optional)", "add-an-optional-password": "Add an optional password", "enter-room-name-here": "Enter Room name here"}, "miscellaneous": {"new-display-name": "Enter a new Display Name for this stream", "submit-error-report": "Press OK to submit any error logs. Error logs may contain private information.", "director-redirect-1": "The director wishes to redirect you to the URL: ", "director-redirect-2": "Press OK to be redirected.", "audio-processing-disabled": "Audio processing is disabled with this guest. Can't mute or change volume", "not-the-director": "<font color='red'>You are not the director of this room. You will have limited to no control.</font>", "room-is-claimed": "The room is already claimed by someone else.\n\nOnly the first person to join a room is the assigned director.\n\nRefresh after the first director leaves to claim.", "token-room-is-claimed": "The room is claimed by someone else.\n\nJoin as a guest or co-director instead.", "room-is-claimed-codirector": "The room is already claimed by someone else.\n\nTrying to join as a co-director...", "streamid-already-published": "The stream ID you are publishing to is already in use.\n\nPlease try with a different invite link or refresh to retry again.\n\nYou will now be disconnected.", "director": "Director", "unknown-user": "Unknown User", "room-test-not-good": "The room name 'test' is very commonly used and may not be secure.\n\nAre you sure you wish to proceed?", "load-previous-session": "Would you like to load your previous session's settings?", "enter-password": "Please enter the password below: \n\n(Note: Passwords are case-sensitive and you will not be alerted if it is incorrect.)", "enter-password-2": "Please enter the password below: \n\n(Note: Passwords are case-sensitive.)", "enter-director-password": "Please enter the director's password:\n\n(Note: Passwords are case-sensitive and you will not be alerted if it is incorrect.)", "password-incorrect": "The password was incorrect.\n\nRefresh and try again.", "enter-display-name": "Please enter your display name:", "enter-new-display-name": "Enter a new Display Name for this stream", "what-bitrate": "What bitrate would you like to record at? (kbps)\n(note: This feature is experimental, so have backup recordings going)", "enter-website": "Enter a website URL to share", "press-ok-to-record": "Press OK to start recording. Press again to stop and download.\n\nWarning: Keep this browser tab active to continue recording.\n\nYou can change the default video bitrate if desired below (kbps)", "no-streamID-provided": "No streamID was provided; one will be generated randomily.\n\nStream ID: ", "alphanumeric-only": "Info: Only AlphaNumeric characters should be used for the stream ID.\n\nThe offending characters have been replaced by an underscore", "stream-id-too-long": "The Stream ID should be less than 45 alPhaNuMeric characters long.\n\nWe will trim it to length.", "share-with-trusted": "Share only with those you trust", "pass-recommended": "A password is recommended", "insecure-room-name": "Insecure room name.", "allowed-chars": "Allowed chars", "transfer": "transfer", "armed": "armed", "transfer-guest-to-room": "Transfer guests to room:\n\n(Please note: rooms must share the same password)", "transfer-guest-to-url": "Transfer guests to new website URL.\n\nGuests will be prompted to accept unless they are using &consent", "mute-in-scene": "mute in scene", "unmute-guest": "unmute guest", "deafen": "deafen guest", "blind": "blind guest", "unhide": "unhide guest", "confirm-disconnect-users": "Are you sure you wish to disconnect these users?", "confirm-disconnect-user": "Are you sure you wish to disconnect this user?", "enter-new-codirector-password": "Enter a co-director password to use", "control-room-co-director": "Control Room: Co-Director", "volume-control": "Volume control for local playback only", "signal-meter": "Video packet loss indicator of video preview; green is good, red is bad. Flame implies CPU is overloaded. May not reflect the packet loss seen by scenes or other guests.", "waiting-for-the-stream": "Waiting for the stream. Tip: Adding &cleanoutput to the URL will hide this spinner, or click to retry, which will also hide it.", "main-director": "Main Director", "share-a-screen": "Share a screen", "stop-screen-sharing": "Stop screen sharing", "you-have-been-transferred": "You've been transferred to a different room", "you-have-been-activated": "The director has allowed you to see others in the room now", "you-are-no-longer-a-co-director": "You are no longer a co-director as you were transferred.", "transferred": "Transferred", "room-changed": "Your room has changed", "headphones-tip": "<i>Tip:</i> Use headphones to avoid audio echo issues.", "camera-tip-c922": "<i>Tip:</i> To achieve 60-fps with a C922 webcam, low-light compensation needs to be turned off, exposure set to auto, and 720p used.", "camera-tip-camlink": "<i>Tip:</i> A Cam Link may glitch green/purple if accessed elsewhere while already in use.", "samsung-a-series": "Samsung A-series phones may have issues with Chrome; if so, try Firefox Mobile instead or switch video codecs.", "screen-permissions-denied": "Permission to capture denied. Ensure your browser has screen record system permissions\n\n1.On your Mac, choose Apple menu  > System Preferences, click Security & Privacy , then click Privacy.\n2.Select Screen Recording.\n3.Select the checkbox next to your browser to allow it to record your screen.", "change-audio-output-device": "Audio could not be captured. Please make sure you have an audio output device available.\n\nSome gaming headsets (ie: Corsair) may need to be set to 2-channel output to work, as surround sound drivers may cause problems.", "prompt-access-request": " is trying to view your stream. Allow them?", "confirm-reload-user": "Are you sure you wish to reload this user's browser?", "webrtc-is-blocked": "⚠ This browser has either blocked WebRTC or does not support it.\n\nThis site will not work without it.\n\nDisable any browser extensions or privacy settings that may be blocking WebRTC, or try a different browser.", "not-clean-session": "Video effects or canvas rendering failed.\n\nCheck to ensure any remotely hosted images are cross-origin allowed.", "ios-no-screen-share": "Sorry, but your iOS browser does not support screen-sharing.", "android-no-screen-share": "Sorry, your mobile browser does not support screen-sharing.", "no-screen-share-supported": "Sorry, your browser does not support screen-sharing.\n\nPlease use the desktop versions of Firefox or Chrome instead.", "speech-not-suppoted": "⚠ Speech Recognition is not supported by this browser", "blue-yeti-tip": "<i>Tip:</i> Blue Yeti microphones may experience issues being overly loud. <a href='https://support.google.com/chrome/thread/7542181?hl=en&msgid=79691143'>Please see here</a> for a solution or disable auto-gain.", "site-not-responsive": "<h3>Notice: The system cannot be accessed or is currently slow to respond.</h3>Check your connection or contact support.This service requires the use of Websockets over port 443.", "no-audio-source-detected": "Notice: No Audio Source was detected", "viewer-count": "Total outbound p2p connections of this remote stream", "enter-url-for-widget": "Enter a URL for a page to embed as a sidebar", "director-password": "Enter the main director's password", "vision-disabled": "The Director has disabled your vision temporarily<br /><br ><center><i style='font-size:500%;' class='las la-eye-slash'></i></center>", "invalid-remote-code": "Invalid remote control code.\n\nUse the field below to try again with a different passcode.", "invalid-remote-code-obs": "Invalid remote control code.\n\nThe remote OBS system needs a matching passcode set using &remote.\n\nSee the documentation for help..", "request-rejected-obs": "The request was rejected.The remote OBS system needs a matching passcode set using &remote.See the documentation for help.", "remote-token-rejected": "The remote request failed; the &remote token did not match or the remote user does not allow remote control.", "remote-control-failed": "The remote control request failed.", "remote-peer-connected": "Remote peer connected to video stream.\n\nConnection to handshake server being killed on request. This increases security, but the peer will not be able to reconnect automatically on connection failure.\n\nPress OK to start the stream!", "director-denied": "The main director denied you as a co-director", "only-main-director": "Only the main director can transfer this guest", "request-failed": "The request failed; you can't apply this action", "tokens-did-not-match": "The remote request failed; the remote token did not match or the remote user does not allow remote control.", "token-not-director": "The request failed; the remote user did not recognize you as the director", "approved-as-director": "The director approved you as a co-director", "you-are-a-codirector": "You are a co-director of this room; you have partial director control assigned to you.", "this-is-you": "This is you, a co-director.<br />You are also a performer.", "preview-meshcast-disabled": "You can't adjust the preview bitrate for Meshcast-based streams", "co-director": "Co-Director", "you-not-yet-activated": "Please wait until the director brings you into the room", "sample-rate-too-high": "Your audio playback device has its sample rate set very high. If having audio issues, try using 48-kHz instead.", "no-network": "Network connection lost 🤷‍♀️❌📶", "no-network-details": "Network connection lost. 🤷‍♀️❌📶\n\nHave you lost your Internet connection?", "enter-password-if-desired": "Enter a password if provided, otherwise just click Cancel", "your-screenshare": "Your screenshare", "your-camera": "Your camera", "accept-inbound-caller": "Accept the inbound telephone caller?", "disable-video": "Disable Video", "show-more-options": "Show more options", "system-default": "System Default"}}