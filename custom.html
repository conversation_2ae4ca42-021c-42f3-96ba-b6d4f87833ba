<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VDO.Ninja - App</title>
    <style>
        body { font-family: sans-serif; background-color: #121212; color: #e0e0e0; display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; }
        .container { background-color: #1e1e1e; padding: 2em; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); text-align: center; width: 90%; max-width: 500px; }
        input[type="text"] { padding: 0.8em; margin: 1em 0; width: 80%; background-color: #333; border: 1px solid #555; color: #e0e0e0; border-radius: 4px; }
        button { padding: 0.8em 1.5em; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 1em; margin-top: 1em; }
        button:hover { background-color: #0056b3; }
    </style>
</head>
<body>

    <div id="join-room-container" class="container">
        <h2>Join a Room</h2>
        <input type="text" id="roomName" placeholder="Enter Room Name">
        <br>
        <button onclick="joinRoom()">Join Room with Camera</button>
    </div>

    <script>
        function joinRoom() {
            const roomName = document.getElementById('roomName').value;
            if (!roomName) {
                alert('Please enter a room name.');
                return;
            }

            const password = prompt('Enter a password if provided, otherwise just click Cancel') || '';

            let url = `index.html?room=${encodeURIComponent(roomName)}&webcam`;
            if (password) {
                url += `&password=${encodeURIComponent(password)}`;
            }
            window.location.href = url;
        }
    </script>
</body>
</html>