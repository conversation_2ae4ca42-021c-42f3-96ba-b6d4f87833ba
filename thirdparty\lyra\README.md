## WebRTC snippets for Lyra sourced from meeting.dev

Apache 2.0 Licenced

Sourced from: https://meeting.dev/lab/lyra-webrtc/loopback.html


## NOTE FROM STEVE
 At the moment Lyra isn't actually being used, but I'm trying to get it working with VDO.Ninja. It may be removed at some future point - steve

## TFLITE MODELS SOURCED FROM GOOGLE ON GITHUB

google/lyra is licensed under the
Apache License 2.0
https://github.com/google/lyra
https://github.com/google/lyra/tree/f079e8c4dd1c61c87de1852178976ee3bdf15561/model_coeffs


## Further Acknowledgments 

Thanks to [the team that developed Lyra](https://ai.googleblog.com/2021/02/lyra-new-very-low-bitrate-codec-for.html) and to [mayitayew for making it work with WASM](https://github.com/mayitayew/soundstream-wasm).

We are using a modified Lyra WASM. It is available [here](https://github.com/Flash-Meeting/lyra-wasm)


