<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VDO.Ninja - Join Room</title>
    <style>
        :root {
            --primary-color: #2D3748;
            --accent-color: #4FD1C5;
            --background-color: #F7FAFC;
            --text-color: #1A202C;
            --error-color: #E53E3E;
            --success-color: #38A169;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .join-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .preview-container {
            width: 100%;
            aspect-ratio: 16/9;
            background: #000;
            margin: 1rem 0;
            border-radius: 8px;
            overflow: hidden;
        }

        #previewVideo {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        select, input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #CBD5E0;
            border-radius: 6px;
            font-size: 1rem;
            margin-bottom: 0.5rem;
        }

        select:focus, input:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(79, 209, 197, 0.2);
        }

        .button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-size: 1rem;
            cursor: pointer;
            width: 100%;
            transition: background 0.2s ease;
        }

        .button:hover {
            background: var(--accent-color);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 1rem;
            font-size: 0.875rem;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
        }

        @media (max-width: 640px) {
            .container {
                margin: 1rem auto;
            }

            .join-section {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">VDO.Ninja</div>
            <p>Simple, secure video calls</p>
        </header>

        <main class="join-section">
            <div class="preview-container">
                <video id="previewVideo" autoplay muted playsinline></video>
            </div>

            <form id="joinForm">
                <div class="form-group">
                    <label for="roomName">Room Name</label>
                    <input type="text" id="roomName" placeholder="Enter room name" required>
                </div>

                <div class="form-group">
                    <label for="cameraSelect">Camera</label>
                    <select id="cameraSelect">
                        <option value="">Loading cameras...</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="micSelect">Microphone</label>
                    <select id="micSelect">
                        <option value="">Loading microphones...</option>
                    </select>
                </div>

                <button type="submit" class="button">Join Room</button>

                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span>Ready to join</span>
                </div>
            </form>
        </main>
    </div>

    <script>
        // Load necessary scripts from original app
        const scriptsToLoad = [
            "./thirdparty/adapter.js",
            "./thirdparty/CodecsHandler.js",
            "./thirdparty/aes.js",
            "./webrtc.js",
            "./lib.js",
            "./main.js"
        ];

        scriptsToLoad.forEach(src => {
            const script = document.createElement('script');
            script.src = src;
            script.crossOrigin = "anonymous";
            document.body.appendChild(script);
        });

        // Initialize camera/mic selection
        async function initializeDevices() {
            try {
                const devices = await navigator.mediaDevices.enumerateDevices();
                const cameras = devices.filter(device => device.kind === 'videoinput');
                const mics = devices.filter(device => device.kind === 'audioinput');

                const cameraSelect = document.getElementById('cameraSelect');
                const micSelect = document.getElementById('micSelect');

                cameraSelect.innerHTML = cameras.map(camera => 
                    `<option value="${camera.deviceId}">${camera.label || `Camera ${cameras.indexOf(camera) + 1}`}</option>`
                ).join('');

                micSelect.innerHTML = mics.map(mic => 
                    `<option value="${mic.deviceId}">${mic.label || `Microphone ${mics.indexOf(mic) + 1}`}</option>`
                ).join('');

                // Start preview
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    video: { deviceId: cameras[0]?.deviceId ? { exact: cameras[0].deviceId } : undefined },
                    audio: false 
                });
                document.getElementById('previewVideo').srcObject = stream;
            } catch (err) {
                console.error('Error accessing media devices:', err);
            }
        }

        // Handle form submission
        document.getElementById('joinForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const roomName = document.getElementById('roomName').value;
            const cameraId = document.getElementById('cameraSelect').value;
            const micId = document.getElementById('micSelect').value;

            // Use existing VDO.Ninja connection logic here
            // This would integrate with the existing session object
            if (typeof session !== 'undefined') {
                session.roomid = roomName;
                // Add additional connection logic as needed
            }
        });

        // Initialize when document loads
        document.addEventListener('DOMContentLoaded', initializeDevices);
    </script>
</body>
</html>