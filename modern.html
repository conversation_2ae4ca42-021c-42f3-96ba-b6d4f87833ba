<html lang='en'>
	<head>
		<script type="text/javascript">
			//  MS Internet Explorer must not be given a chance to fail before I can give the user an error message.
			try {
				var msie = window.navigator.userAgent.indexOf("MSIE ");
				if (msie>0 || !!navigator.userAgent.match(/Trident.*rv\:11\./)){ // If MSIE or IE 11
					alert("Internet Explorer is not supported.\n\nPlease consider using Microsoft Edge or Google Chrome instead\n\nYou will be forwarded to the download page for MS Edge now.");
					console.error("INTERNET EXPLORER IS EVIL");
					document.write("Internet Explorer is not supported");
					window.location = "https://www.microsoft.com/edge";
				}
			} catch(e){
				console.error(e);
			}
		
		</script>
		<style>
			html {
				background-color: #0000;
				transition: opacity .1s linear;
			}

			/* Landing Page Styles */
			.landing-container {
				display: flex;
				justify-content: center;
				align-items: center;
				min-height: 100vh;
				width: 100%;
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				padding: 20px;
				box-sizing: border-box;
			}

			.landing-card {
				background: rgba(255, 255, 255, 0.95);
				backdrop-filter: blur(10px);
				border-radius: 20px;
				padding: 40px;
				box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
				max-width: 450px;
				width: 100%;
				text-align: center;
				animation: slideUp 0.6s ease-out;
			}

			@keyframes slideUp {
				from {
					opacity: 0;
					transform: translateY(30px);
				}
				to {
					opacity: 1;
					transform: translateY(0);
				}
			}

			.landing-header h1 {
				color: #333;
				font-size: 2.5rem;
				margin-bottom: 10px;
				font-weight: 700;
			}

			.landing-header p {
				color: #666;
				font-size: 1.1rem;
				margin-bottom: 30px;
			}

			.landing-form {
				text-align: left;
			}

			.form-group {
				margin-bottom: 25px;
			}

			.form-group label {
				display: block;
				color: #333;
				font-weight: 600;
				margin-bottom: 8px;
				font-size: 1rem;
			}

			.form-group input {
				width: 100%;
				padding: 15px;
				border: 2px solid #e1e5e9;
				border-radius: 12px;
				font-size: 1rem;
				transition: all 0.3s ease;
				box-sizing: border-box;
				background: #fff;
			}

			.form-group input:focus {
				outline: none;
				border-color: #667eea;
				box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
			}

			.submit-btn {
				width: 100%;
				padding: 15px;
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				color: white;
				border: none;
				border-radius: 12px;
				font-size: 1.1rem;
				font-weight: 600;
				cursor: pointer;
				transition: all 0.3s ease;
				margin-top: 10px;
			}

			.submit-btn:hover {
				transform: translateY(-2px);
				box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
			}

			.submit-btn:active {
				transform: translateY(0);
			}

			/* Responsive design */
			@media (max-width: 768px) {
				.landing-card {
					padding: 30px 20px;
					margin: 10px;
				}

				.landing-header h1 {
					font-size: 2rem;
				}
			}

			/* Camera setup styling */
			#container-3 {
				background: rgba(255, 255, 255, 0.95);
				backdrop-filter: blur(10px);
				border-radius: 20px;
				margin: 20px;
				box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
				animation: slideUp 0.6s ease-out;
			}

			#container-3 h2 {
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
				background-clip: text;
				text-align: center;
				margin-bottom: 20px;
			}

			#container-3 .gowebcam:enabled {
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
				border: none !important;
				border-radius: 12px !important;
				color: white !important;
				font-weight: 600 !important;
				padding: 15px 30px !important;
				font-size: 1.1rem !important;
				transition: all 0.3s ease !important;
			}

			#container-3 .gowebcam:enabled:hover {
				transform: translateY(-2px);
				box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
			}
		</style>
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
		<meta content="text/html;charset=utf-8" http-equiv="Content-Type" />
		<meta content="utf-8" http-equiv="encoding" />
		<meta name="msapplication-TileColor" content="#da532c" />
		<meta name="theme-color" content="#0f131d" />
		<link rel="stylesheet" href="./main.css?ver=401" />
		<link rel="stylesheet" href="app.css">
		<script type="text/javascript" crossorigin="anonymous" src="./thirdparty/adapter.js"></script>
		<style id="lightbox-animations" type="text/css"></style>
	</head>
	<body id="main" class="main hidden" onload="main()">

		<script type="text/javascript" crossorigin="anonymous"  src="./thirdparty/CodecsHandler.js?ver=28"></script>
		<script type="text/javascript" crossorigin="anonymous" src="./thirdparty/aes.js"></script>
		<script type="text/javascript" crossorigin="anonymous" src="./webrtc.js?ver=842"></script>
		<input id="zoomSlider" type="range" style="display: none;" />
		<span id="electronDragZone" style="pointer-events: none; z-index:-10; position:absolute;top:0;left:0;width:100%;height:2%;-webkit-app-region: drag;min-height:20px;"></span>
		<div id="header">
			
			<a id="logoname" href="./" style="text-decoration: none; color: white; margin: 0 2px 0px 8px;">
				<span data-translate="logo-header">
					<span id="qos">V</span>DO.Ninja 
				</span>
			</a>
			<div id="head1">
				<input type="text" autocorrect="off" autocapitalize="none" id="joinroomID" name="joinroomID" tabindex="1" size="22" placeholder="Join by Room Name here" alt="Enter a room name to join" title="Enter a room name to quick join" onkeyup="jumptoroom(event)"/>
				<button onclick="jumptoroom();" id='jumptoroomButton' role="button" aria-pressed="false" tabindex="1" alt="Join room" title="Join room" >GO</button>
			</div>
			<div id="head1a" class="hidden">
				<input type="text" autocorrect="off" autocapitalize="none" id="joinbyURL" name="joinbyURL" size="22" placeholder="Load a website URL" alt="Enter the URL to load" title="Enter the URL to load"/>
				<button onclick="jumptoURL(event)"  role="button" aria-pressed="false" alt="Load URL" title="Load URL" >Load URL</button>
			</div>
			<div id="head5" class="hidden"></div>
			<div id="head3" style="display: inline-block;" class="hidden">
				<span style="color: #888;" id="copythisurl" tabindex="1" > &nbsp; 
					<span data-translate="copy-this-url">Copy this URL into an OBS "Browser Source"</span> <i style="color: #CCC;" class="las la-long-arrow-alt-right"></i> &nbsp; 
				</span>
			</div>
			<div id="head3a" style="display: inline-block;" class="hidden">
				<a
					id="reshare"
					data-drag="1"
					onclick="copyFunction(this, event)"
					class="task grabLinks"
					data-menu="context-menu"
					style="font-weight: bold; color: #afa !important; cursor: grab; background-color: #0000;  font-size: 115%; min-width: 335px; max-width: 800px;"
				></a>
				<i class="las la-paperclip" style="color: #DDD;" title="Copy link to clipboard" onclick="copyFunction(document.getElementById('reshare'), event);" onmouseover="this.style.cursor='pointer'"></i>
				<span title="Save and ask to reload the current page on next site visit" style='font-size:92%;' onclick="saveRoom(this);" onmouseover="this.style.cursor='pointer'">💾</span>
			</div>
			<div id="head4" style="display: inline-block;" class="hidden">
				<span style="font-size: 68%; color: white;">
					<span data-translate="you-are-in-the-control-center">Control center for room:</span>
					
					<div id="dirroomid" style="font-size: 140%; color: #99c; display: inline-block;"></div>
					<span id="saveRoom" onclick="saveRoom(this)" style='cursor:pointer;margin-left:10px;' title="Will remember the room, prompting you the next time you visit if you wish to load this director's room again">💾</span>
					<span id="togglePreviewMode" onclick="switchModes()" style='cursor:pointer;margin-left:2px;' title="Toggle between the director control-room view and a scene preview-mode.">🪟</span>
				</span>
			</div>
			<div id="head2" class="hidden" style="display: inline-block; text-decoration: none; font-size: 60%; color: white;">
				<span data-translate="joining-room">You are in room</span>:
				<div id="roomid" style="display: inline-block;"></div>
			</div>
			<div id="head6" class="hidden" data-translate="only-director-can-hear-you">Only the director can hear you currently.</div>
			<div id="head7" class="hidden" data-translate="director-muted-you">The director has muted you.</div>
			<div id="head8" class="hidden" data-translate="director-video-muted-you">The director has disabled your camera temporarily.</div>
		</div>
		<div id="obsState" class="hidden" >ACTIVE</div>
		<div id="chatModule" class="hidden">
			<div class="chat-header">
				<span>Chat</span>
				<span>
					<a id="popOutChat" onclick="createPopoutChat();"><i class="las la-external-link-alt"></i></a>
					<a id="closeChat" onclick="toggleChat();">x</i></a>
				</span>
			</div>
			
			<div id="chatBody" class="resizable-div">
				<!-- Chat messages will be inserted here -->
			</div>
			
			<div class="chat-input-area">
				<input type="text" id="chatInput" placeholder="Enter chat message to send" onkeypress="EnterButtonChat(event)" />
				<button class="chatBarInputButton" onclick="sendChatMessage()">Send</button>
				<button onclick="toggleFileshare()"><i class="las la-file-upload"></i></button>
			</div>
			<div class="resizer"></div>
		</div>
		<div id="activeShares"></div>
		<div id="controlButtons" class="hidden">
			<div class="controlPositioning">
                <div id="subControlButtons">
					<div id="unmuteSelf" title="Hear yourself at 50% volume" alt="Hear yourself at 50% volume" aria-label="Hear Yourself" onmousedown="event.preventDefault(); event.stopPropagation();"  onclick="toggleDirectFeedback()"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="hidden float" style="cursor: pointer;" >
                        <i id="unmuteSelftoggle" class="toggleSize las la-podcast"></i>
                    </div>
					<div id="mediafileshare" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Select and stream a media file" aria-label="Stream a media file" alt="Stream a media file to others" onclick="getById('fileselector3').click();" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="float hidden" style="cursor: pointer;">
                        <i id="mediafilesharetoggle" onmousedown="event.preventDefault(); event.stopPropagation();"  class="toggleSize las la-file-video"></i>
						<input id="fileselector3" class="hidden" onchange="session.changePublishFile(this,event);" type="file" accept="video/*,audio/*" alt="Hold CTRL (or CMD) to select multiple files" title="Hold CTRL (or CMD) to select multiple files" multiple/>
                    </div>
				
                    <div id="blindAllGuests" title="Blind all guests in room (toggle)" alt="Blind all guests in room (toggle)" aria-label="Blind all guests in room" onmousedown="event.preventDefault(); event.stopPropagation();"  onclick="blindAllGuests(this, event)"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="hidden float" style="cursor: pointer;" >
                        <i class="toggleSize las la-eye"></i>
                    </div>
                
                    <div id="queuebutton" title="Load the next guest in queue" alt="Load the next guest in queue"  aria-label="Load next guest in queue" onmousedown="event.preventDefault(); event.stopPropagation();"  onclick="nextQueue()"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="hidden float" style="cursor: pointer;" >
                        <i id="queuetoggle" class="toggleSize las la-stream"></i>
                        <div id="queueNotification"></div>
                    </div>
                    <div id="sharefilebutton" title="Transfer any file to the group" alt="Transfer any file to the group" aria-label="Select file to transfer" onmousedown="event.preventDefault(); event.stopPropagation();"  onclick="toggleFileshare()"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="float hidden" style="cursor: pointer;" >
                        <i id="filesharetoggle" class="toggleSize las la-file-upload"></i> 
                        <div id="transferNotification"></div>
                    </div>
                    <div id="chatbutton" title="Toggle the Chat" alt="Toggle the Chat" aria-label="Text chat" onmousedown="event.preventDefault(); event.stopPropagation();"  onclick="toggleChat()"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="hidden float" style="cursor: pointer;" >
                        <i id="chattoggle" class="toggleSize las la-comment-alt"></i>
                        <div id="chatNotification"></div>
                    </div>
                    <div id="mutespeakerbutton" onmousedown="event.preventDefault(); event.stopPropagation();" alt="Toggle the speaker output." aria-label="Mute Speaker output" title="Mute the Speaker (ALT + A)" onclick="toggleSpeakerMute()"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="hidden float" style="cursor: pointer;" >
                        <i id="mutespeakertoggle" class="toggleSize las la-volume-up" style="position: relative; top: 0.5px;"></i>
                    </div>
                    <div id="mutebutton" onmousedown="toggleMute(false, event);event.preventDefault(); event.stopPropagation();" data-translate="mute-the-mic" title="Mute the Mic (CTRL/⌘ + M)" alt="Mute the Mic" aria-label="Mute Microphone" ontouchstart="toggleMute(false, event);event.preventDefault(); event.stopPropagation();" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="hidden float" style="cursor: pointer;">
                        <i id="mutetoggle" class="toggleSize las la-microphone" style="position: relative; top: 0.5px;"></i>
                    </div>
                    <div id="mutevideobutton" onmousedown="event.preventDefault(); event.stopPropagation();" data-translate="disable-the-camera" title="Disable the Camera (CTRL/⌘ + B)" alt="Disable the Camera" aria-label="Mute Camera" onclick="toggleVideoMute()"   tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="hidden float" style="cursor: pointer;">
                        <i id="mutevideotoggle" onmousedown="event.preventDefault(); event.stopPropagation();"  class="toggleSize las la-video"></i>
                    </div>
                    <div id="screensharebutton" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Share a Screen with others" alt="Share a Screen with others" aria-label="Share a screen" onclick="screenshareTypeDecider(1)" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="float hidden task" data-menu="context-menu-screen-share" style="cursor: pointer;">
                        <i id="screensharetoggle" onmousedown="event.preventDefault(); event.stopPropagation();"  class="toggleSize las la-desktop"></i>
                    </div>
                    <div id="screenshare2button" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Add a Screen Share" alt="Add a Screen Share" aria-label="Share a screen"  onclick="screenshareTypeDecider(2)" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="float hidden task" data-menu="context-menu-screen-share"  style="cursor: pointer;">
                        <i id="screenshare2toggle" onmousedown="event.preventDefault(); event.stopPropagation();"  class="toggleSize las  la-tv"></i>
                    </div>
                    <div id="screenshare3button" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Share a Screen with others" alt="Add a Screen Share" aria-label="Share a screen"  onclick="screenshareTypeDecider(3)" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="float hidden task" data-menu="context-menu-screen-share"  style="cursor: pointer;">
                        <i id="screenshare3toggle" onmousedown="event.preventDefault(); event.stopPropagation();"  class="toggleSize las  la-tv"></i>
                    </div>
                    <div id="websitesharebutton" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Share a website with your guests (IFRAME)" aria-label="Share a website" alt="Share a website with your guests (IFRAME)" onclick="shareWebsite(false, event)" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="float hidden" style="cursor: pointer;">
                        <i id="websitesharetoggle" onmousedown="event.preventDefault(); event.stopPropagation();"  class="toggleSize las la-window-maximize"></i>
                    </div>
                    <div id="websitesharebutton2" onmousedown="event.preventDefault(); event.stopPropagation();" title="Hold CTRL (or CMD) and click to spotlight this video" alt="Share a website as an embedded iFRAME" aria-label="Share a website" onclick="shareWebsite(false, event)" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" class="float2 orange shake hidden" style="cursor: pointer;max-width: 200px;margin: auto;padding: 0 10px;">
                        <i onmousedown="event.preventDefault(); event.stopPropagation();" class="toggleSize las la-window-close" style="display: inline-block;"></i>
                        <div style="display: inline-block;width: 85px;line-height: 1; font-size: 0.9em; background-color: unset;box-shadow: unset;">
                            Stop Sharing Website
                        </div>
                    </div>
                    <div id="fullscreenPage" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Full-screen the page" alt="Full-screen the page" aria-label="Full screen"  onclick="fullscreenPageToggle()" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="float hidden" style="cursor: pointer;">
                        <i id="fullscreenPageToggle" onmousedown="event.preventDefault(); event.stopPropagation();"  class="toggleSize las la-expand-arrows-alt"></i>
                    </div>
					<div id="PictureInPicturePage" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Picture-in-Picture the video mix" alt="Picture-in-Picture the page" aria-label="Picture-in-Picture"  onclick="PictureInPicturePageToggle()" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="float hidden" style="cursor: pointer;">
                        <i id="PictureInPicturePageToggle" onmousedown="event.preventDefault(); event.stopPropagation();"  class="toggleSize las la-external-link-square-alt"></i>
                    </div>
                    <div id="flipcamerabutton" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Cycle the Cameras"  onclick="cycleCameras()" class="hidden float"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" style="cursor: pointer;" aria-label="Cycle Cameras" alt="Cycle the Cameras">
                        <i id="settingstoggle" class="toggleSize las la-sync-alt"></i> 
                    </div>
					
					 <div id="blackoutmode" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Enter black-out mode"  onclick="blackoutMode()" class="float hidden"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" style="cursor: pointer;" aria-label="Black out mode" alt="Enter black-out mode">
                        <i id="blackouttoggle" class="toggleSize las la-moon"></i> 
                    </div>
					
                    <div id="obscontrolbutton" onmousedown="event.preventDefault(); event.stopPropagation();"  title="OBS Remote Controller; start/stop and change scenes." onclick="toggleOBSControls();" class="hidden float"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" style="cursor: pointer;" aria-label="Remote OBS control menu" alt="Toggle the Remote OBS Controls Menu">
                        <i id="obscontroltoggle" class="toggleSize las la-gamepad"></i>
                    </div>
                    
                    <div id="roomsettingsbutton" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Room Settings" onclick="toggleRoomSettings();" class="hidden float"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" style="cursor: pointer;" alt="Toggle the Room Settings Menu" aria-label="Room settings menu">
                        <i id="roomsettingstoggle" class="toggleSize las la-users-cog"></i>
                    </div>
                    
                    <div id="settingsbutton" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Your audio and video Settings"  onclick="toggleSettings()" class="hidden float"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" style="cursor: pointer;" alt="Toggle Settings Menu" aria-label="Settings menu">
                        <i id="settingstoggle" class="toggleSize las la-cog"></i>
                    </div>
                    
                    <div id="hangupbutton"  onmousedown="event.preventDefault(); event.stopPropagation();" title="Hangup the Call" aria-label="Hang up" alt="Hangup the Call" onclick="hangup()" class="hidden float"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" style="cursor: pointer;" >
                        <i class="toggleSize las la-phone rotate225" aria-hidden="true"></i>
                    </div>
                    <div id="raisehandbutton" onmousedown="event.preventDefault(); event.stopPropagation();"  data-raised="0" title="Alert the host you want to speak"  aria-label="Raise hand" alt="Alert the host you want to speak" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" onclick="raisehand()" class="hidden float" style="cursor: pointer;">
                        <i class="toggleSize las la-hand-paper" style="position: relative; right: 1px;" aria-hidden="true"></i>
                    </div>
                    
                    <div id="pptbackbutton" onmousedown="event.preventDefault(); event.stopPropagation();" title="Go back a slide" aria-label="Back a slide" alt="Go back a slide" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" onclick="gobackSlide()" class="hidden float red" style="cursor: pointer;">
                        <i class="toggleSize las la-chevron-left" style="position: relative; right: 1px;" aria-hidden="true"></i>
                    </div>
                    <div id="pptnextbutton" onmousedown="event.preventDefault(); event.stopPropagation();" title="Next slide" aria-label="Next slide" alt="Next slide" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" onclick="nextSlide()" class="hidden float" style="cursor: pointer;">
                        <i class="toggleSize las la-chevron-right" style="position: relative; right: 1px;" aria-hidden="true"></i>
                    </div>
                    
                    <div id="recordLocalbutton" onmousedown="event.preventDefault(); event.stopPropagation();"  data-state="0" title="Record your stream to disk" aria-label="Record your stream to disk" alt="Record your stream to disk" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" onclick="recordLocalVideoToggle();" class="hidden float" style="cursor: pointer;">
                        <i class="toggleSize las la-dot-circle" style="position: relative;" aria-hidden="true"></i>
                    </div>
                    <div id="recordLocalScreenbutton" onmousedown="event.preventDefault(); event.stopPropagation();"  data-state="0" title="Stop screen share recording" aria-label="Stop screen share recording" alt="Stop screen recording" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" onclick="recordLocalScreenStopRecord();" class="hidden float" style="cursor: pointer;">
                        <small>Stop<br>Screen<br>Record</small>
                    </div>
                    <span id="miniPerformer" style="pointer-events: auto;" class="hidden"></span>
                    <span id="rooms" class="hidden" style="padding-top:3px;padding-left:6px;pointer-events: auto;color:#fff;"></span>
                    <span id="groups" class="hidden" style="padding-top:3px;padding-left:6px;pointer-events: auto;color:#fff;text-align: center;"></span>
                    <div id="hangupbutton2" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Cancel the Director's Video/Audio"  onclick="hangup2()" class="hidden float"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" style="cursor: pointer;" aria-label="stop publishing audio and video" alt="Disconnect Direcotor's cam">
                        <i class="toggleSize las la-phone rotate225" aria-hidden="true"></i>
                    </div>
                </div>
            </div>
		</div>
		<span id="miniTaskBar" style="float: right; bottom: 0px;right:0; position:fixed; display:flex;">
			<div id="closedList_connectUsers" class="hidden"  onclick="getById('connectUsers').classList.remove('hidden');getById('closedList_connectUsers').classList.add('hidden');">
				<i class="las la-theater-masks"></i>
			</div>
			</span>
		<div id="mainmenu" class="row" style="opacity: 0;">

			<!-- Landing Page Form -->
			<div id="landing-form" class="landing-container">
				<div class="landing-card">
					<div class="landing-header">
						<h1>Join Room</h1>
						<p>Enter your room details to get started</p>
					</div>
					<form id="roomForm" class="landing-form">
						<div class="form-group">
							<label for="roomNameInput">Room Name</label>
							<input type="text" id="roomNameInput" name="roomName" placeholder="Enter room name" required>
						</div>
						<div class="form-group">
							<label for="passwordInput">Password (Optional)</label>
							<input type="password" id="passwordInput" name="password" placeholder="Enter password">
						</div>
						<button type="submit" class="submit-btn">Start Camera Setup</button>
					</form>
				</div>
			</div>

			<div id="container-3" class="hidden" title="Add your Camera to OBS" onkeyup="enterPressedClick(event,this);"  alt="Add your Camera to OBS" tabindex="1" role="button" aria-pressed="false" class="column columnfade pointer card" onclick="previewWebcam()" style=" overflow-y: auto;">
				<h2 id="add_camera">
					<span data-translate="add-your-camera">Add your Camera to OBS</span>
				</h2>
				<div class="container-inner" id="add_camera_inner">
					<br />
					<p>
						<video id="previewWebcam" class="previewWebcam task" aria-hidden="true" title="Right-click this video for additional options" data-menu="context-menu-video" oncanplay="updateStats();" controlsList="nodownload" muted autoplay playsinline ></video>
					</p>
					<div id="infof"></div>
					<button onclick="this.disabled=true;setTimeout(function(){requestBasicPermissions();},20);" id="getPermissions" style="display:none;" data-ready="false" >
						<span data-translate="ask-for-permissions">Allow Access to Camera/Microphone</span>
					</button>
					<span style="display:block;">
						<button onclick="joinRoomAndStream(this)" title="Join room and start streaming" aria-label="Join room and start streaming" role="button" aria-pressed="false" tabindex="1" id="gowebcam" class="gowebcam" alt="Join Room and Start Streaming" disabled data-audioready="false" data-ready="false" >
							<span data-translate="waiting-for-camera">Join Room & Start Streaming</span>
						</button>
					</span>
					<div id="consentWarning" class="startupWarning hidden">
						<i class="las la-exclamation-circle"></i>
						<p><span data-translate="privacy-disabled">Privacy warning: The director will be able to remotely change your camera, microphone, and URL.</span></p>
					</div>
					<div id="guestTips" style="display:none" aria-hidden="true">
						<p data-translate="for-the-best-possible-experience-make-sure">For the best possible experience, make sure</p>
						<span><i class="las la-plug"></i><span data-translate="your-device-is-powered">Your device is powered</span></span>
						<span><i class="las la-ethernet"></i><span data-translate="your-connection-is-hardwired-instead-of-wifi">Your connection is hardwired instead of wifi</span></span>
						<span><i class="las la-headphones"></i><span data-translate="you-are-using-headphones-earphones">You are using headphones / earphones</span></span>
					</div>
					<div id="videoMenu" class="videoMenu" aria-hidden="true">
						<div class="title">
                            <i class="las la-video"></i><span data-translate="video-source"> Video Source </span>
                        </div>
						<span style="display:inline-block;padding-top: 5px;">
							<select id="videoSourceSelect" tabindex="1" title="Video source list"></select>
							<span id="gear_webcam" onclick="toggle(document.getElementById('videoSettings'));">
								<i class="las la-cog" style="font-size: 140%; vertical-align: middle;" aria-hidden="true"></i>
							</span>
						</span>
						<div id="cameraTip1" class="cameraTip hidden">
							<i class="las la-info-circle"></i>
							<p><span id="cameraTipContext1"></span></p>
						</div>
					</div>
					<br />
					<center>
						<div id="videoSettings" style="display: none;" aria-hidden="true">
							<form id="webcamquality">
								<span class="hidden">
									<input type="radio" id="4kquality" alt="2160p60 video capture" name="resolution" value="-2" />
									<label for="4kquality">
										<span data-translate="up-to-4k">4K</span>
									</label> |
								</span>
								
								<input type="radio" id="fullhd" alt="1080p60 video capture" name="resolution" value="0" />
								<label for="fullhd">
									<span data-translate="max-resolution">High Resolution</span>
								</label> |
								
								<input type="radio" checked id="halfhd" alt="720p60 video capture"  name="resolution" value="1" />
								<label for="halfhd">
									<span data-translate="balanced">Balanced</span>
								</label> |
								
								<input type="radio" id="nothd" name="resolution" alt="360p30 video capture"  value="2" />
								<label for="nothd">
									<span data-translate="smooth-cool">Smooth and Cool</span>
								</label>
								<div id="webcamstats" style="padding: 5px 0 0 0;"></div>
							</form>
						</div>
					</center>
					<div id="audioMenu" class="form-group multiselect" alt="tip: Hold CTRL (command) to select Multiple" title="tip: Hold CTRL (command) to select Multiple">
						<span class='gear_microphone hidden'>
							<input type="checkbox" id='micStereoMonoInput' alt="Mono microphone audio"  onchange="toggleMonoStereoMic(this);">Mono
						</span>
						<a id="multiselect-trigger" class="form-control multiselect-trigger" data-state="1">
							<div class="title">
								<i class="las la-microphone-alt"></i><span data-translate="select-audio-source"> Audio Source(s) </span>
								<i id='chevarrow1' class="chevron bottom" aria-hidden="true"></i>
								<div class="meter" id="meter1"></div><div class="meter2" id="meter2"></div>
							</div>
						</a>
						<ul id="audioSource" class="multiselect-contents" >
							<li>
								<input type="checkbox" id="multiselect1" name="multiselect1" style="display: none;" checked value="ZZZ" />
								<label for="multiselect1">
									<span data-translate="no-audio"> No Audio</span>
								</label>
							</li>
						</ul>
						<div id="audioTip1" class="cameraTip hidden">
							<i class="las la-info-circle"></i>
							<p><span id="audioTipContext1"></span></p>
						</div>
					</div>
					<br style="line-height: 0;" />
					<div id="headphonesDiv" class="audioMenu hidden" aria-hidden="true">
						<div class="title">
							<i class="las la-headphones"></i><span data-translate="select-output-source"> Audio Output Destination</span><button onclick="playtone()" title="Play a sound out of the selected audio playback device" class="testtonebutton" type="button">Test</button>
						</div>
						<select id="outputSource" alt="Audio output device" ></select>
						<div id="headphoneTip1" class="cameraTip hidden">
							<i class="las la-info-circle"></i>
							<p><span id="headphoneTipContext1"></span></p>
						</div>
						<div id="audioTipSR" class="cameraTip hidden">
							<i class="las la-exclamation-circle"></i>
							<p><span id="audioTipContextSR"></span></p>
						</div>
					</div>
					<br style="line-height: 0;" />
					<div id="avatarDiv" class="hidden" aria-hidden="true">
						<div class="title">
							<i class="las la-robot"></i><span data-translate="select-avatar-image"> Default Avatar / Placeholder Image </span>
						</div>
						<div id="selectAvatarImage" style="margin-top:10px;">
							<img src="./media/avatar.webp" crossOrigin="Anonymous" loading="lazy" id="defaultAvatar1" style="max-width:130px;max-height:73.5px;display:inline-block;margin:10px;cursor:pointer;" onclick="changeAvatarImage(event, this);"/>
							<label class="selected" id="noAvatarSelected" style="width:130px;display:inline-block;text-align: center; cursor:pointer;">
							  <i class="las la-minus-circle" style="font-size: 3em;"></i><br />No Image Selected
							  <button onclick="changeAvatarImage(event, this)" style="position: fixed; top: -100em; margin-left:10px; border:1px solid #555;"></button>
							</label>
							<label style="width:130px;display:inline-block; text-align: center; cursor:pointer;">
							  <i class="las la-hdd" style="font-size: 3em;"></i><br />Select Local Image
							  <input type="file" onchange="changeAvatarImage(event, this)" accept="image/*" style="position: fixed; top: -100em; margin-left:10px; border:1px solid #555;"> 
							</label>
						</div>
					</div>
					<br style="line-height: 0;" />
					<div id="effectsDiv">
						<div class="title">
							<i class="las la-robot"></i>
							<span data-translate="select-digital-effect"> Digital Video Effects </span>
						</div>
						<select id="effectSelector" alt="Digital video effect options" onchange="effectsDynamicallyUpdate(event, this);">
							<option value="0" data-translate="no-effects-applied">No effects applied</option>
							<option value="3" data-translate="blurred-background">Blurred background</option>
							<option value="13" class="hidden" disabled data-translate="blurred-background-2">Blurred background 2 🧪</option>
							<option value="4" data-translate="digital-greenscreen">Digital greenscreen</option>    
							<option value="5" data-translate="virtual-background">Virtual background</option>
							<option value="6" data-translate="face-mesh" title="experimental">Face mesh (slow load) 👨‍🔬</option>
							<option value="7" data-translate="digital-zoom">Digital zoom</option>
							<option value="overlay" data-translate="overlay-image">Overlay image</option>
							<option value="anon" data-translate="anonymous-mask" title="experimental">Anonymous mask 👨‍🔬</option>
							<option value="dog" data-translate="dog-face" title="experimental">Dog ears and nose 👨‍🔬</option>
							<option value="1" disabled title='Enable the Chrome experimental features flag to use: chrome://flags/#enable-experimental-web-platform-features' class='facetracker' data-translate="face-tracker">Face Tracker</option>
						</select>
						<span data-warnSimdNotice="true" style='display:none; font-size: 140%; margin-left:10px; vertical-align: middle; cursor:pointer' title="Improve performance and quality with this tip" onclick="smdInfo();">
							<i class="las la-info-circle"></i>
						</span>
						<span data-effectsNotice="true" style='display:none; font-size: 140%; margin-left:10px; vertical-align: middle; cursor:pointer' title="Improve performance and quality with this tip" onclick="warnUser('Use a Chromium Based Browser');">
							<i class="las la-info-circle"></i>
						</span>
						
						<div id="selectImageContent" style="display:none;margin-top:10px;"></div>
						<div id="selectImageOverlay" style="display:none;margin-top:10px;"></div>
						
						<div id="selectEffectAmount" style="display:none;margin-top:10px;">
							<label for="selectEffectAmountInput" style="width: 113px;display: inline-block;">Effect Amount</label>
							<input id="selectEffectAmountInput" style="display: inline-block;width: 350px; max-width: 60%;margin:6px 0;" 
								   name="selectEffectAmountInput" title="Adjust the amount of effect applied" type="range" 
								   oninput="changeEffectAmount(event, this)" onchange="changeEffectAmount(event, this)" 
								   min="0" step="1" max="20">
						</div>
						<div id="zoomPositionControls" style="display:none;margin-top:10px;">
							<div style="margin-bottom: 10px;">
								<label for="zoomPositionX1" style="width: 113px;display: inline-block;">Horizontal Position:</label>
								<input id="zoomPositionX1" style="display: inline-block;width: 350px; max-width: 60%;margin:6px 0;" 
									   type="range" oninput="changeZoomPosition(event, this)" onchange="changeZoomPosition(event, this)" 
									   min="0" max="1" step="0.01" value="0.5">
							</div>
							<div>
								<label for="zoomPositionY1" style="width: 113px;display: inline-block;">Vertical Position:</label>
								<input id="zoomPositionY1" style="display: inline-block;width: 350px; max-width: 60%;margin:6px 0;" 
									   type="range" oninput="changeZoomPosition(event, this)" onchange="changeZoomPosition(event, this)" 
									   min="0" max="1" step="0.01" value="0.5">
							</div>
						</div>
					</div>
					<br style="line-height: 0;" />
					<div id="addPasswordBasic">
                        <div class="title" title="Add an optional password">
                            <i class="las la-key"></i><span data-translate="add-a-password"> Add a Password</span>
                        </div>
                        <input type="text" id="passwordBasicInput" title="Enter an optional password here" placeholder="optional"/>
                    </div>
					
					<br style="line-height: 0;" />
					<div id="rememberStreamID" class="hidden" style="display:inline-block;" title="Remember and reuse the provided stream ID on each visit">
						<br />
						<br style="line-height: 0;" />
                        Remember Stream ID: <input type="checkbox" id="rememberStreamIDcheck" checked="true" />
                    </div>
					
					<div id="SafariWarning" class="startupWarning hidden" aria-hidden="true" title="Consider using Chrome instead of Safari">
						<i class="las la-exclamation-circle"></i>
						<p><span data-translate="use-chrome-instead">Consider using a Chromium-based browser instead.<br />
 						Safari is more prone to having audio issues</span></p>
					</div>
					
					<div id="oldiOSWarning" class="startupWarning hidden" title="Please update your version of iOS for best performance">
						<i class="las la-exclamation-circle"></i>
						<p><span data-translate="update-your-device">We've detected that you are using an old version of Apple iOS.<br /><br />Please consider updating if facing issues.</span></p>
					</div>
					
				</div>
				<div class="outer close" role="button" aria-pressed="false" title="Go back">
					<div class="inner">
						<label class="labelclass">
							<span data-translate="back">Back</span>
						</label>
					</div>
				</div>
			</div>

		</div>
	    <div id="hiddenElements"></div>
		<div id="overlayClockContainer" data-menu='context-menu-clock' data-initial="600" class="hidden"><span id="overlayClock"></span></div>
		<div id="overlayClockContainer2" data-menu='context-menu-clock' data-initial="600" class="hidden"><span id="overlayClock2"></span></div>
		<div id="overlayMsgs" onclick="this.innerHTML = '';" style="display:none"></div>
		<div id="stickyMsgs" class="hidden"></div>
		<div id="bigPlayButton" onclick="this.innerHTML = '';" style="display:none"></div>	
		<div id="popupSelector" style="display:none;">

				<span id="videoMenu3">
                    <div class="title">
                        <i class="las la-video"></i><span data-translate="video-source"> Video Source: </span>
                    </div>
					<span>
						<select id="videoSource3" ></select>			
						<span id="refreshVideoButton" title="Activate or Reload this video device.">
							<i class="las la-sync-alt"></i>
						</span>
						<span id="gear_webcam3" style="display: none; cursor:pointer;" onclick="toggleQualityGear3();">
							<i class="las la-cog" style="font-size: 135%; top:1px; vertical-align: middle;" aria-hidden="true"></i>
						</span>
					</span>
					<span id="videoSettings3" style="display: none;">
						<center>
						<form id="webcamquality3">
							<input type="radio" id="fullhd3" name="resolution" value="0" />
							<label for="fullhd3">
								<span data-translate="max-resolution">High Quality</span>
							</label> |
							
							<input type="radio" checked id="halfhd3" name="resolution" value="1" />
							<label for="halfhd3">
								<span data-translate="balanced">Balanced</span>
							</label> |
							
							<input type="radio" id="nothd3" name="resolution" value="2" />
							<label for="nothd3">
								<span data-translate="smooth-cool">Smooth and Cool</span>
							</label>
							<div id="webcamstats3" style="padding: 5px 0 0 0;"></div>
						</form>
						</center>
					</span>
				</span>
				
				<div id="audioMenu2" class="form-group multiselect" alt="tip: Hold CTRL (command) to select Multiple" title="tip: Hold CTRL (command) to select Multiple" >
					<span class='gear_microphone gearflat hidden'>
						<input type="checkbox" id='micStereoMonoInput3' onchange="toggleMonoStereoMic(this);">Mono
					</span>
					<a id="multiselect-trigger3" class="form-control multiselect-trigger" >
						<div id="title" class="title">
							<i class="las la-microphone-alt"></i><span data-translate="select-audio-source"> Audio Source(s) </span>
							<i id="chevarrow2" class="chevron bottom" aria-hidden="true"></i>
							<div class="meter" id="meter3"></div><div class="meter2" id="meter4"></div>
						</div>
					</a>
					<ul id="audioSource3" class="multiselect-contents">
						<li>
						</li>
					</ul>
					
				</div>
				<br />
				<span id="headphonesDiv3" style="display: block;">
					<div class="title">
						<i class="las la-headphones"></i>
						<span data-translate="select-output-source"> Audio Output Destination: </span>
						<button onclick="playtone()" class="testtonebutton" type="button">Test</button>
					</div>
					<select id="outputSource3" ></select>
				</span>
				
				<div id="avatarDiv3" class="hidden">
						<div style="text-align: left;display: inline-block;">
							<i class="las la-robot"></i><span data-translate="select-avatar-image"> Default Avatar / Placeholder Image: </span>
						</div>
						<div id="selectAvatarImage3" style="margin-top:10px;">
							<img src="./media/avatar.webp" crossOrigin="Anonymous"  loading="lazy" id="defaultAvatar2" style="max-width:130px;max-height:73.5px;display:inline-block;margin:10px;cursor:pointer;" onclick="changeAvatarImage(event, this);"/>
							<label class="selected avatarSelection" id="noAvatarSelected3">
							  <i class="las la-minus-circle" style="font-size: 3em;"></i><br />No Image Selected
							  <button onclick="changeAvatarImage(event, this)" style="position: fixed; top: -100em; margin-left:10px; border:1px solid #555;"></button>
							</label>
							<label class="avatarSelection">
							  <i class="las la-hdd" style="font-size: 3em;"></i><br />Select Local Image
							  <input type="file" onchange="changeAvatarImage(event, this)" accept="image/*" style="position: fixed; top: -100em; margin-left:10px; border:1px solid #555;"> 
							</label>
						</div>
					</div>
				
				<div id="effectsDiv3" class="effects-controls" style="display: none;">
					<div class="title" role="heading" aria-level="2">
						<i class="las la-robot" aria-hidden="true"></i>
						<span data-translate="select-digital-effect"> Digital Video Effects: </span>
					</div>
					<div class="effect-selector-group">
						<label for="effectSelector3">Select Effect:</label>
						<select id="effectSelector3" onchange="effectsDynamicallyUpdate(event, this);">
							<option value="0" data-translate="no-effects-applied">No effects applied</option>
							<option value="3" data-translate="blurred-background">Blurred background</option>
							<option value="13" class="hidden" disabled data-translate="blurred-background-2">Blurred background 2 🧪</option>
							<option value="4" data-translate="digital-greenscreen">Digital greenscreen</option>    
							<option value="5" data-translate="virtual-background">Virtual background</option>
							<option value="6" data-translate="face-mesh" title="experimental">Face mesh (slow load) 👨‍🔬</option>
							<option value="7" data-translate="digital-zoom">Digital zoom</option>
							<option value="overlay" data-translate="overlay-image">Overlay image</option>
							<option value="14" data-translate="remove-green">Remove Chroma Green</option>
							<option value="anon" data-translate="anonymous-mask" title="experimental">Anonymous mask 👨‍🔬</option>
							<option value="dog" data-translate="dog-face" title="experimental">Dog ears and nose 👨‍🔬</option>
							<option value="1" disabled title='Face Detection API not detected' class='facetracker' data-translate="face-tracker">Face tracker</option>
						</select>
					</div>
					<div id="selectEffectAmount3" style="display:none;" role="group" aria-label="Effect Amount Control">
						<label for="selectEffectAmountInput3" id="effectAmountLabel">Effect amount:</label>
						<input id="selectEffectAmountInput3" 
							   type="range"
							   aria-labelledby="effectAmountLabel"
							   oninput="changeEffectAmount(event, this)" 
							   onchange="changeEffectAmount(event, this)" 
							   min="0" 
							   step="1" 
							   max="20"
							   class="effect-slider">
					</div>
					<div id="zoomPositionControls3" style="display:none;" role="group" aria-label="Digital Zoom Controls">
						<div class="zoom-control-group">
							<label for="zoomPositionX" id="zoomPosXLabel">Horizontal Position:</label>
							<input id="zoomPositionX" 
								   type="range" 
								   aria-labelledby="zoomPosXLabel"
								   oninput="changeZoomPosition(event, this)" 
								   onchange="changeZoomPosition(event, this)" 
								   min="0" 
								   max="1" 
								   step="0.01" 
								   value="0.5"
								   class="zoom-slider">
							<output for="zoomPositionX" class="zoom-value"></output>
						</div>
						
						<div class="zoom-control-group">
							<label for="zoomPositionY" id="zoomPosYLabel">Vertical Position:</label>
							<input id="zoomPositionY" 
								   type="range" 
								   aria-labelledby="zoomPosYLabel"
								   oninput="changeZoomPosition(event, this)" 
								   onchange="changeZoomPosition(event, this)" 
								   min="0" 
								   max="1" 
								   step="0.01" 
								   value="0.5"
								   class="zoom-slider">
							<output for="zoomPositionY" class="zoom-value"></output>
						</div>
					</div>
				</div>
				<button id="shareScreenGear" style="width: 135px; padding:20px;text-align:center;" onclick="grabScreen()"><b>Share Screen</b><br /><i style="padding:5px; font-size:300%;" class="las la-desktop"></i></button>
				
				<button id="pIpStartButton" style="width: 135px; background-color:#EFEFEF;padding:20px;text-align:center;display:none;"><b>Preview PiP VIdeo</b><br /><i style="padding:5px; font-size:300%;color:black;" class="las la-compress-arrows-alt"></i></button>
				
				
				<div class="hidden" id="grabDirectorSoloLinkParent" title="The solo view link of the Director's video.">
                    <div class="title">
                        <i class="las la-user"></i> Director's solo link:
                    </div>
                    <a onclick="copyFunction(this,event)" data-drag="1" draggable="true" id="grabDirectorSoloLink" data-menu="context-menu" class="task" ></a>
                </div>
				<br />
				<button onclick="toggleSettings()" class="toggleSettings"><i class="las la-chevron-right"></i><b><span data-translate="close-settings">Close Settings</span></b></button>
				
				<button id='advancedOptionsAudio' onclick="toggleAudioUser(this);" class="advancedToggle">
				<i class="las la-sliders-h" style="position:relative;"></i> <b>Audio</b></button>
				
				<button id='advancedOptionsCamera' onclick="toggleVideoUser(this);" class="advancedToggle">
				<i class="las la-sliders-h" style="position:relative;"></i> <b>Video</b></button>
				
				<button id='advancedOptionsGeneral' onclick="toggleUserUser(this);" >
				<i class="las la-user-cog" style="position:relative;"></i> <b><span data-translate="user">User</span></b></button>
				
				
				<span id="popupSelector_constraints_audio" class="popupSelector_constraints" style="display: none;">
				
				</span>
				<span id="popupSelector_constraints_video" class="popupSelector_constraints" style="display: none;">
				
				</span>
				<span id="popupSelector_constraints_loading" style="display: none; visibility:hidden">
					<i class='las la-spinner icn-spinner' style='margin:30px;font-size:400%;color:white;'></i>
				</span>
				
				<span id="popupSelector_user_settings" style="display: none; visibility:hidden">
					<span class="mobileHide">
						<label title="Choose a hotkey for Hold-to-Talk. If using Electron Capture, elevate privilleges to have it become global" data-translate="hold-to-talk" >Hold-to-Talk Hot-key</label>
						<input type="text" id="pptHotKey" placeholder="press a key here" style="padding-left: 7px;" onkeypress="event.preventDefault;event.stopPropagation();return false;" onkeyup="event.preventDefault;event.stopPropagation();return false;" onkeydown="setHotKey();"/>
						<button onclick="setHotKey(false);" style="margin: 0 0 0 4px; border-radius: 5px; padding: 3px 3px;" data-translate="clear">Clear</button>
						<br /><br />
						<label title="Draw on the Screen">Draw-on-Screen</label>
						<button id='startDrawScreen' title="CTRL (cmd) + ALT + D to toggle" onclick="drawOnScreen();" style="margin: 0 0 0 4px; border-radius: 5px; padding: 3px 3px;" data-translate="enable" >Enable</button>
						<button class='hidden drawActive' onclick="drawOnScreenObject.erase();" style="margin: 0 0 0 4px; border-radius: 5px; padding: 3px 3px;" data-translate="clear">Clear</button>
						<button class='hidden drawActive' onclick="drawOnScreenObject.stop();" style="margin: 0 0 0 4px; border-radius: 5px; padding: 3px 3px;" title="CTRL (cmd) + ALT + D to toggle" data-translate="stop">Stop</button>
						<button class='hidden drawActive' onclick="drawOnScreenObject.color(this);" data-color='red' style="background-color:red; margin: 0 0 0 4px; border-radius: 5px; padding: 3px 3px;">✏️</button>
						<button class='hidden drawActive' onclick="drawOnScreenObject.color(this);" data-color='blue' style="background-color:blue; margin: 0 0 0 4px; border-radius: 5px; padding: 3px 3px;">✏️</button>
						<button class='hidden drawActive' onclick="drawOnScreenObject.color(this);" data-color='green' style="background-color:green; margin: 0 0 0 4px; border-radius: 5px; padding: 3px 3px;">✏️</button>
						<button class='hidden drawActive' onclick="drawOnScreenObject.color(this);" data-color='black' style="background-color:black; margin: 0 0 0 4px; border-radius: 5px; padding: 3px 3px;">✏️</button>
						<button class='hidden drawActive' onclick="drawOnScreenObject.color(this);" data-color='white' style="background-color:white; margin: 0 0 0 4px; border-radius: 5px; padding: 3px 3px;">🎨</button>
						<br /><br />
						<button class="generalButton" onclick="cycleStyleOptions();" id='toggleWaveformButton' title="Audio-only sources can be stylized in different ways" data-translate="cycle-between-audio-visualizations">Cycle between several audio-visualizations styles</button>
						<br />
					</span>
					<button class="generalButton" onclick="clearStorage();" title="Clear site's local storage and settings" data-translate="cleaer-sites-local-storage">Clear site's local browser storage and saved settings</button>
				</span>
		</div>
		<nav id="context-menu" class="context-menu">
			<ul class="context-menu__items">
				<li class="context-menu__item">
					<a href="#" class="context-menu__link" data-action="Open">
						<i class="las la-external-link"></i>
						<span data-translate="open-in-new-tab">Open in new tab</span>
					</a>
				</li>
				<li class="context-menu__item">
					<a href="#" class="context-menu__link" data-action="Copy">
						<i class="las la-paperclip"></i>
						<span data-translate="copy-to-clipboard" >Copy to clipboard</span>
					</a>
				</li>
				<li class="context-menu__item">
					<a href="#" class="context-menu__link" data-action="Edit">
						<i class="las la-pen"></i>
						<span data-translate="edit-url" >Edit URL manually</span>
					</a>
				</li>
				<li class="context-menu__item hidden">
					<a href="#" class="context-menu__link" data-action="Publish">
						<i class="las la-tv"></i>
						<span data-translate="publish-url" >Publish via WHIP</span>
					</a>
				</li>
				<li class="context-menu__item hidden">
					<a href="#" class="context-menu__link" data-action="RecordWindow">
						🔴
						<span data-translate="record-window" >Record as a Window</span>
					</a>
				</li>
				<li class="context-menu__item">
					<a href="#" class="context-menu__link" data-action="QRCode">
						📷
						<span data-translate="show-qr-code" >Show as QR Code</span>
					</a>
				</li>
			</ul>
		</nav>
		<nav id="context-menu-screen-share" class="context-menu">
			<ul class="context-menu__items">
				<li class="context-menu__item">
					<a href="#" class="context-menu__link" data-action="SSNewTab">
						<i class="las la-external-link"></i>
						<span data-translate="open-ss-in-new-tab">Share from a new tab</span>
					</a>
				</li>
				<li class="context-menu__item hidden">
					<a href="#" class="context-menu__link" data-action="ss1">
						1📷
						<span data-translate="ss-mode-1" >Screen Share Mode 1</span>
					</a>
				</li>
				<li class="context-menu__item hidden">
					<a href="#" class="context-menu__link" data-action="ss2">
						2📷
						<span data-translate="ss-mode-2" >Screen Share Mode 2</span>
					</a>
				</li>
				<li class="context-menu__item hidden">
					<a href="#" class="context-menu__link" data-action="ss3">
						3📷
						<span data-translate="ss-mode-3" >Screen Share Mode 3</span>
					</a>
				</li>
			</ul>
		</nav>
		<nav id="context-menu-clock" class="context-menu">
			<ul class="context-menu__items">
				<li class="context-menu__item">
					<a href="#" class="context-menu__link" data-action="pip-clock">
						<i class="las la-external-link"></i>
						<span data-translate="detach-clock2-pip">Pop-out clock toggle</span>
					</a>
				</li>
			</ul>
		</nav>
		<nav id="context-menu-video" class="context-menu">
			<ul class="context-menu__items">
				<li class="context-menu__item">
					<a href="#" class="context-menu__link" data-action="Mirror">
						<i class="las la-external-link"></i>
						<span data-translate="mirror-video">Mirror Video</span>
					</a>
				</li>
				<li class="context-menu__item">
					<a href="#" class="context-menu__link" data-action="Rotate">
						<i class="las la-external-link"></i>
						<span data-translate="rotate-video">Rotate Video</span>
					</a>
				</li>
				<li class="context-menu__item">
					<a href="#" class="context-menu__link" data-action="Controls">
						<i class="las la-external-link"></i>
						<span data-translate="show-controls-video">Show control bar</span>
					</a>
				</li>
				<li class="context-menu__item hidden">
					<a href="#" class="context-menu__link" data-action="HideControls">
						<i class="las la-external-link"></i>
						<span data-translate="hide-controls-video">Hide control bar</span>
					</a>
				</li>
				<li class="context-menu__item">
					<a href="#" class="context-menu__link" data-action="PiP">
						<i class="las la-external-link"></i>
						<span data-translate="picture-in-picture">Picture-in-picture</span>
					</a>
				</li>
				<li class="context-menu__item hidden">
					<a href="#" class="context-menu__link" data-action="PiP2">
						<i class="las la-external-link"></i>
						<span data-translate="picture-in-picture-all">Picture-in-picture all</span>
					</a>
				</li>
				<li class="context-menu__item">
					<a href="#" class="context-menu__link" data-action="FullWindow">
						<i class="las la-external-link"></i>
						<span data-translate="full-window">Full-window</span>
					</a>
				</li>
				<li class="context-menu__item hidden">
					<a href="#" class="context-menu__link" data-action="ShrinkWindow">
						<i class="las la-external-link"></i>
						<span data-translate="shrink-window">Shrink-window</span>
					</a>
				</li>
				<li class="context-menu__item">
					<a href="#" class="context-menu__link" data-action="Pause">
						<i class="las la-external-link"></i>
						<span data-translate="pause-stream">Pause stream</span>
					</a>
				</li>
				<li class="context-menu__item hidden">
					<a href="#" class="context-menu__link" data-action="UnPause">
						<i class="las la-external-link"></i>
						<span data-translate="resume-stream">Resume stream</span>
					</a>
				</li>
				<li class="context-menu__item">
					<a href="#" class="context-menu__link" data-action="Record">
						<i class="las la-external-link"></i>
						<span data-translate="record-to-disk">Record to disk</span>
					</a>
				</li>
				<li class="context-menu__item hidden">
					<a href="#" class="context-menu__link" data-action="StopRecording">
						<i class="las la-external-link"></i>
						<span data-translate="stop-record-to-disk">Stop Recording</span>
					</a>
				</li>
				<li class="context-menu__item hidden">
					<a href="#" class="context-menu__link" data-action="CopyFrameAsImage">
						<i class="las la-external-link"></i>
						<span data-translate="copy-to-clipboard-frame">Snapshot to clipboard</span>
					</a>
				</li>
				<li class="context-menu__item hidden">
					<a href="#" class="context-menu__link" data-action="SaveFrameToDisk">
						<i class="las la-external-link"></i>
						<span data-translate="save-current-frame">Save frame to disk</span>
					</a>
				</li>
				<li class="context-menu__item">
					<a href="#" class="context-menu__link" data-action="DrawOnVideo">
						<i class="las la-external-link"></i>
						<span data-translate="draw-on-video">Toggle draw mode</span>
					</a>
				</li>
				<li class="context-menu__item">
					<a href="#" class="context-menu__link" data-action="ShowStats">
						<i class="las la-external-link"></i>
						<span data-translate="show-video-stats">Show Stats</span>
					</a>
				</li>
				<li class="context-menu__item" title="Using this may cause audio-issues on some systems">
					<a href="#" class="context-menu__link" data-action="OutputAudio">
						<i class="las la-external-link"></i>
						<span data-translate="custom-audio-output">Audio Destination</span>
					</a>
				</li>
				<li class="context-menu__item hidden" id="RemoteHangupContextOption">
					<a href="#" class="context-menu__link" data-action="RemoteHangup">
						<i class="las la-external-link"></i>
						<span data-translate="remote-hangup-connection">Remote Hang-up</span>
					</a>
				</li>
				<li class="context-menu__item hidden" id="RemoteReloadContextOption">
					<a href="#" class="context-menu__link" data-action="RemoteReload">
						<i class="las la-external-link"></i>
						<span data-translate="remote-reload-connection">Remote Reload Page</span>
					</a>
				</li>
				<li class="context-menu__item">
					<a href="#" class="context-menu__link" data-action="ChangeBuffer">
						<i class="las la-external-link"></i>
						<span data-translate="change-playout-buffer">Change Buffer</span>
					</a>
				</li>
				<li class="context-menu__item hidden">
					<hr />
					<span href="#" class="context-menu__tip" data-action="TipRightClick">
						<small><i data-translate="hold-ctrl">tip:  <b>CTRL</b>(⌘) + <b>Click</b> for alt-menu</i></small>
					</span>
				</li>
			</ul>
		</nav>			
		<div id="connectUsers">
			<div style="margin-bottom:5px"><u ><span data-translate="invisible-guests">Not Visible</span></u> 
			<span title="Hide this window" id='hideusers' style="cursor:pointer;font-size:1.2em; float: right;" onclick="getById('connectUsers').classList.add('hidden');getById('closedList_connectUsers').classList.remove('hidden');"><i class="las la-times"></i></span></div>
			<span style="height:5px;display:block;"></span>
			<div id="userList">
			</div>
		</div>	
		<div id="callerMenu" class="hidden">
			<button><i class="las la-phone"></i> Hang up</button>
		</div>		
		<div id="progressContainer" class="hidden">
			<div id="progressBar"></div>
		</div>
		
		<div id="gridlayout"></div>
		<audio id="testtone" style="display:none;" preload="none">
			<source src="./media/tone.mp3" type="audio/mpeg"> 
			<source src="./media/tone.ogg" type="audio/ogg"> 
		</audio>
		<div class="gone" >
			<!-- This image is used when dragging elements -->
			<img src="./media/favicon-32x32.png" id="dragImage" loading="lazy" />
		</div>
		<div id="request_info_prompt" style="display:none">
		</div>
		<div id="screenPopup" class="popup-screen">
			<button onclick="getById('screenPopup').style.display='none';margin:0;padding:0;">Close Window</button>
			<div>See the 
				<a style="text-decoration: none; color: blue;" target="_blank" href="https://docs.vdo.ninja/advanced-settings">documentation</a> for more options and info.
			</div>
		</div>
		<div id="messagePopup" class="popup-message"></div>
		
		<span class="hidden" id="hangupTemplate">
			<span id="hangupContainer">👋<br><button onClick='parent.location.reload();' title="Reload the page" data-translate="reload-page">Refresh</button></span>
		</span>
		
		<div aria-hidden="true" style="height: 0; width: 0; position: absolute; overflow: hidden; margin: -1px; clip: rect(0 0 0 0);">
			<a href="/rawdoc.md" type="text/markdown" rel="documentation">Full and User Documentation</a>
		</div>
		<script>
		
			if (window.location.hostname.indexOf("www.obs.ninja") == 0) {
				window.location = window.location.href.replace("www.obs.ninja","obs.ninja"); // the session.salt is domain specific; let's consolidate www as a result.
			} else if (window.location.hostname.indexOf("www.vdo.ninja") == 0) {
				window.location = window.location.href.replace("www.vdo.ninja","vdo.ninja"); // the session.salt is domain specific; let's consolidate www as a result.
			} else if (("isSecureContext" in window) && (window.isSecureContext===false)){
				console.error(" * Warning *\n\n - This site should be run in a secure context (https).\n - Please ensure all links, iframes, and parent windows are using SSL.\n - The webcam and other media devices may not be accessible while insecure.\n - WHEP playback and some other features might still function.");
			}
			
			console.log("->> Note: Do not paste code into this console unless from a trusted source <<--")

			
			var session = WebRTC.Media; // session is a required global variable if configuring manually. Run before loading main.js but after webrtc.js.
			session.version = "27.0";
			session.streamID = session.generateStreamID(); // randomly generates a streamID for this session. You can set your own programmatically if needed
			
			session.defaultPassword = "someEncryptionKey123"; // Change this password if self-deploying for added security/privacy
			// session.salt = location.hostname; // used only if password is not == False. You can change to "session.salt = location.hostname+location.pathname;" for greater deployment isolation
			
			session.stunServers = [{ urls: ["stun:stun.l.google.com:19302", "stun:stun.cloudflare.com:3478"]}]; // google + cloudflare stun servers. default
			
			
			///// The following lets you set the defaults

			// session.webcamonly // true,false
			// session.stereo // 0,1,2,3,4,5
			// session.audiobitrate // int in kbps
			// session.view // "xxxx" ; the stream ID or a list of Stream IDs to Connect to. Not the same as &noaudio/&novideo. Set to "" (empty) if you don't wish to connect to any.
			// session.remote // See docs, but allows for remote stats monitoring and remote focus/zoom control
			// session.optimize  // Whether to optimize invisible scenes in OBS. See docs.vdo.ninja. Setting to 0 will pause the video/audio when not visible in OBS
			// session.disableOBS // If true, will disable any OBS-specific events/functions.
			// session.noaudio // False by default, otherwise specify a list [] of stream IDs to allow. Listing none allows no incoming audio streams
			// session.novideo // False by default, otherwise specify a list [] of stream IDs to allow. Listing none allows no incoming video streams
			// session.forceios // If true, will allow iOS devices to send H264 video to other guests in a room
			// session.nocursor // hides the cursor using CSS
			// session.codec // default codec; maybe h264 is useful? the default is up to the browser normally
			// session.scale // By default, scale is self-optimizing, but you can set a value of 1 to 100 to choose the playback scale of all incoming video streams
			// session.bitrate  // int in kbps -- you can set the default max target bitrate here
			// session.totalRoomBitrate = 500; // int, kbps -- you can set the default quality of the group room here
			// session.height // int  ; height to publish a video stream at.  Will fail if not supported by the camera
			// session.width // int ; see above
			// session.quality // int  -- if setting == 0, then than the default resolution will be 1080p, instead of 720p60 (q=1) , while  q=2 = 360p30.
			// session.sink // Output device to playback audio to. see the docs
			// session.offsetChannel // int
			// session.audioChannels // int
			// session.security // true to disable the wss connection after the first peer connection is made
			// session.frameRate // int ; publishing frame rate. will fail if camera does not support it.
			// session.sync // see the docs
			// session.buffer // int in milliseconds ; see the docs
			// session.roomid // "yyyy"  -- the room name to use.  alphanumeric.
			// session.password = null; // null will ask the user for a password
			// session.scene // the scene name. Scene 0, 1, ... 8, or any custom scene name is supported.  STRING value. Needed to have a clean view link of a guest stream
			// session.title // "zzzz"   ; sets the title of the browser page.
			// session.introOnClean = true; // this will load the page with the webcam selection screen if &push or &room is in the URL; no need to use &webcam.
			// session.lowBitrateCutoff = 300; // Set a minimum bitrate (in kbps) before the stream is hidden. Useful for IRL streams maybe
			// session.apiserver = "wss://api.vdo.ninja:443"; // specifiy a custom websocket API URL.
			// session.darkmode = false; // enable or disable the dark style theme as the default
			// session.defaultBackgroundImages = ["./media/bg_sample1.webp", "./media/bg_sample2.webp"]; // for &effects=5 (virtual backgrounds)
			
			// session.language="auto"; // "blank" is another option, or a specific language, like "de" or "pt-br"
			// session.record = false; // uncomment to block users from being able to record via vdo.ninja's built in recording function
			// session.whipServerURL = "wss://whip.vdo.ninja"; // If you deploy your own whip websocket service
			// session.mediamtx = "youdomain.com:443"; // Your hosted MediaMTX SFU domain. Assumes HTTPS-enabled.
			// session.GDRIVE_CLIENT_ID = "877199999934-67tq62xxxxxxxxxxxxxxxxxxxxxxxxx.apps.googleusercontent.com"; // get your own id/key from Google Cloud
			// session.GDRIVE_API_KEY = 'AINNNNNNNNNNNNNNN-39s99999999999999999'; // lets you upload to google drive if self hosting.
			// session.decrypted = session.decodeInvite("U2FsdGVkX1+d58DFIHoO3EQZSuX86ch4PqW2ouztnJ0="); // get a code from invite.cam
			// session.hidehome = true; // If used, 'hide home' will make the landing page inaccessible, along with hiding a few go-home elements.
			// if ("42" != prompt("enter the password")){document.body.innerHTML = "";window.location.href = "";} // if you want a low-effort password for the site

			// Landing page form handling
			document.addEventListener('DOMContentLoaded', function() {
				const roomForm = document.getElementById('roomForm');
				const landingForm = document.getElementById('landing-form');
				const container3 = document.getElementById('container-3');
				const mainmenu = document.getElementById('mainmenu');

				// Show the main menu with opacity animation
				setTimeout(() => {
					mainmenu.style.opacity = '1';
				}, 100);

				roomForm.addEventListener('submit', function(e) {
					e.preventDefault();

					const roomName = document.getElementById('roomNameInput').value.trim();
					const password = document.getElementById('passwordInput').value.trim();

					if (!roomName) {
						alert('Please enter a room name');
						return;
					}

					// Set the room name and password in the existing password input
					if (password) {
						document.getElementById('passwordBasicInput').value = password;
					}

					// Hide landing form and show camera setup
					landingForm.style.display = 'none';
					container3.classList.remove('hidden');
					container3.style.display = 'block';

					// Set room parameters for VDO.Ninja
					if (typeof session !== 'undefined') {
						session.roomid = roomName;
						if (password) {
							session.password = password;
						}
					}

					// Trigger the camera preview
					setTimeout(() => {
						if (typeof previewWebcam === 'function') {
							previewWebcam();
						}
					}, 500);
				});

				// Custom function to handle room joining when Start Streaming is clicked
				window.joinRoomAndStream = function(button) {
					const roomName = document.getElementById('roomNameInput').value.trim();
					const password = document.getElementById('passwordInput').value.trim();

					if (roomName && typeof session !== 'undefined') {
						// Build the room URL
						let roomUrl = window.location.origin + window.location.pathname;
						roomUrl += `?room=${encodeURIComponent(roomName)}&webcam`;

						if (password) {
							roomUrl += `&password=${encodeURIComponent(password)}`;
						}

						// Navigate to the room
						window.location.href = roomUrl;
					} else {
						// Fallback to original publishWebcam function
						if (typeof publishWebcam === 'function') {
							publishWebcam(button);
						}
					}
				};
			});

		</script>
		<script type="text/javascript" crossorigin="anonymous" id="lib-js" src="./lib.js?ver=1294"></script>
		<script type="text/javascript" crossorigin="anonymous" id="main-js" src="./main.js?ver=954"></script>
	</body>
</html>
