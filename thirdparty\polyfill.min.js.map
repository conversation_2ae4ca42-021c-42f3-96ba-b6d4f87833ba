{"version": 3, "file": "polyfill.min.js", "sources": ["../src/stub/symbol.ts", "../src/utils.ts", "../src/lib/helpers/miscellaneous.ts", "../src/lib/helpers/webidl.ts", "../src/lib/simple-queue.ts", "../src/lib/readable-stream/generic-reader.ts", "../src/lib/abstract-ops/internal-methods.ts", "../src/stub/number-isfinite.ts", "../src/stub/math-trunc.ts", "../src/lib/validators/basic.ts", "../src/lib/validators/readable-stream.ts", "../src/lib/readable-stream/default-reader.ts", "../src/target/es5/stub/async-iterator-prototype.ts", "../src/lib/readable-stream/async-iterator.ts", "../src/stub/number-isnan.ts", "../src/lib/abstract-ops/miscellaneous.ts", "../src/lib/abstract-ops/queue-with-sizes.ts", "../src/lib/abstract-ops/ecmascript.ts", "../src/lib/readable-stream/byte-stream-controller.ts", "../src/lib/readable-stream/byob-reader.ts", "../src/lib/abstract-ops/queuing-strategy.ts", "../src/lib/validators/queuing-strategy.ts", "../src/lib/validators/underlying-sink.ts", "../src/lib/validators/writable-stream.ts", "../src/lib/writable-stream.ts", "../src/stub/native.ts", "../src/stub/dom-exception.ts", "../src/lib/readable-stream/pipe.ts", "../src/lib/readable-stream/default-controller.ts", "../src/lib/validators/underlying-source.ts", "../src/lib/validators/reader-options.ts", "../src/lib/validators/pipe-options.ts", "../src/lib/abort-signal.ts", "../src/lib/readable-stream.ts", "../src/lib/validators/readable-writable-pair.ts", "../src/lib/readable-stream/tee.ts", "../src/lib/validators/iterator-options.ts", "../src/lib/validators/queuing-strategy-init.ts", "../src/lib/byte-length-queuing-strategy.ts", "../src/lib/count-queuing-strategy.ts", "../src/lib/validators/transformer.ts", "../src/lib/transform-stream.ts", "../src/polyfill.ts"], "sourcesContent": ["/// <reference lib=\"es2015.symbol\" />\n\nconst SymbolPolyfill: (description?: string) => symbol =\n  typeof Symbol === 'function' && typeof Symbol.iterator === 'symbol' ?\n    Symbol :\n    description => `Symbol(${description})` as any as symbol;\n\nexport default SymbolPolyfill;\n", "/// <reference lib=\"dom\" />\n\nexport function noop() {\n  // do nothing\n}\n\nfunction getGlobals() {\n  if (typeof self !== 'undefined') {\n    return self;\n  } else if (typeof window !== 'undefined') {\n    return window;\n  } else if (typeof global !== 'undefined') {\n    return global;\n  }\n  return undefined;\n}\n\nexport const globals = getGlobals();\n", "import { noop } from '../../utils';\nimport { AssertionError } from '../../stub/assert';\n\nexport function typeIsObject(x: any): x is object {\n  return (typeof x === 'object' && x !== null) || typeof x === 'function';\n}\n\nexport const rethrowAssertionErrorRejection: (e: any) => void =\n  DEBUG ? e => {\n    // Used throughout the reference implementation, as `.catch(rethrowAssertionErrorRejection)`, to ensure any errors\n    // get shown. There are places in the spec where we do promise transformations and purposefully ignore or don't\n    // expect any errors, but assertion errors are always problematic.\n    if (e && e instanceof AssertionError) {\n      setTimeout(() => {\n        throw e;\n      }, 0);\n    }\n  } : noop;\n", "import { globals } from '../../utils';\nimport { rethrowAssertionErrorRejection } from './miscellaneous';\nimport assert from '../../stub/assert';\n\nconst originalPromise = Promise;\nconst originalPromiseThen = Promise.prototype.then;\nconst originalPromiseResolve = Promise.resolve.bind(originalPromise);\nconst originalPromiseReject = Promise.reject.bind(originalPromise);\n\nexport function newPromise<T>(executor: (\n  resolve: (value?: T | PromiseLike<T>) => void,\n  reject: (reason?: any) => void\n) => void): Promise<T> {\n  return new originalPromise(executor);\n}\n\nexport function promiseResolvedWith<T>(value: T | PromiseLike<T>): Promise<T> {\n  return originalPromiseResolve(value);\n}\n\nexport function promiseRejectedWith<T = never>(reason: any): Promise<T> {\n  return originalPromiseReject(reason);\n}\n\nexport function PerformPromiseThen<T, TResult1 = T, TResult2 = never>(\n  promise: Promise<T>,\n  onFulfilled?: (value: T) => TResult1 | PromiseLike<TResult1>,\n  onRejected?: (reason: any) => TResult2 | PromiseLike<TResult2>): Promise<TResult1 | TResult2> {\n  // There doesn't appear to be any way to correctly emulate the behaviour from JavaScript, so this is just an\n  // approximation.\n  return originalPromiseThen.call(promise, onFulfilled, onRejected) as Promise<TResult1 | TResult2>;\n}\n\nexport function uponPromise<T>(\n  promise: Promise<T>,\n  onFulfilled?: (value: T) => void | PromiseLike<void>,\n  onRejected?: (reason: any) => void | PromiseLike<void>): void {\n  PerformPromiseThen(\n    PerformPromiseThen(promise, onFulfilled, onRejected),\n    undefined,\n    rethrowAssertionErrorRejection\n  );\n}\n\nexport function uponFulfillment<T>(promise: Promise<T>, onFulfilled: (value: T) => void | PromiseLike<void>): void {\n  uponPromise(promise, onFulfilled);\n}\n\nexport function uponRejection(promise: Promise<unknown>, onRejected: (reason: any) => void | PromiseLike<void>): void {\n  uponPromise(promise, undefined, onRejected);\n}\n\nexport function transformPromiseWith<T, TResult1 = T, TResult2 = never>(\n  promise: Promise<T>,\n  fulfillmentHandler?: (value: T) => TResult1 | PromiseLike<TResult1>,\n  rejectionHandler?: (reason: any) => TResult2 | PromiseLike<TResult2>): Promise<TResult1 | TResult2> {\n  return PerformPromiseThen(promise, fulfillmentHandler, rejectionHandler);\n}\n\nexport function setPromiseIsHandledToTrue(promise: Promise<unknown>): void {\n  PerformPromiseThen(promise, undefined, rethrowAssertionErrorRejection);\n}\n\nexport const queueMicrotask: (fn: () => void) => void = (() => {\n  const globalQueueMicrotask = globals && globals.queueMicrotask;\n  if (typeof globalQueueMicrotask === 'function') {\n    return globalQueueMicrotask;\n  }\n\n  const resolvedPromise = promiseResolvedWith(undefined);\n  return (fn: () => void) => PerformPromiseThen(resolvedPromise, fn);\n})();\n\nexport function reflectCall<T, A extends any[], R>(F: (this: T, ...args: A) => R, V: T, args: A): R {\n  if (typeof F !== 'function') {\n    throw new TypeError('Argument is not a function');\n  }\n  return Function.prototype.apply.call(F, V, args);\n}\n\nexport function promiseCall<T, A extends any[], R>(F: (this: T, ...args: A) => R | PromiseLike<R>,\n                                                   V: T,\n                                                   args: A): Promise<R> {\n  assert(typeof F === 'function');\n  assert(V !== undefined);\n  assert(Array.isArray(args));\n  try {\n    return promiseResolvedWith(reflectCall(F, V, args));\n  } catch (value) {\n    return promiseRejectedWith(value);\n  }\n}\n", "import assert from '../stub/assert';\n\n// Original from Chromium\n// https://chromium.googlesource.com/chromium/src/+/0aee4434a4dba42a42abaea9bfbc0cd196a63bc1/third_party/blink/renderer/core/streams/SimpleQueue.js\n\nconst QUEUE_MAX_ARRAY_SIZE = 16384;\n\ninterface Node<T> {\n  _elements: T[];\n  _next: Node<T> | undefined;\n}\n\n/**\n * Simple queue structure.\n *\n * Avoids scalability issues with using a packed array directly by using\n * multiple arrays in a linked list and keeping the array size bounded.\n */\nexport class SimpleQueue<T> {\n  private _front: Node<T>;\n  private _back: Node<T>;\n  private _cursor = 0;\n  private _size = 0;\n\n  constructor() {\n    // _front and _back are always defined.\n    this._front = {\n      _elements: [],\n      _next: undefined\n    };\n    this._back = this._front;\n    // The cursor is used to avoid calling Array.shift().\n    // It contains the index of the front element of the array inside the\n    // front-most node. It is always in the range [0, QUEUE_MAX_ARRAY_SIZE).\n    this._cursor = 0;\n    // When there is only one node, size === elements.length - cursor.\n    this._size = 0;\n  }\n\n  get length(): number {\n    return this._size;\n  }\n\n  // For exception safety, this method is structured in order:\n  // 1. Read state\n  // 2. Calculate required state mutations\n  // 3. Perform state mutations\n  push(element: T): void {\n    const oldBack = this._back;\n    let newBack = oldBack;\n    assert(oldBack._next === undefined);\n    if (oldBack._elements.length === QUEUE_MAX_ARRAY_SIZE - 1) {\n      newBack = {\n        _elements: [],\n        _next: undefined\n      };\n    }\n\n    // push() is the mutation most likely to throw an exception, so it\n    // goes first.\n    oldBack._elements.push(element);\n    if (newBack !== oldBack) {\n      this._back = newBack;\n      oldBack._next = newBack;\n    }\n    ++this._size;\n  }\n\n  // Like push(), shift() follows the read -> calculate -> mutate pattern for\n  // exception safety.\n  shift(): T {\n    assert(this._size > 0); // must not be called on an empty queue\n\n    const oldFront = this._front;\n    let newFront = oldFront;\n    const oldCursor = this._cursor;\n    let newCursor = oldCursor + 1;\n\n    const elements = oldFront._elements;\n    const element = elements[oldCursor];\n\n    if (newCursor === QUEUE_MAX_ARRAY_SIZE) {\n      assert(elements.length === QUEUE_MAX_ARRAY_SIZE);\n      assert(oldFront._next !== undefined);\n      newFront = oldFront._next!;\n      newCursor = 0;\n    }\n\n    // No mutations before this point.\n    --this._size;\n    this._cursor = newCursor;\n    if (oldFront !== newFront) {\n      this._front = newFront;\n    }\n\n    // Permit shifted element to be garbage collected.\n    elements[oldCursor] = undefined!;\n\n    return element;\n  }\n\n  // The tricky thing about forEach() is that it can be called\n  // re-entrantly. The queue may be mutated inside the callback. It is easy to\n  // see that push() within the callback has no negative effects since the end\n  // of the queue is checked for on every iteration. If shift() is called\n  // repeatedly within the callback then the next iteration may return an\n  // element that has been removed. In this case the callback will be called\n  // with undefined values until we either \"catch up\" with elements that still\n  // exist or reach the back of the queue.\n  forEach(callback: (element: T) => void): void {\n    let i = this._cursor;\n    let node = this._front;\n    let elements = node._elements;\n    while (i !== elements.length || node._next !== undefined) {\n      if (i === elements.length) {\n        assert(node._next !== undefined);\n        assert(i === QUEUE_MAX_ARRAY_SIZE);\n        node = node._next!;\n        elements = node._elements;\n        i = 0;\n        if (elements.length === 0) {\n          break;\n        }\n      }\n      callback(elements[i]);\n      ++i;\n    }\n  }\n\n  // Return the element that would be returned if shift() was called now,\n  // without modifying the queue.\n  peek() {\n    assert(this._size > 0); // must not be called on an empty queue\n\n    const front = this._front;\n    const cursor = this._cursor;\n    return front._elements[cursor];\n  }\n}\n", "import assert from '../../stub/assert';\nimport { ReadableStream, ReadableStreamCancel, ReadableStreamReader } from '../readable-stream';\nimport { newPromise, setPromiseIsHandledToTrue } from '../helpers/webidl';\n\nexport function ReadableStreamReaderGenericInitialize<R>(reader: ReadableStreamReader<R>, stream: ReadableStream<R>) {\n  reader._ownerReadableStream = stream;\n  stream._reader = reader;\n\n  if (stream._state === 'readable') {\n    defaultReaderClosedPromiseInitialize(reader);\n  } else if (stream._state === 'closed') {\n    defaultReaderClosedPromiseInitializeAsResolved(reader);\n  } else {\n    assert(stream._state === 'errored');\n\n    defaultReaderClosedPromiseInitializeAsRejected(reader, stream._storedError);\n  }\n}\n\n// A client of ReadableStreamDefaultReader and ReadableStreamBYOBReader may use these functions directly to bypass state\n// check.\n\nexport function ReadableStreamReaderGenericCancel(reader: ReadableStreamReader<any>, reason: any): Promise<void> {\n  const stream = reader._ownerReadableStream;\n  assert(stream !== undefined);\n  return ReadableStreamCancel(stream, reason);\n}\n\nexport function ReadableStreamReaderGenericRelease(reader: ReadableStreamReader<any>) {\n  assert(reader._ownerReadableStream !== undefined);\n  assert(reader._ownerReadableStream._reader === reader);\n\n  if (reader._ownerReadableStream._state === 'readable') {\n    defaultReaderClosedPromiseReject(\n      reader,\n      new TypeError(`Reader was released and can no longer be used to monitor the stream's closedness`));\n  } else {\n    defaultReaderClosedPromiseResetToRejected(\n      reader,\n      new TypeError(`Reader was released and can no longer be used to monitor the stream's closedness`));\n  }\n\n  reader._ownerReadableStream._reader = undefined;\n  reader._ownerReadableStream = undefined!;\n}\n\n// Helper functions for the readers.\n\nexport function readerLockException(name: string): TypeError {\n  return new TypeError('Cannot ' + name + ' a stream using a released reader');\n}\n\n// Helper functions for the ReadableStreamDefaultReader.\n\nexport function defaultReaderClosedPromiseInitialize(reader: ReadableStreamReader<any>) {\n  reader._closedPromise = newPromise((resolve, reject) => {\n    reader._closedPromise_resolve = resolve;\n    reader._closedPromise_reject = reject;\n  });\n}\n\nexport function defaultReaderClosedPromiseInitializeAsRejected(reader: ReadableStreamReader<any>, reason: any) {\n  defaultReaderClosedPromiseInitialize(reader);\n  defaultReaderClosedPromiseReject(reader, reason);\n}\n\nexport function defaultReaderClosedPromiseInitializeAsResolved(reader: ReadableStreamReader<any>) {\n  defaultReaderClosedPromiseInitialize(reader);\n  defaultReaderClosedPromiseResolve(reader);\n}\n\nexport function defaultReaderClosedPromiseReject(reader: ReadableStreamReader<any>, reason: any) {\n  if (reader._closedPromise_reject === undefined) {\n    return;\n  }\n\n  setPromiseIsHandledToTrue(reader._closedPromise);\n  reader._closedPromise_reject(reason);\n  reader._closedPromise_resolve = undefined;\n  reader._closedPromise_reject = undefined;\n}\n\nexport function defaultReaderClosedPromiseResetToRejected(reader: ReadableStreamReader<any>, reason: any) {\n  assert(reader._closedPromise_resolve === undefined);\n  assert(reader._closedPromise_reject === undefined);\n\n  defaultReaderClosedPromiseInitializeAsRejected(reader, reason);\n}\n\nexport function defaultReaderClosedPromiseResolve(reader: ReadableStreamReader<any>) {\n  if (reader._closedPromise_resolve === undefined) {\n    return;\n  }\n\n  reader._closedPromise_resolve(undefined);\n  reader._closedPromise_resolve = undefined;\n  reader._closedPromise_reject = undefined;\n}\n", "export const AbortSteps = Symbol('[[AbortSteps]]');\nexport const ErrorSteps = Symbol('[[ErrorSteps]]');\nexport const CancelSteps = Symbol('[[CancelSteps]]');\nexport const PullSteps = Symbol('[[PullSteps]]');\n", "/// <reference lib=\"es2015.core\" />\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isFinite#Polyfill\nconst NumberIsFinite: typeof Number.isFinite = Number.isFinite || function (x) {\n  return typeof x === 'number' && isFinite(x);\n};\n\nexport default NumberIsFinite;\n", "/// <reference lib=\"es2015.core\" />\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/trunc#Polyfill\nconst MathTrunc: typeof Math.trunc = Math.trunc || function (v) {\n  return v < 0 ? Math.ceil(v) : Math.floor(v);\n};\n\nexport default MathTrunc;\n", "import NumberIsFinite from '../../stub/number-isfinite';\nimport MathTrunc from '../../stub/math-trunc';\n\n// https://heycam.github.io/webidl/#idl-dictionaries\nexport function isDictionary(x: any): x is object | null {\n  return typeof x === 'object' || typeof x === 'function';\n}\n\nexport function assertDictionary(obj: unknown,\n                                 context: string): asserts obj is object | null | undefined {\n  if (obj !== undefined && !isDictionary(obj)) {\n    throw new TypeError(`${context} is not an object.`);\n  }\n}\n\nexport type AnyFunction = (...args: any[]) => any;\n\n// https://heycam.github.io/webidl/#idl-callback-functions\nexport function assertFunction(x: unknown, context: string): asserts x is AnyFunction {\n  if (typeof x !== 'function') {\n    throw new TypeError(`${context} is not a function.`);\n  }\n}\n\n// https://heycam.github.io/webidl/#idl-object\nexport function isObject(x: any): x is object {\n  return (typeof x === 'object' && x !== null) || typeof x === 'function';\n}\n\nexport function assertObject(x: unknown,\n                             context: string): asserts x is object {\n  if (!isObject(x)) {\n    throw new TypeError(`${context} is not an object.`);\n  }\n}\n\nexport function assertRequiredArgument<T extends any>(x: T | undefined,\n                                                      position: number,\n                                                      context: string): asserts x is T {\n  if (x === undefined) {\n    throw new TypeError(`Parameter ${position} is required in '${context}'.`);\n  }\n}\n\nexport function assertRequiredField<T extends any>(x: T | undefined,\n                                                   field: string,\n                                                   context: string): asserts x is T {\n  if (x === undefined) {\n    throw new TypeError(`${field} is required in '${context}'.`);\n  }\n}\n\n// https://heycam.github.io/webidl/#idl-unrestricted-double\nexport function convertUnrestrictedDouble(value: unknown): number {\n  return Number(value);\n}\n\nfunction censorNegativeZero(x: number): number {\n  return x === 0 ? 0 : x;\n}\n\nfunction integerPart(x: number): number {\n  return censorNegativeZero(MathTrunc(x));\n}\n\n// https://heycam.github.io/webidl/#idl-unsigned-long-long\nexport function convertUnsignedLongLongWithEnforceRange(value: unknown, context: string): number {\n  const lowerBound = 0;\n  const upperBound = Number.MAX_SAFE_INTEGER;\n\n  let x = Number(value);\n  x = censorNegativeZero(x);\n\n  if (!NumberIsFinite(x)) {\n    throw new TypeError(`${context} is not a finite number`);\n  }\n\n  x = integerPart(x);\n\n  if (x < lowerBound || x > upperBound) {\n    throw new TypeError(`${context} is outside the accepted range of ${lowerBound} to ${upperBound}, inclusive`);\n  }\n\n  if (!NumberIsFinite(x) || x === 0) {\n    return 0;\n  }\n\n  // TODO Use BigInt if supported?\n  // let xBigInt = BigInt(integerPart(x));\n  // xBigInt = BigInt.asUintN(64, xBigInt);\n  // return Number(xBigInt);\n\n  return x;\n}\n", "import { IsReadableStream, ReadableStream } from '../readable-stream';\n\nexport function assertReadableStream(x: unknown, context: string): asserts x is ReadableStream {\n  if (!IsReadableStream(x)) {\n    throw new TypeError(`${context} is not a ReadableStream.`);\n  }\n}\n", "import assert from '../../stub/assert';\nimport { SimpleQueue } from '../simple-queue';\nimport {\n  ReadableStreamReaderGenericCancel,\n  ReadableStreamReaderGenericInitialize,\n  ReadableStreamReaderGenericRelease,\n  readerLockException\n} from './generic-reader';\nimport { IsReadableStreamLocked, ReadableStream } from '../readable-stream';\nimport { typeIsObject } from '../helpers/miscellaneous';\nimport { PullSteps } from '../abstract-ops/internal-methods';\nimport { newPromise, promiseRejectedWith } from '../helpers/webidl';\nimport { assertRequiredArgument } from '../validators/basic';\nimport { assertReadableStream } from '../validators/readable-stream';\n\n/**\n * A result returned by {@link ReadableStreamDefaultReader.read}.\n *\n * @public\n */\nexport type ReadableStreamDefaultReadResult<T> = {\n  done: false;\n  value: T;\n} | {\n  done: true;\n  value: undefined;\n}\n\n// Abstract operations for the ReadableStream.\n\nexport function AcquireReadableStreamDefaultReader<R>(stream: ReadableStream): ReadableStreamDefaultReader<R> {\n  return new ReadableStreamDefaultReader(stream);\n}\n\n// ReadableStream API exposed for controllers.\n\nexport function ReadableStreamAddReadRequest<R>(stream: ReadableStream<R>,\n                                                readRequest: ReadRequest<R>): void {\n  assert(IsReadableStreamDefaultReader(stream._reader));\n  assert(stream._state === 'readable');\n\n  (stream._reader! as ReadableStreamDefaultReader<R>)._readRequests.push(readRequest);\n}\n\nexport function ReadableStreamFulfillReadRequest<R>(stream: ReadableStream<R>, chunk: R | undefined, done: boolean) {\n  const reader = stream._reader as ReadableStreamDefaultReader<R>;\n\n  assert(reader._readRequests.length > 0);\n\n  const readRequest = reader._readRequests.shift()!;\n  if (done) {\n    readRequest._closeSteps();\n  } else {\n    readRequest._chunkSteps(chunk!);\n  }\n}\n\nexport function ReadableStreamGetNumReadRequests<R>(stream: ReadableStream<R>): number {\n  return (stream._reader as ReadableStreamDefaultReader<R>)._readRequests.length;\n}\n\nexport function ReadableStreamHasDefaultReader(stream: ReadableStream): boolean {\n  const reader = stream._reader;\n\n  if (reader === undefined) {\n    return false;\n  }\n\n  if (!IsReadableStreamDefaultReader(reader)) {\n    return false;\n  }\n\n  return true;\n}\n\n// Readers\n\nexport interface ReadRequest<R> {\n  _chunkSteps(chunk: R): void;\n\n  _closeSteps(): void;\n\n  _errorSteps(e: any): void;\n}\n\n/**\n * A default reader vended by a {@link ReadableStream}.\n *\n * @public\n */\nexport class ReadableStreamDefaultReader<R = any> {\n  /** @internal */\n  _ownerReadableStream!: ReadableStream<R>;\n  /** @internal */\n  _closedPromise!: Promise<void>;\n  /** @internal */\n  _closedPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _closedPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _readRequests: SimpleQueue<ReadRequest<R>>;\n\n  constructor(stream: ReadableStream<R>) {\n    assertRequiredArgument(stream, 1, 'ReadableStreamDefaultReader');\n    assertReadableStream(stream, 'First parameter');\n\n    if (IsReadableStreamLocked(stream)) {\n      throw new TypeError('This stream has already been locked for exclusive reading by another reader');\n    }\n\n    ReadableStreamReaderGenericInitialize(this, stream);\n\n    this._readRequests = new SimpleQueue();\n  }\n\n  /**\n   * Returns a promise that will be fulfilled when the stream becomes closed,\n   * or rejected if the stream ever errors or the reader's lock is released before the stream finishes closing.\n   */\n  get closed(): Promise<void> {\n    if (!IsReadableStreamDefaultReader(this)) {\n      return promiseRejectedWith(defaultReaderBrandCheckException('closed'));\n    }\n\n    return this._closedPromise;\n  }\n\n  /**\n   * If the reader is active, behaves the same as {@link ReadableStream.cancel | stream.cancel(reason)}.\n   */\n  cancel(reason: any = undefined): Promise<void> {\n    if (!IsReadableStreamDefaultReader(this)) {\n      return promiseRejectedWith(defaultReaderBrandCheckException('cancel'));\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('cancel'));\n    }\n\n    return ReadableStreamReaderGenericCancel(this, reason);\n  }\n\n  /**\n   * Returns a promise that allows access to the next chunk from the stream's internal queue, if available.\n   *\n   * If reading a chunk causes the queue to become empty, more data will be pulled from the underlying source.\n   */\n  read(): Promise<ReadableStreamDefaultReadResult<R>> {\n    if (!IsReadableStreamDefaultReader(this)) {\n      return promiseRejectedWith(defaultReaderBrandCheckException('read'));\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('read from'));\n    }\n\n    let resolvePromise!: (result: ReadableStreamDefaultReadResult<R>) => void;\n    let rejectPromise!: (reason: any) => void;\n    const promise = newPromise<ReadableStreamDefaultReadResult<R>>((resolve, reject) => {\n      resolvePromise = resolve;\n      rejectPromise = reject;\n    });\n    const readRequest: ReadRequest<R> = {\n      _chunkSteps: chunk => resolvePromise({ value: chunk, done: false }),\n      _closeSteps: () => resolvePromise({ value: undefined, done: true }),\n      _errorSteps: e => rejectPromise(e)\n    };\n    ReadableStreamDefaultReaderRead(this, readRequest);\n    return promise;\n  }\n\n  /**\n   * Releases the reader's lock on the corresponding stream. After the lock is released, the reader is no longer active.\n   * If the associated stream is errored when the lock is released, the reader will appear errored in the same way\n   * from now on; otherwise, the reader will appear closed.\n   *\n   * A reader's lock cannot be released while it still has a pending read request, i.e., if a promise returned by\n   * the reader's {@link ReadableStreamDefaultReader.read | read()} method has not yet been settled. Attempting to\n   * do so will throw a `TypeError` and leave the reader locked to the stream.\n   */\n  releaseLock(): void {\n    if (!IsReadableStreamDefaultReader(this)) {\n      throw defaultReaderBrandCheckException('releaseLock');\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return;\n    }\n\n    if (this._readRequests.length > 0) {\n      throw new TypeError('Tried to release a reader lock when that reader has pending read() calls un-settled');\n    }\n\n    ReadableStreamReaderGenericRelease(this);\n  }\n}\n\nObject.defineProperties(ReadableStreamDefaultReader.prototype, {\n  cancel: { enumerable: true },\n  read: { enumerable: true },\n  releaseLock: { enumerable: true },\n  closed: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStreamDefaultReader.prototype, Symbol.toStringTag, {\n    value: 'ReadableStreamDefaultReader',\n    configurable: true\n  });\n}\n\n// Abstract operations for the readers.\n\nexport function IsReadableStreamDefaultReader<R = any>(x: any): x is ReadableStreamDefaultReader<R> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_readRequests')) {\n    return false;\n  }\n\n  return true;\n}\n\nexport function ReadableStreamDefaultReaderRead<R>(reader: ReadableStreamDefaultReader<R>,\n                                                   readRequest: ReadRequest<R>): void {\n  const stream = reader._ownerReadableStream;\n\n  assert(stream !== undefined);\n\n  stream._disturbed = true;\n\n  if (stream._state === 'closed') {\n    readRequest._closeSteps();\n  } else if (stream._state === 'errored') {\n    readRequest._errorSteps(stream._storedError);\n  } else {\n    assert(stream._state === 'readable');\n    stream._readableStreamController[PullSteps](readRequest as ReadRequest<any>);\n  }\n}\n\n// Helper functions for the ReadableStreamDefaultReader.\n\nfunction defaultReaderBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableStreamDefaultReader.prototype.${name} can only be used on a ReadableStreamDefaultReader`);\n}\n", "/// <reference lib=\"es2018.asynciterable\" />\n\nexport let AsyncIteratorPrototype: AsyncIterable<any> | undefined;\n\nif (typeof Symbol.asyncIterator === 'symbol') {\n  // We're running inside a ES2018+ environment, but we're compiling to an older syntax.\n  // We cannot access %AsyncIteratorPrototype% without non-ES2018 syntax, but we can re-create it.\n  AsyncIteratorPrototype = {\n    // 25.1.3.1 %AsyncIteratorPrototype% [ @@asyncIterator ] ( )\n    // https://tc39.github.io/ecma262/#sec-asynciteratorprototype-asynciterator\n    [Symbol.asyncIterator]() {\n      return this as any;\n    }\n  };\n  Object.defineProperty(AsyncIteratorPrototype, Symbol.asyncIterator, { enumerable: false });\n}\n", "/// <reference lib=\"es2018.asynciterable\" />\n\nimport { ReadableStream } from '../readable-stream';\nimport {\n  AcquireReadableStreamDefaultReader,\n  ReadableStreamDefaultReader,\n  ReadableStreamDefaultReaderRead,\n  ReadableStreamDefaultReadResult,\n  ReadRequest\n} from './default-reader';\nimport {\n  ReadableStreamReaderGenericCancel,\n  ReadableStreamReaderGenericRelease,\n  readerLockException\n} from './generic-reader';\nimport assert from '../../stub/assert';\nimport { AsyncIteratorPrototype } from '@@target/stub/async-iterator-prototype';\nimport { typeIsObject } from '../helpers/miscellaneous';\nimport {\n  newPromise,\n  promiseRejectedWith,\n  promiseResolvedWith,\n  queueMicrotask,\n  transformPromiseWith\n} from '../helpers/webidl';\n\n/**\n * An async iterator returned by {@link ReadableStream.values}.\n *\n * @public\n */\nexport interface ReadableStreamAsyncIterator<R> extends AsyncIterator<R> {\n  next(): Promise<IteratorResult<R>>;\n\n  return(value?: any): Promise<IteratorResult<any>>;\n}\n\nexport class ReadableStreamAsyncIteratorImpl<R> {\n  private readonly _reader: ReadableStreamDefaultReader<R>;\n  private readonly _preventCancel: boolean;\n  private _ongoingPromise: Promise<ReadableStreamDefaultReadResult<R>> | undefined = undefined;\n  private _isFinished = false;\n\n  constructor(reader: ReadableStreamDefaultReader<R>, preventCancel: boolean) {\n    this._reader = reader;\n    this._preventCancel = preventCancel;\n  }\n\n  next(): Promise<ReadableStreamDefaultReadResult<R>> {\n    const nextSteps = () => this._nextSteps();\n    this._ongoingPromise = this._ongoingPromise ?\n      transformPromiseWith(this._ongoingPromise, nextSteps, nextSteps) :\n      nextSteps();\n    return this._ongoingPromise;\n  }\n\n  return(value: any): Promise<ReadableStreamDefaultReadResult<any>> {\n    const returnSteps = () => this._returnSteps(value);\n    return this._ongoingPromise ?\n      transformPromiseWith(this._ongoingPromise, returnSteps, returnSteps) :\n      returnSteps();\n  }\n\n  private _nextSteps(): Promise<ReadableStreamDefaultReadResult<R>> {\n    if (this._isFinished) {\n      return Promise.resolve({ value: undefined, done: true });\n    }\n\n    const reader = this._reader;\n    if (reader._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('iterate'));\n    }\n\n    let resolvePromise!: (result: ReadableStreamDefaultReadResult<R>) => void;\n    let rejectPromise!: (reason: any) => void;\n    const promise = newPromise<ReadableStreamDefaultReadResult<R>>((resolve, reject) => {\n      resolvePromise = resolve;\n      rejectPromise = reject;\n    });\n    const readRequest: ReadRequest<R> = {\n      _chunkSteps: chunk => {\n        this._ongoingPromise = undefined;\n        // This needs to be delayed by one microtask, otherwise we stop pulling too early which breaks a test.\n        // FIXME Is this a bug in the specification, or in the test?\n        queueMicrotask(() => resolvePromise({ value: chunk, done: false }));\n      },\n      _closeSteps: () => {\n        this._ongoingPromise = undefined;\n        this._isFinished = true;\n        ReadableStreamReaderGenericRelease(reader);\n        resolvePromise({ value: undefined, done: true });\n      },\n      _errorSteps: reason => {\n        this._ongoingPromise = undefined;\n        this._isFinished = true;\n        ReadableStreamReaderGenericRelease(reader);\n        rejectPromise(reason);\n      }\n    };\n    ReadableStreamDefaultReaderRead(reader, readRequest);\n    return promise;\n  }\n\n  private _returnSteps(value: any): Promise<ReadableStreamDefaultReadResult<any>> {\n    if (this._isFinished) {\n      return Promise.resolve({ value, done: true });\n    }\n    this._isFinished = true;\n\n    const reader = this._reader;\n    if (reader._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('finish iterating'));\n    }\n\n    assert(reader._readRequests.length === 0);\n\n    if (!this._preventCancel) {\n      const result = ReadableStreamReaderGenericCancel(reader, value);\n      ReadableStreamReaderGenericRelease(reader);\n      return transformPromiseWith(result, () => ({ value, done: true }));\n    }\n\n    ReadableStreamReaderGenericRelease(reader);\n    return promiseResolvedWith({ value, done: true });\n  }\n}\n\ndeclare class ReadableStreamAsyncIteratorInstance<R> implements ReadableStreamAsyncIterator<R> {\n  /** @interal */\n  _asyncIteratorImpl: ReadableStreamAsyncIteratorImpl<R>;\n\n  next(): Promise<ReadableStreamDefaultReadResult<R>>;\n\n  return(value?: any): Promise<ReadableStreamDefaultReadResult<any>>;\n}\n\nconst ReadableStreamAsyncIteratorPrototype: ReadableStreamAsyncIteratorInstance<any> = {\n  next(this: ReadableStreamAsyncIteratorInstance<any>): Promise<ReadableStreamDefaultReadResult<any>> {\n    if (!IsReadableStreamAsyncIterator(this)) {\n      return promiseRejectedWith(streamAsyncIteratorBrandCheckException('next'));\n    }\n    return this._asyncIteratorImpl.next();\n  },\n\n  return(this: ReadableStreamAsyncIteratorInstance<any>, value: any): Promise<ReadableStreamDefaultReadResult<any>> {\n    if (!IsReadableStreamAsyncIterator(this)) {\n      return promiseRejectedWith(streamAsyncIteratorBrandCheckException('return'));\n    }\n    return this._asyncIteratorImpl.return(value);\n  }\n} as any;\nif (AsyncIteratorPrototype !== undefined) {\n  Object.setPrototypeOf(ReadableStreamAsyncIteratorPrototype, AsyncIteratorPrototype);\n}\n\n// Abstract operations for the ReadableStream.\n\nexport function AcquireReadableStreamAsyncIterator<R>(stream: ReadableStream<R>,\n                                                      preventCancel: boolean): ReadableStreamAsyncIterator<R> {\n  const reader = AcquireReadableStreamDefaultReader<R>(stream);\n  const impl = new ReadableStreamAsyncIteratorImpl(reader, preventCancel);\n  const iterator: ReadableStreamAsyncIteratorInstance<R> = Object.create(ReadableStreamAsyncIteratorPrototype);\n  iterator._asyncIteratorImpl = impl;\n  return iterator;\n}\n\nfunction IsReadableStreamAsyncIterator<R = any>(x: any): x is ReadableStreamAsyncIterator<R> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_asyncIteratorImpl')) {\n    return false;\n  }\n\n  return true;\n}\n\n// Helper functions for the ReadableStream.\n\nfunction streamAsyncIteratorBrandCheckException(name: string): TypeError {\n  return new TypeError(`ReadableStreamAsyncIterator.${name} can only be used on a ReadableSteamAsyncIterator`);\n}\n", "/// <reference lib=\"es2015.core\" />\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isNaN#Polyfill\nconst NumberIsNaN: typeof Number.isNaN = Number.isNaN || function (x) {\n  // eslint-disable-next-line no-self-compare\n  return x !== x;\n};\n\nexport default NumberIsNaN;\n", "import NumberIsNaN from '../../stub/number-isnan';\n\nexport function IsFiniteNonNegativeNumber(v: number): boolean {\n  if (!IsNonNegativeNumber(v)) {\n    return false;\n  }\n\n  if (v === Infinity) {\n    return false;\n  }\n\n  return true;\n}\n\nexport function IsNonNegativeNumber(v: number): boolean {\n  if (typeof v !== 'number') {\n    return false;\n  }\n\n  if (NumberIsNaN(v)) {\n    return false;\n  }\n\n  if (v < 0) {\n    return false;\n  }\n\n  return true;\n}\n", "import assert from '../../stub/assert';\nimport { SimpleQueue } from '../simple-queue';\nimport { IsFiniteNonNegativeNumber } from './miscellaneous';\n\nexport interface QueueContainer<T> {\n  _queue: SimpleQueue<T>;\n  _queueTotalSize: number;\n}\n\nexport interface QueuePair<T> {\n  value: T;\n  size: number;\n}\n\nexport function DequeueValue<T>(container: QueueContainer<QueuePair<T>>): T {\n  assert('_queue' in container && '_queueTotalSize' in container);\n  assert(container._queue.length > 0);\n\n  const pair = container._queue.shift()!;\n  container._queueTotalSize -= pair.size;\n  if (container._queueTotalSize < 0) {\n    container._queueTotalSize = 0;\n  }\n\n  return pair.value;\n}\n\nexport function EnqueueValueWithSize<T>(container: QueueContainer<QueuePair<T>>, value: T, size: number) {\n  assert('_queue' in container && '_queueTotalSize' in container);\n\n  size = Number(size);\n  if (!IsFiniteNonNegativeNumber(size)) {\n    throw new RangeError('Size must be a finite, non-NaN, non-negative number.');\n  }\n\n  container._queue.push({ value, size });\n  container._queueTotalSize += size;\n}\n\nexport function PeekQueueValue<T>(container: QueueContainer<QueuePair<T>>): T {\n  assert('_queue' in container && '_queueTotalSize' in container);\n  assert(container._queue.length > 0);\n\n  const pair = container._queue.peek();\n  return pair.value;\n}\n\nexport function ResetQueue<T>(container: QueueContainer<T>) {\n  assert('_queue' in container && '_queueTotalSize' in container);\n\n  container._queue = new SimpleQueue<T>();\n  container._queueTotalSize = 0;\n}\n", "export function CreateArrayFromList<T extends any[]>(elements: T): T {\n  // We use arrays to represent lists, so this is basically a no-op.\n  // Do a slice though just in case we happen to depend on the unique-ness.\n  return elements.slice() as T;\n}\n\nexport function CopyDataBlockBytes(dest: ArrayBuffer,\n                                   destOffset: number,\n                                   src: ArrayBuffer,\n                                   srcOffset: number,\n                                   n: number) {\n  new Uint8Array(dest).set(new Uint8Array(src, srcOffset, n), destOffset);\n}\n\n// Not implemented correctly\nexport function TransferArrayBuffer<T extends ArrayBufferLike>(O: T): T {\n  return O;\n}\n\n// Not implemented correctly\nexport function IsDetachedBuffer(O: ArrayBufferLike): boolean { // eslint-disable-line @typescript-eslint/no-unused-vars\n  return false;\n}\n", "import assert from '../../stub/assert';\nimport { SimpleQueue } from '../simple-queue';\nimport { ResetQueue } from '../abstract-ops/queue-with-sizes';\nimport {\n  ReadableStreamAddReadRequest,\n  ReadableStreamFulfillReadRequest,\n  ReadableStreamGetNumReadRequests,\n  ReadableStreamHasDefaultReader,\n  ReadRequest\n} from './default-reader';\nimport {\n  ReadableStreamAddReadIntoRequest,\n  ReadableStreamFulfillReadIntoRequest,\n  ReadableStreamGetNumReadIntoRequests,\n  ReadableStreamHasBYOBReader,\n  ReadIntoRequest\n} from './byob-reader';\nimport NumberIsInteger from '../../stub/number-isinteger';\nimport {\n  IsReadableStreamLocked,\n  ReadableByteStream,\n  ReadableStreamClose,\n  ReadableStreamError\n} from '../readable-stream';\nimport { ValidatedUnderlyingByteSource } from './underlying-source';\nimport { typeIsObject } from '../helpers/miscellaneous';\nimport { CopyDataBlockBytes, IsDetachedBuffer, TransferArrayBuffer } from '../abstract-ops/ecmascript';\nimport { CancelSteps, PullSteps } from '../abstract-ops/internal-methods';\nimport { IsFiniteNonNegativeNumber } from '../abstract-ops/miscellaneous';\nimport { promiseResolvedWith, uponPromise } from '../helpers/webidl';\nimport { assertRequiredArgument, convertUnsignedLongLongWithEnforceRange } from '../validators/basic';\n\n/**\n * A pull-into request in a {@link ReadableByteStreamController}.\n *\n * @public\n */\nexport class ReadableStreamBYOBRequest {\n  /** @internal */\n  _associatedReadableByteStreamController!: ReadableByteStreamController;\n  /** @internal */\n  _view!: ArrayBufferView | null;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Returns the view for writing in to, or `null` if the BYOB request has already been responded to.\n   */\n  get view(): ArrayBufferView | null {\n    if (!IsReadableStreamBYOBRequest(this)) {\n      throw byobRequestBrandCheckException('view');\n    }\n\n    return this._view;\n  }\n\n  /**\n   * Indicates to the associated readable byte stream that `bytesWritten` bytes were written into\n   * {@link ReadableStreamBYOBRequest.view | view}, causing the result be surfaced to the consumer.\n   *\n   * After this method is called, {@link ReadableStreamBYOBRequest.view | view} will be transferred and no longer\n   * modifiable.\n   */\n  respond(bytesWritten: number): void;\n  respond(bytesWritten: number | undefined): void {\n    if (!IsReadableStreamBYOBRequest(this)) {\n      throw byobRequestBrandCheckException('respond');\n    }\n    assertRequiredArgument(bytesWritten, 1, 'respond');\n    bytesWritten = convertUnsignedLongLongWithEnforceRange(bytesWritten, 'First parameter');\n\n    if (this._associatedReadableByteStreamController === undefined) {\n      throw new TypeError('This BYOB request has been invalidated');\n    }\n\n    if (IsDetachedBuffer(this._view!.buffer)) {\n      throw new TypeError(`The BYOB request's buffer has been detached and so cannot be used as a response`);\n    }\n\n    assert(this._view!.byteLength > 0);\n    assert(this._view!.buffer.byteLength > 0);\n\n    ReadableByteStreamControllerRespond(this._associatedReadableByteStreamController, bytesWritten);\n  }\n\n  /**\n   * Indicates to the associated readable byte stream that instead of writing into\n   * {@link ReadableStreamBYOBRequest.view | view}, the underlying byte source is providing a new `ArrayBufferView`,\n   * which will be given to the consumer of the readable byte stream.\n   *\n   * After this method is called, `view` will be transferred and no longer modifiable.\n   */\n  respondWithNewView(view: ArrayBufferView): void;\n  respondWithNewView(view: ArrayBufferView | undefined): void {\n    if (!IsReadableStreamBYOBRequest(this)) {\n      throw byobRequestBrandCheckException('respondWithNewView');\n    }\n    assertRequiredArgument(view, 1, 'respondWithNewView');\n\n    if (!ArrayBuffer.isView(view)) {\n      throw new TypeError('You can only respond with array buffer views');\n    }\n    if (view.byteLength === 0) {\n      throw new TypeError('chunk must have non-zero byteLength');\n    }\n    if (view.buffer.byteLength === 0) {\n      throw new TypeError(`chunk's buffer must have non-zero byteLength`);\n    }\n\n    if (this._associatedReadableByteStreamController === undefined) {\n      throw new TypeError('This BYOB request has been invalidated');\n    }\n\n    ReadableByteStreamControllerRespondWithNewView(this._associatedReadableByteStreamController, view);\n  }\n}\n\nObject.defineProperties(ReadableStreamBYOBRequest.prototype, {\n  respond: { enumerable: true },\n  respondWithNewView: { enumerable: true },\n  view: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStreamBYOBRequest.prototype, Symbol.toStringTag, {\n    value: 'ReadableStreamBYOBRequest',\n    configurable: true\n  });\n}\n\ninterface ArrayBufferViewConstructor<T extends ArrayBufferView = ArrayBufferView> {\n  new(buffer: ArrayBufferLike, byteOffset: number, length?: number): T;\n\n  readonly prototype: T;\n  readonly BYTES_PER_ELEMENT: number;\n}\n\ninterface ByteQueueElement {\n  buffer: ArrayBufferLike;\n  byteOffset: number;\n  byteLength: number;\n}\n\ntype PullIntoDescriptor<T extends ArrayBufferView = ArrayBufferView> =\n  DefaultPullIntoDescriptor\n  | BYOBPullIntoDescriptor;\n\ninterface DefaultPullIntoDescriptor {\n  buffer: ArrayBufferLike;\n  byteOffset: number;\n  byteLength: number;\n  bytesFilled: number;\n  elementSize: number;\n  viewConstructor: ArrayBufferViewConstructor<Uint8Array>;\n  readerType: 'default';\n}\n\ninterface BYOBPullIntoDescriptor<T extends ArrayBufferView = ArrayBufferView> {\n  buffer: ArrayBufferLike;\n  byteOffset: number;\n  byteLength: number;\n  bytesFilled: number;\n  elementSize: number;\n  viewConstructor: ArrayBufferViewConstructor<T>;\n  readerType: 'byob';\n}\n\n/**\n * Allows control of a {@link ReadableStream | readable byte stream}'s state and internal queue.\n *\n * @public\n */\nexport class ReadableByteStreamController {\n  /** @internal */\n  _controlledReadableByteStream!: ReadableByteStream;\n  /** @internal */\n  _queue!: SimpleQueue<ByteQueueElement>;\n  /** @internal */\n  _queueTotalSize!: number;\n  /** @internal */\n  _started!: boolean;\n  /** @internal */\n  _closeRequested!: boolean;\n  /** @internal */\n  _pullAgain!: boolean;\n  /** @internal */\n  _pulling !: boolean;\n  /** @internal */\n  _strategyHWM!: number;\n  /** @internal */\n  _pullAlgorithm!: () => Promise<void>;\n  /** @internal */\n  _cancelAlgorithm!: (reason: any) => Promise<void>;\n  /** @internal */\n  _autoAllocateChunkSize: number | undefined;\n  /** @internal */\n  _byobRequest: ReadableStreamBYOBRequest | null;\n  /** @internal */\n  _pendingPullIntos!: SimpleQueue<PullIntoDescriptor>;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Returns the current BYOB pull request, or `null` if there isn't one.\n   */\n  get byobRequest(): ReadableStreamBYOBRequest | null {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('byobRequest');\n    }\n\n    if (this._byobRequest === null && this._pendingPullIntos.length > 0) {\n      const firstDescriptor = this._pendingPullIntos.peek();\n      const view = new Uint8Array(firstDescriptor.buffer,\n                                  firstDescriptor.byteOffset + firstDescriptor.bytesFilled,\n                                  firstDescriptor.byteLength - firstDescriptor.bytesFilled);\n\n      const byobRequest: ReadableStreamBYOBRequest = Object.create(ReadableStreamBYOBRequest.prototype);\n      SetUpReadableStreamBYOBRequest(byobRequest, this, view);\n      this._byobRequest = byobRequest;\n    }\n\n    return this._byobRequest;\n  }\n\n  /**\n   * Returns the desired size to fill the controlled stream's internal queue. It can be negative, if the queue is\n   * over-full. An underlying byte source ought to use this information to determine when and how to apply backpressure.\n   */\n  get desiredSize(): number | null {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('desiredSize');\n    }\n\n    return ReadableByteStreamControllerGetDesiredSize(this);\n  }\n\n  /**\n   * Closes the controlled readable stream. Consumers will still be able to read any previously-enqueued chunks from\n   * the stream, but once those are read, the stream will become closed.\n   */\n  close(): void {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('close');\n    }\n\n    if (this._closeRequested) {\n      throw new TypeError('The stream has already been closed; do not close it again!');\n    }\n\n    const state = this._controlledReadableByteStream._state;\n    if (state !== 'readable') {\n      throw new TypeError(`The stream (in ${state} state) is not in the readable state and cannot be closed`);\n    }\n\n    ReadableByteStreamControllerClose(this);\n  }\n\n  /**\n   * Enqueues the given chunk chunk in the controlled readable stream.\n   * The chunk has to be an `ArrayBufferView` instance, or else a `TypeError` will be thrown.\n   */\n  enqueue(chunk: ArrayBufferView): void;\n  enqueue(chunk: ArrayBufferView | undefined): void {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('enqueue');\n    }\n\n    assertRequiredArgument(chunk, 1, 'enqueue');\n    if (!ArrayBuffer.isView(chunk)) {\n      throw new TypeError('chunk must be an array buffer view');\n    }\n    if (chunk.byteLength === 0) {\n      throw new TypeError('chunk must have non-zero byteLength');\n    }\n    if (chunk.buffer.byteLength === 0) {\n      throw new TypeError(`chunk's buffer must have non-zero byteLength`);\n    }\n\n    if (this._closeRequested) {\n      throw new TypeError('stream is closed or draining');\n    }\n\n    const state = this._controlledReadableByteStream._state;\n    if (state !== 'readable') {\n      throw new TypeError(`The stream (in ${state} state) is not in the readable state and cannot be enqueued to`);\n    }\n\n    ReadableByteStreamControllerEnqueue(this, chunk);\n  }\n\n  /**\n   * Errors the controlled readable stream, making all future interactions with it fail with the given error `e`.\n   */\n  error(e: any = undefined): void {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('error');\n    }\n\n    ReadableByteStreamControllerError(this, e);\n  }\n\n  /** @internal */\n  [CancelSteps](reason: any): Promise<void> {\n    if (this._pendingPullIntos.length > 0) {\n      const firstDescriptor = this._pendingPullIntos.peek();\n      firstDescriptor.bytesFilled = 0;\n    }\n\n    ResetQueue(this);\n\n    const result = this._cancelAlgorithm(reason);\n    ReadableByteStreamControllerClearAlgorithms(this);\n    return result;\n  }\n\n  /** @internal */\n  [PullSteps](readRequest: ReadRequest<Uint8Array>): void {\n    const stream = this._controlledReadableByteStream;\n    assert(ReadableStreamHasDefaultReader(stream));\n\n    if (this._queueTotalSize > 0) {\n      assert(ReadableStreamGetNumReadRequests(stream) === 0);\n\n      const entry = this._queue.shift()!;\n      this._queueTotalSize -= entry.byteLength;\n\n      ReadableByteStreamControllerHandleQueueDrain(this);\n\n      const view = new Uint8Array(entry.buffer, entry.byteOffset, entry.byteLength);\n\n      readRequest._chunkSteps(view);\n      return;\n    }\n\n    const autoAllocateChunkSize = this._autoAllocateChunkSize;\n    if (autoAllocateChunkSize !== undefined) {\n      let buffer: ArrayBuffer;\n      try {\n        buffer = new ArrayBuffer(autoAllocateChunkSize);\n      } catch (bufferE) {\n        readRequest._errorSteps(bufferE);\n        return;\n      }\n\n      const pullIntoDescriptor: DefaultPullIntoDescriptor = {\n        buffer,\n        byteOffset: 0,\n        byteLength: autoAllocateChunkSize,\n        bytesFilled: 0,\n        elementSize: 1,\n        viewConstructor: Uint8Array,\n        readerType: 'default'\n      };\n\n      this._pendingPullIntos.push(pullIntoDescriptor);\n    }\n\n    ReadableStreamAddReadRequest(stream, readRequest);\n    ReadableByteStreamControllerCallPullIfNeeded(this);\n  }\n}\n\nObject.defineProperties(ReadableByteStreamController.prototype, {\n  close: { enumerable: true },\n  enqueue: { enumerable: true },\n  error: { enumerable: true },\n  byobRequest: { enumerable: true },\n  desiredSize: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableByteStreamController.prototype, Symbol.toStringTag, {\n    value: 'ReadableByteStreamController',\n    configurable: true\n  });\n}\n\n// Abstract operations for the ReadableByteStreamController.\n\nexport function IsReadableByteStreamController(x: any): x is ReadableByteStreamController {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_controlledReadableByteStream')) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction IsReadableStreamBYOBRequest(x: any): x is ReadableStreamBYOBRequest {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_associatedReadableByteStreamController')) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction ReadableByteStreamControllerCallPullIfNeeded(controller: ReadableByteStreamController): void {\n  const shouldPull = ReadableByteStreamControllerShouldCallPull(controller);\n  if (!shouldPull) {\n    return;\n  }\n\n  if (controller._pulling) {\n    controller._pullAgain = true;\n    return;\n  }\n\n  assert(!controller._pullAgain);\n\n  controller._pulling = true;\n\n  // TODO: Test controller argument\n  const pullPromise = controller._pullAlgorithm();\n  uponPromise(\n    pullPromise,\n    () => {\n      controller._pulling = false;\n\n      if (controller._pullAgain) {\n        controller._pullAgain = false;\n        ReadableByteStreamControllerCallPullIfNeeded(controller);\n      }\n    },\n    e => {\n      ReadableByteStreamControllerError(controller, e);\n    }\n  );\n}\n\nfunction ReadableByteStreamControllerClearPendingPullIntos(controller: ReadableByteStreamController) {\n  ReadableByteStreamControllerInvalidateBYOBRequest(controller);\n  controller._pendingPullIntos = new SimpleQueue();\n}\n\nfunction ReadableByteStreamControllerCommitPullIntoDescriptor<T extends ArrayBufferView>(stream: ReadableByteStream,\n                                                                                         pullIntoDescriptor: PullIntoDescriptor<T>) {\n  assert(stream._state !== 'errored');\n\n  let done = false;\n  if (stream._state === 'closed') {\n    assert(pullIntoDescriptor.bytesFilled === 0);\n    done = true;\n  }\n\n  const filledView = ReadableByteStreamControllerConvertPullIntoDescriptor<T>(pullIntoDescriptor);\n  if (pullIntoDescriptor.readerType === 'default') {\n    ReadableStreamFulfillReadRequest(stream, filledView as unknown as Uint8Array, done);\n  } else {\n    assert(pullIntoDescriptor.readerType === 'byob');\n    ReadableStreamFulfillReadIntoRequest(stream, filledView, done);\n  }\n}\n\nfunction ReadableByteStreamControllerConvertPullIntoDescriptor<T extends ArrayBufferView>(pullIntoDescriptor: PullIntoDescriptor<T>): T {\n  const bytesFilled = pullIntoDescriptor.bytesFilled;\n  const elementSize = pullIntoDescriptor.elementSize;\n\n  assert(bytesFilled <= pullIntoDescriptor.byteLength);\n  assert(bytesFilled % elementSize === 0);\n\n  return new pullIntoDescriptor.viewConstructor(\n    pullIntoDescriptor.buffer, pullIntoDescriptor.byteOffset, bytesFilled / elementSize) as T;\n}\n\nfunction ReadableByteStreamControllerEnqueueChunkToQueue(controller: ReadableByteStreamController,\n                                                         buffer: ArrayBufferLike,\n                                                         byteOffset: number,\n                                                         byteLength: number) {\n  controller._queue.push({ buffer, byteOffset, byteLength });\n  controller._queueTotalSize += byteLength;\n}\n\nfunction ReadableByteStreamControllerFillPullIntoDescriptorFromQueue(controller: ReadableByteStreamController,\n                                                                     pullIntoDescriptor: PullIntoDescriptor) {\n  const elementSize = pullIntoDescriptor.elementSize;\n\n  const currentAlignedBytes = pullIntoDescriptor.bytesFilled - pullIntoDescriptor.bytesFilled % elementSize;\n\n  const maxBytesToCopy = Math.min(controller._queueTotalSize,\n                                  pullIntoDescriptor.byteLength - pullIntoDescriptor.bytesFilled);\n  const maxBytesFilled = pullIntoDescriptor.bytesFilled + maxBytesToCopy;\n  const maxAlignedBytes = maxBytesFilled - maxBytesFilled % elementSize;\n\n  let totalBytesToCopyRemaining = maxBytesToCopy;\n  let ready = false;\n  if (maxAlignedBytes > currentAlignedBytes) {\n    totalBytesToCopyRemaining = maxAlignedBytes - pullIntoDescriptor.bytesFilled;\n    ready = true;\n  }\n\n  const queue = controller._queue;\n\n  while (totalBytesToCopyRemaining > 0) {\n    const headOfQueue = queue.peek();\n\n    const bytesToCopy = Math.min(totalBytesToCopyRemaining, headOfQueue.byteLength);\n\n    const destStart = pullIntoDescriptor.byteOffset + pullIntoDescriptor.bytesFilled;\n    CopyDataBlockBytes(pullIntoDescriptor.buffer, destStart, headOfQueue.buffer, headOfQueue.byteOffset, bytesToCopy);\n\n    if (headOfQueue.byteLength === bytesToCopy) {\n      queue.shift();\n    } else {\n      headOfQueue.byteOffset += bytesToCopy;\n      headOfQueue.byteLength -= bytesToCopy;\n    }\n    controller._queueTotalSize -= bytesToCopy;\n\n    ReadableByteStreamControllerFillHeadPullIntoDescriptor(controller, bytesToCopy, pullIntoDescriptor);\n\n    totalBytesToCopyRemaining -= bytesToCopy;\n  }\n\n  if (!ready) {\n    assert(controller._queueTotalSize === 0);\n    assert(pullIntoDescriptor.bytesFilled > 0);\n    assert(pullIntoDescriptor.bytesFilled < pullIntoDescriptor.elementSize);\n  }\n\n  return ready;\n}\n\nfunction ReadableByteStreamControllerFillHeadPullIntoDescriptor(controller: ReadableByteStreamController,\n                                                                size: number,\n                                                                pullIntoDescriptor: PullIntoDescriptor) {\n  assert(controller._pendingPullIntos.length === 0 || controller._pendingPullIntos.peek() === pullIntoDescriptor);\n\n  ReadableByteStreamControllerInvalidateBYOBRequest(controller);\n  pullIntoDescriptor.bytesFilled += size;\n}\n\nfunction ReadableByteStreamControllerHandleQueueDrain(controller: ReadableByteStreamController) {\n  assert(controller._controlledReadableByteStream._state === 'readable');\n\n  if (controller._queueTotalSize === 0 && controller._closeRequested) {\n    ReadableByteStreamControllerClearAlgorithms(controller);\n    ReadableStreamClose(controller._controlledReadableByteStream);\n  } else {\n    ReadableByteStreamControllerCallPullIfNeeded(controller);\n  }\n}\n\nfunction ReadableByteStreamControllerInvalidateBYOBRequest(controller: ReadableByteStreamController) {\n  if (controller._byobRequest === null) {\n    return;\n  }\n\n  controller._byobRequest._associatedReadableByteStreamController = undefined!;\n  controller._byobRequest._view = null!;\n  controller._byobRequest = null;\n}\n\nfunction ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller: ReadableByteStreamController) {\n  assert(!controller._closeRequested);\n\n  while (controller._pendingPullIntos.length > 0) {\n    if (controller._queueTotalSize === 0) {\n      return;\n    }\n\n    const pullIntoDescriptor = controller._pendingPullIntos.peek();\n\n    if (ReadableByteStreamControllerFillPullIntoDescriptorFromQueue(controller, pullIntoDescriptor)) {\n      ReadableByteStreamControllerShiftPendingPullInto(controller);\n\n      ReadableByteStreamControllerCommitPullIntoDescriptor(\n        controller._controlledReadableByteStream,\n        pullIntoDescriptor\n      );\n    }\n  }\n}\n\nexport function ReadableByteStreamControllerPullInto<T extends ArrayBufferView>(\n  controller: ReadableByteStreamController,\n  view: T,\n  readIntoRequest: ReadIntoRequest<T>\n): void {\n  const stream = controller._controlledReadableByteStream;\n\n  let elementSize = 1;\n  if (view.constructor !== DataView) {\n    elementSize = (view.constructor as ArrayBufferViewConstructor<T>).BYTES_PER_ELEMENT;\n  }\n\n  const ctor = view.constructor as ArrayBufferViewConstructor<T>;\n\n  const buffer = TransferArrayBuffer(view.buffer);\n  const pullIntoDescriptor: BYOBPullIntoDescriptor<T> = {\n    buffer,\n    byteOffset: view.byteOffset,\n    byteLength: view.byteLength,\n    bytesFilled: 0,\n    elementSize,\n    viewConstructor: ctor,\n    readerType: 'byob'\n  };\n\n  if (controller._pendingPullIntos.length > 0) {\n    controller._pendingPullIntos.push(pullIntoDescriptor);\n\n    // No ReadableByteStreamControllerCallPullIfNeeded() call since:\n    // - No change happens on desiredSize\n    // - The source has already been notified of that there's at least 1 pending read(view)\n\n    ReadableStreamAddReadIntoRequest(stream, readIntoRequest);\n    return;\n  }\n\n  if (stream._state === 'closed') {\n    const emptyView = new ctor(pullIntoDescriptor.buffer, pullIntoDescriptor.byteOffset, 0);\n    readIntoRequest._closeSteps(emptyView);\n    return;\n  }\n\n  if (controller._queueTotalSize > 0) {\n    if (ReadableByteStreamControllerFillPullIntoDescriptorFromQueue(controller, pullIntoDescriptor)) {\n      const filledView = ReadableByteStreamControllerConvertPullIntoDescriptor<T>(pullIntoDescriptor);\n\n      ReadableByteStreamControllerHandleQueueDrain(controller);\n\n      readIntoRequest._chunkSteps(filledView);\n      return;\n    }\n\n    if (controller._closeRequested) {\n      const e = new TypeError('Insufficient bytes to fill elements in the given buffer');\n      ReadableByteStreamControllerError(controller, e);\n\n      readIntoRequest._errorSteps(e);\n      return;\n    }\n  }\n\n  controller._pendingPullIntos.push(pullIntoDescriptor);\n\n  ReadableStreamAddReadIntoRequest<T>(stream, readIntoRequest);\n  ReadableByteStreamControllerCallPullIfNeeded(controller);\n}\n\nfunction ReadableByteStreamControllerRespondInClosedState(controller: ReadableByteStreamController,\n                                                          firstDescriptor: PullIntoDescriptor) {\n  firstDescriptor.buffer = TransferArrayBuffer(firstDescriptor.buffer);\n\n  assert(firstDescriptor.bytesFilled === 0);\n\n  const stream = controller._controlledReadableByteStream;\n  if (ReadableStreamHasBYOBReader(stream)) {\n    while (ReadableStreamGetNumReadIntoRequests(stream) > 0) {\n      const pullIntoDescriptor = ReadableByteStreamControllerShiftPendingPullInto(controller);\n      ReadableByteStreamControllerCommitPullIntoDescriptor(stream, pullIntoDescriptor);\n    }\n  }\n}\n\nfunction ReadableByteStreamControllerRespondInReadableState(controller: ReadableByteStreamController,\n                                                            bytesWritten: number,\n                                                            pullIntoDescriptor: PullIntoDescriptor) {\n  if (pullIntoDescriptor.bytesFilled + bytesWritten > pullIntoDescriptor.byteLength) {\n    throw new RangeError('bytesWritten out of range');\n  }\n\n  ReadableByteStreamControllerFillHeadPullIntoDescriptor(controller, bytesWritten, pullIntoDescriptor);\n\n  if (pullIntoDescriptor.bytesFilled < pullIntoDescriptor.elementSize) {\n    // TODO: Figure out whether we should detach the buffer or not here.\n    return;\n  }\n\n  ReadableByteStreamControllerShiftPendingPullInto(controller);\n\n  const remainderSize = pullIntoDescriptor.bytesFilled % pullIntoDescriptor.elementSize;\n  if (remainderSize > 0) {\n    const end = pullIntoDescriptor.byteOffset + pullIntoDescriptor.bytesFilled;\n    const remainder = pullIntoDescriptor.buffer.slice(end - remainderSize, end);\n    ReadableByteStreamControllerEnqueueChunkToQueue(controller, remainder, 0, remainder.byteLength);\n  }\n\n  pullIntoDescriptor.buffer = TransferArrayBuffer(pullIntoDescriptor.buffer);\n  pullIntoDescriptor.bytesFilled -= remainderSize;\n  ReadableByteStreamControllerCommitPullIntoDescriptor(controller._controlledReadableByteStream, pullIntoDescriptor);\n\n  ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller);\n}\n\nfunction ReadableByteStreamControllerRespondInternal(controller: ReadableByteStreamController, bytesWritten: number) {\n  const firstDescriptor = controller._pendingPullIntos.peek();\n\n  const state = controller._controlledReadableByteStream._state;\n\n  if (state === 'closed') {\n    if (bytesWritten !== 0) {\n      throw new TypeError('bytesWritten must be 0 when calling respond() on a closed stream');\n    }\n\n    ReadableByteStreamControllerRespondInClosedState(controller, firstDescriptor);\n  } else {\n    assert(state === 'readable');\n\n    ReadableByteStreamControllerRespondInReadableState(controller, bytesWritten, firstDescriptor);\n  }\n\n  ReadableByteStreamControllerCallPullIfNeeded(controller);\n}\n\nfunction ReadableByteStreamControllerShiftPendingPullInto(controller: ReadableByteStreamController): PullIntoDescriptor {\n  const descriptor = controller._pendingPullIntos.shift()!;\n  ReadableByteStreamControllerInvalidateBYOBRequest(controller);\n  return descriptor;\n}\n\nfunction ReadableByteStreamControllerShouldCallPull(controller: ReadableByteStreamController): boolean {\n  const stream = controller._controlledReadableByteStream;\n\n  if (stream._state !== 'readable') {\n    return false;\n  }\n\n  if (controller._closeRequested) {\n    return false;\n  }\n\n  if (!controller._started) {\n    return false;\n  }\n\n  if (ReadableStreamHasDefaultReader(stream) && ReadableStreamGetNumReadRequests(stream) > 0) {\n    return true;\n  }\n\n  if (ReadableStreamHasBYOBReader(stream) && ReadableStreamGetNumReadIntoRequests(stream) > 0) {\n    return true;\n  }\n\n  const desiredSize = ReadableByteStreamControllerGetDesiredSize(controller);\n  assert(desiredSize !== null);\n  if (desiredSize! > 0) {\n    return true;\n  }\n\n  return false;\n}\n\nfunction ReadableByteStreamControllerClearAlgorithms(controller: ReadableByteStreamController) {\n  controller._pullAlgorithm = undefined!;\n  controller._cancelAlgorithm = undefined!;\n}\n\n// A client of ReadableByteStreamController may use these functions directly to bypass state check.\n\nfunction ReadableByteStreamControllerClose(controller: ReadableByteStreamController) {\n  const stream = controller._controlledReadableByteStream;\n\n  if (controller._closeRequested || stream._state !== 'readable') {\n    return;\n  }\n\n  if (controller._queueTotalSize > 0) {\n    controller._closeRequested = true;\n\n    return;\n  }\n\n  if (controller._pendingPullIntos.length > 0) {\n    const firstPendingPullInto = controller._pendingPullIntos.peek();\n    if (firstPendingPullInto.bytesFilled > 0) {\n      const e = new TypeError('Insufficient bytes to fill elements in the given buffer');\n      ReadableByteStreamControllerError(controller, e);\n\n      throw e;\n    }\n  }\n\n  ReadableByteStreamControllerClearAlgorithms(controller);\n  ReadableStreamClose(stream);\n}\n\nfunction ReadableByteStreamControllerEnqueue(controller: ReadableByteStreamController, chunk: ArrayBufferView) {\n  const stream = controller._controlledReadableByteStream;\n\n  if (controller._closeRequested || stream._state !== 'readable') {\n    return;\n  }\n\n  const buffer = chunk.buffer;\n  const byteOffset = chunk.byteOffset;\n  const byteLength = chunk.byteLength;\n  const transferredBuffer = TransferArrayBuffer(buffer);\n\n  if (ReadableStreamHasDefaultReader(stream)) {\n    if (ReadableStreamGetNumReadRequests(stream) === 0) {\n      ReadableByteStreamControllerEnqueueChunkToQueue(controller, transferredBuffer, byteOffset, byteLength);\n    } else {\n      assert(controller._queue.length === 0);\n\n      const transferredView = new Uint8Array(transferredBuffer, byteOffset, byteLength);\n      ReadableStreamFulfillReadRequest(stream, transferredView, false);\n    }\n  } else if (ReadableStreamHasBYOBReader(stream)) {\n    // TODO: Ideally in this branch detaching should happen only if the buffer is not consumed fully.\n    ReadableByteStreamControllerEnqueueChunkToQueue(controller, transferredBuffer, byteOffset, byteLength);\n    ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller);\n  } else {\n    assert(!IsReadableStreamLocked(stream));\n    ReadableByteStreamControllerEnqueueChunkToQueue(controller, transferredBuffer, byteOffset, byteLength);\n  }\n\n  ReadableByteStreamControllerCallPullIfNeeded(controller);\n}\n\nfunction ReadableByteStreamControllerError(controller: ReadableByteStreamController, e: any) {\n  const stream = controller._controlledReadableByteStream;\n\n  if (stream._state !== 'readable') {\n    return;\n  }\n\n  ReadableByteStreamControllerClearPendingPullIntos(controller);\n\n  ResetQueue(controller);\n  ReadableByteStreamControllerClearAlgorithms(controller);\n  ReadableStreamError(stream, e);\n}\n\nfunction ReadableByteStreamControllerGetDesiredSize(controller: ReadableByteStreamController): number | null {\n  const state = controller._controlledReadableByteStream._state;\n\n  if (state === 'errored') {\n    return null;\n  }\n  if (state === 'closed') {\n    return 0;\n  }\n\n  return controller._strategyHWM - controller._queueTotalSize;\n}\n\nfunction ReadableByteStreamControllerRespond(controller: ReadableByteStreamController, bytesWritten: number) {\n  bytesWritten = Number(bytesWritten);\n  if (!IsFiniteNonNegativeNumber(bytesWritten)) {\n    throw new RangeError('bytesWritten must be a finite');\n  }\n\n  assert(controller._pendingPullIntos.length > 0);\n\n  ReadableByteStreamControllerRespondInternal(controller, bytesWritten);\n}\n\nfunction ReadableByteStreamControllerRespondWithNewView(controller: ReadableByteStreamController,\n                                                        view: ArrayBufferView) {\n  assert(controller._pendingPullIntos.length > 0);\n\n  const firstDescriptor = controller._pendingPullIntos.peek();\n\n  if (firstDescriptor.byteOffset + firstDescriptor.bytesFilled !== view.byteOffset) {\n    throw new RangeError('The region specified by view does not match byobRequest');\n  }\n  if (firstDescriptor.byteLength !== view.byteLength) {\n    throw new RangeError('The buffer of view has different capacity than byobRequest');\n  }\n\n  firstDescriptor.buffer = view.buffer;\n\n  ReadableByteStreamControllerRespondInternal(controller, view.byteLength);\n}\n\nexport function SetUpReadableByteStreamController(stream: ReadableByteStream,\n                                                  controller: ReadableByteStreamController,\n                                                  startAlgorithm: () => void | PromiseLike<void>,\n                                                  pullAlgorithm: () => Promise<void>,\n                                                  cancelAlgorithm: (reason: any) => Promise<void>,\n                                                  highWaterMark: number,\n                                                  autoAllocateChunkSize: number | undefined) {\n  assert(stream._readableStreamController === undefined);\n  if (autoAllocateChunkSize !== undefined) {\n    assert(NumberIsInteger(autoAllocateChunkSize));\n    assert(autoAllocateChunkSize > 0);\n  }\n\n  controller._controlledReadableByteStream = stream;\n\n  controller._pullAgain = false;\n  controller._pulling = false;\n\n  controller._byobRequest = null;\n\n  // Need to set the slots so that the assert doesn't fire. In the spec the slots already exist implicitly.\n  controller._queue = controller._queueTotalSize = undefined!;\n  ResetQueue(controller);\n\n  controller._closeRequested = false;\n  controller._started = false;\n\n  controller._strategyHWM = highWaterMark;\n\n  controller._pullAlgorithm = pullAlgorithm;\n  controller._cancelAlgorithm = cancelAlgorithm;\n\n  controller._autoAllocateChunkSize = autoAllocateChunkSize;\n\n  controller._pendingPullIntos = new SimpleQueue();\n\n  stream._readableStreamController = controller;\n\n  const startResult = startAlgorithm();\n  uponPromise(\n    promiseResolvedWith(startResult),\n    () => {\n      controller._started = true;\n\n      assert(!controller._pulling);\n      assert(!controller._pullAgain);\n\n      ReadableByteStreamControllerCallPullIfNeeded(controller);\n    },\n    r => {\n      ReadableByteStreamControllerError(controller, r);\n    }\n  );\n}\n\nexport function SetUpReadableByteStreamControllerFromUnderlyingSource(\n  stream: ReadableByteStream,\n  underlyingByteSource: ValidatedUnderlyingByteSource,\n  highWaterMark: number\n) {\n  const controller: ReadableByteStreamController = Object.create(ReadableByteStreamController.prototype);\n\n  let startAlgorithm: () => void | PromiseLike<void> = () => undefined;\n  let pullAlgorithm: () => Promise<void> = () => promiseResolvedWith(undefined);\n  let cancelAlgorithm: (reason: any) => Promise<void> = () => promiseResolvedWith(undefined);\n\n  if (underlyingByteSource.start !== undefined) {\n    startAlgorithm = () => underlyingByteSource.start!(controller);\n  }\n  if (underlyingByteSource.pull !== undefined) {\n    pullAlgorithm = () => underlyingByteSource.pull!(controller);\n  }\n  if (underlyingByteSource.cancel !== undefined) {\n    cancelAlgorithm = reason => underlyingByteSource.cancel!(reason);\n  }\n\n  const autoAllocateChunkSize = underlyingByteSource.autoAllocateChunkSize;\n\n  SetUpReadableByteStreamController(\n    stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, autoAllocateChunkSize\n  );\n}\n\nfunction SetUpReadableStreamBYOBRequest(request: ReadableStreamBYOBRequest,\n                                        controller: ReadableByteStreamController,\n                                        view: ArrayBufferView) {\n  assert(IsReadableByteStreamController(controller));\n  assert(typeof view === 'object');\n  assert(ArrayBuffer.isView(view));\n  assert(!IsDetachedBuffer(view.buffer));\n  request._associatedReadableByteStreamController = controller;\n  request._view = view;\n}\n\n// Helper functions for the ReadableStreamBYOBRequest.\n\nfunction byobRequestBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableStreamBYOBRequest.prototype.${name} can only be used on a ReadableStreamBYOBRequest`);\n}\n\n// Helper functions for the ReadableByteStreamController.\n\nfunction byteStreamControllerBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableByteStreamController.prototype.${name} can only be used on a ReadableByteStreamController`);\n}\n", "import assert from '../../stub/assert';\nimport { SimpleQueue } from '../simple-queue';\nimport {\n  ReadableStreamReaderGenericCancel,\n  ReadableStreamReaderGenericInitialize,\n  ReadableStreamReaderGenericRelease,\n  readerLockException\n} from './generic-reader';\nimport { IsReadableStreamLocked, ReadableByteStream, ReadableStream } from '../readable-stream';\nimport {\n  IsReadableByteStreamController,\n  ReadableByteStreamController,\n  ReadableByteStreamControllerPullInto\n} from './byte-stream-controller';\nimport { typeIsObject } from '../helpers/miscellaneous';\nimport { newPromise, promiseRejectedWith } from '../helpers/webidl';\nimport { assertRequiredArgument } from '../validators/basic';\nimport { assertReadableStream } from '../validators/readable-stream';\n\n/**\n * A result returned by {@link ReadableStreamBYOBReader.read}.\n *\n * @public\n */\nexport type ReadableStreamBYOBReadResult<T extends ArrayBufferView> = {\n  done: boolean;\n  value: T;\n};\n\n// Abstract operations for the ReadableStream.\n\nexport function AcquireReadableStreamBYOBReader(stream: ReadableStream<Uint8Array>): ReadableStreamBYOBReader {\n  return new ReadableStreamBYOBReader(stream);\n}\n\n// ReadableStream API exposed for controllers.\n\nexport function ReadableStreamAddReadIntoRequest<T extends ArrayBufferView>(stream: ReadableByteStream,\n                                                                            readIntoRequest: ReadIntoRequest<T>): void {\n  assert(IsReadableStreamBYOBReader(stream._reader));\n  assert(stream._state === 'readable' || stream._state === 'closed');\n\n  (stream._reader! as ReadableStreamBYOBReader)._readIntoRequests.push(readIntoRequest);\n}\n\nexport function ReadableStreamFulfillReadIntoRequest(stream: ReadableByteStream,\n                                                     chunk: ArrayBufferView,\n                                                     done: boolean) {\n  const reader = stream._reader as ReadableStreamBYOBReader;\n\n  assert(reader._readIntoRequests.length > 0);\n\n  const readIntoRequest = reader._readIntoRequests.shift()!;\n  if (done) {\n    readIntoRequest._closeSteps(chunk);\n  } else {\n    readIntoRequest._chunkSteps(chunk);\n  }\n}\n\nexport function ReadableStreamGetNumReadIntoRequests(stream: ReadableByteStream): number {\n  return (stream._reader as ReadableStreamBYOBReader)._readIntoRequests.length;\n}\n\nexport function ReadableStreamHasBYOBReader(stream: ReadableByteStream): boolean {\n  const reader = stream._reader;\n\n  if (reader === undefined) {\n    return false;\n  }\n\n  if (!IsReadableStreamBYOBReader(reader)) {\n    return false;\n  }\n\n  return true;\n}\n\n// Readers\n\nexport interface ReadIntoRequest<T extends ArrayBufferView> {\n  _chunkSteps(chunk: T): void;\n\n  _closeSteps(chunk: T): void;\n\n  _errorSteps(e: any): void;\n}\n\n/**\n * A BYOB reader vended by a {@link ReadableStream}.\n *\n * @public\n */\nexport class ReadableStreamBYOBReader {\n  /** @internal */\n  _ownerReadableStream!: ReadableByteStream;\n  /** @internal */\n  _closedPromise!: Promise<void>;\n  /** @internal */\n  _closedPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _closedPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _readIntoRequests: SimpleQueue<ReadIntoRequest<any>>;\n\n  constructor(stream: ReadableByteStream) {\n    assertRequiredArgument(stream, 1, 'ReadableStreamBYOBReader');\n    assertReadableStream(stream, 'First parameter');\n\n    if (IsReadableStreamLocked(stream)) {\n      throw new TypeError('This stream has already been locked for exclusive reading by another reader');\n    }\n\n    if (!IsReadableByteStreamController(stream._readableStreamController)) {\n      throw new TypeError('Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte ' +\n        'source');\n    }\n\n    ReadableStreamReaderGenericInitialize(this, stream);\n\n    this._readIntoRequests = new SimpleQueue();\n  }\n\n  /**\n   * Returns a promise that will be fulfilled when the stream becomes closed, or rejected if the stream ever errors or\n   * the reader's lock is released before the stream finishes closing.\n   */\n  get closed(): Promise<void> {\n    if (!IsReadableStreamBYOBReader(this)) {\n      return promiseRejectedWith(byobReaderBrandCheckException('closed'));\n    }\n\n    return this._closedPromise;\n  }\n\n  /**\n   * If the reader is active, behaves the same as {@link ReadableStream.cancel | stream.cancel(reason)}.\n   */\n  cancel(reason: any = undefined): Promise<void> {\n    if (!IsReadableStreamBYOBReader(this)) {\n      return promiseRejectedWith(byobReaderBrandCheckException('cancel'));\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('cancel'));\n    }\n\n    return ReadableStreamReaderGenericCancel(this, reason);\n  }\n\n  /**\n   * Attempts to reads bytes into view, and returns a promise resolved with the result.\n   *\n   * If reading a chunk causes the queue to become empty, more data will be pulled from the underlying source.\n   */\n  read<T extends ArrayBufferView>(view: T): Promise<ReadableStreamBYOBReadResult<T>> {\n    if (!IsReadableStreamBYOBReader(this)) {\n      return promiseRejectedWith(byobReaderBrandCheckException('read'));\n    }\n\n    if (!ArrayBuffer.isView(view)) {\n      return promiseRejectedWith(new TypeError('view must be an array buffer view'));\n    }\n    if (view.byteLength === 0) {\n      return promiseRejectedWith(new TypeError('view must have non-zero byteLength'));\n    }\n    if (view.buffer.byteLength === 0) {\n      return promiseRejectedWith(new TypeError(`view's buffer must have non-zero byteLength`));\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('read from'));\n    }\n\n    let resolvePromise!: (result: ReadableStreamBYOBReadResult<T>) => void;\n    let rejectPromise!: (reason: any) => void;\n    const promise = newPromise<ReadableStreamBYOBReadResult<T>>((resolve, reject) => {\n      resolvePromise = resolve;\n      rejectPromise = reject;\n    });\n    const readIntoRequest: ReadIntoRequest<T> = {\n      _chunkSteps: chunk => resolvePromise({ value: chunk, done: false }),\n      _closeSteps: chunk => resolvePromise({ value: chunk, done: true }),\n      _errorSteps: e => rejectPromise(e)\n    };\n    ReadableStreamBYOBReaderRead(this, view, readIntoRequest);\n    return promise;\n  }\n\n  /**\n   * Releases the reader's lock on the corresponding stream. After the lock is released, the reader is no longer active.\n   * If the associated stream is errored when the lock is released, the reader will appear errored in the same way\n   * from now on; otherwise, the reader will appear closed.\n   *\n   * A reader's lock cannot be released while it still has a pending read request, i.e., if a promise returned by\n   * the reader's {@link ReadableStreamBYOBReader.read | read()} method has not yet been settled. Attempting to\n   * do so will throw a `TypeError` and leave the reader locked to the stream.\n   */\n  releaseLock(): void {\n    if (!IsReadableStreamBYOBReader(this)) {\n      throw byobReaderBrandCheckException('releaseLock');\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return;\n    }\n\n    if (this._readIntoRequests.length > 0) {\n      throw new TypeError('Tried to release a reader lock when that reader has pending read() calls un-settled');\n    }\n\n    ReadableStreamReaderGenericRelease(this);\n  }\n}\n\nObject.defineProperties(ReadableStreamBYOBReader.prototype, {\n  cancel: { enumerable: true },\n  read: { enumerable: true },\n  releaseLock: { enumerable: true },\n  closed: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStreamBYOBReader.prototype, Symbol.toStringTag, {\n    value: 'ReadableStreamBYOBReader',\n    configurable: true\n  });\n}\n\n// Abstract operations for the readers.\n\nexport function IsReadableStreamBYOBReader(x: any): x is ReadableStreamBYOBReader {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_readIntoRequests')) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction ReadableStreamBYOBReaderRead<T extends ArrayBufferView>(reader: ReadableStreamBYOBReader,\n                                                                 view: T,\n                                                                 readIntoRequest: ReadIntoRequest<T>): void {\n  const stream = reader._ownerReadableStream;\n\n  assert(stream !== undefined);\n\n  stream._disturbed = true;\n\n  if (stream._state === 'errored') {\n    readIntoRequest._errorSteps(stream._storedError);\n  } else {\n    ReadableByteStreamControllerPullInto(\n      stream._readableStreamController as ReadableByteStreamController,\n      view,\n      readIntoRequest\n    );\n  }\n}\n\n// Helper functions for the ReadableStreamBYOBReader.\n\nfunction byobReaderBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableStreamBYOBReader.prototype.${name} can only be used on a ReadableStreamBYOBReader`);\n}\n", "import { QueuingStrategy, QueuingStrategySizeCallback } from '../queuing-strategy';\nimport NumberIsNaN from '../../stub/number-isnan';\n\nexport function ExtractHighWaterMark(strategy: QueuingStrategy, defaultHWM: number): number {\n  const { highWaterMark } = strategy;\n\n  if (highWaterMark === undefined) {\n    return defaultHWM;\n  }\n\n  if (NumberIsNaN(highWaterMark) || highWaterMark < 0) {\n    throw new RangeError('Invalid highWaterMark');\n  }\n\n  return highWaterMark;\n}\n\nexport function ExtractSizeAlgorithm<T>(strategy: QueuingStrategy<T>): QueuingStrategySizeCallback<T> {\n  const { size } = strategy;\n\n  if (!size) {\n    return () => 1;\n  }\n\n  return size;\n}\n", "import { QueuingStrategy, QueuingStrategySizeCallback } from '../queuing-strategy';\nimport { assertDictionary, assertFunction, convertUnrestrictedDouble } from './basic';\n\nexport function convertQueuingStrategy<T>(init: QueuingStrategy<T> | null | undefined,\n                                          context: string): QueuingStrategy<T> {\n  assertDictionary(init, context);\n  const highWaterMark = init?.highWaterMark;\n  const size = init?.size;\n  return {\n    highWaterMark: highWaterMark === undefined ? undefined : convertUnrestrictedDouble(highWaterMark),\n    size: size === undefined ? undefined : convertQueuingStrategySize(size, `${context} has member 'size' that`)\n  };\n}\n\nfunction convertQueuingStrategySize<T>(fn: QueuingStrategySizeCallback<T>,\n                                       context: string): QueuingStrategySizeCallback<T> {\n  assertFunction(fn, context);\n  return chunk => convertUnrestrictedDouble(fn(chunk));\n}\n", "import { assertDictionary, assertFunction } from './basic';\nimport { promiseCall, reflectCall } from '../helpers/webidl';\nimport {\n  UnderlyingSink,\n  UnderlyingSinkAbortCallback,\n  UnderlyingSinkCloseCallback,\n  UnderlyingSinkStartCallback,\n  UnderlyingSinkWriteCallback,\n  ValidatedUnderlyingSink\n} from '../writable-stream/underlying-sink';\nimport { WritableStreamDefaultController } from '../writable-stream';\n\nexport function convertUnderlyingSink<W>(original: UnderlyingSink<W> | null,\n                                         context: string): ValidatedUnderlyingSink<W> {\n  assertDictionary(original, context);\n  const abort = original?.abort;\n  const close = original?.close;\n  const start = original?.start;\n  const type = original?.type;\n  const write = original?.write;\n  return {\n    abort: abort === undefined ?\n      undefined :\n      convertUnderlyingSinkAbortCallback(abort, original!, `${context} has member 'abort' that`),\n    close: close === undefined ?\n      undefined :\n      convertUnderlyingSinkCloseCallback(close, original!, `${context} has member 'close' that`),\n    start: start === undefined ?\n      undefined :\n      convertUnderlyingSinkStartCallback(start, original!, `${context} has member 'start' that`),\n    write: write === undefined ?\n      undefined :\n      convertUnderlyingSinkWriteCallback(write, original!, `${context} has member 'write' that`),\n    type\n  };\n}\n\nfunction convertUnderlyingSinkAbortCallback(\n  fn: UnderlyingSinkAbortCallback,\n  original: UnderlyingSink,\n  context: string\n): (reason: any) => Promise<void> {\n  assertFunction(fn, context);\n  return (reason: any) => promiseCall(fn, original, [reason]);\n}\n\nfunction convertUnderlyingSinkCloseCallback(\n  fn: UnderlyingSinkCloseCallback,\n  original: UnderlyingSink,\n  context: string\n): () => Promise<void> {\n  assertFunction(fn, context);\n  return () => promiseCall(fn, original, []);\n}\n\nfunction convertUnderlyingSinkStartCallback(\n  fn: UnderlyingSinkStartCallback,\n  original: UnderlyingSink,\n  context: string\n): UnderlyingSinkStartCallback {\n  assertFunction(fn, context);\n  return (controller: WritableStreamDefaultController) => reflectCall(fn, original, [controller]);\n}\n\nfunction convertUnderlyingSinkWriteCallback<W>(\n  fn: UnderlyingSinkWriteCallback<W>,\n  original: UnderlyingSink<W>,\n  context: string\n): (chunk: W, controller: WritableStreamDefaultController) => Promise<void> {\n  assertFunction(fn, context);\n  return (chunk: W, controller: WritableStreamDefaultController) => promiseCall(fn, original, [chunk, controller]);\n}\n", "import { IsWritableStream, WritableStream } from '../writable-stream';\n\nexport function assertWritableStream(x: unknown, context: string): asserts x is WritableStream {\n  if (!IsWritableStream(x)) {\n    throw new TypeError(`${context} is not a WritableStream.`);\n  }\n}\n", "import assert from '../stub/assert';\nimport {\n  newPromise,\n  promiseRejectedWith,\n  promiseResolvedWith,\n  setPromiseIsHandledToTrue,\n  uponPromise\n} from './helpers/webidl';\nimport {\n  DequeueValue,\n  EnqueueValueWithSize,\n  PeekQueueValue,\n  QueuePair,\n  ResetQueue\n} from './abstract-ops/queue-with-sizes';\nimport { QueuingStrategy, QueuingStrategySizeCallback } from './queuing-strategy';\nimport { SimpleQueue } from './simple-queue';\nimport { typeIsObject } from './helpers/miscellaneous';\nimport { AbortSteps, ErrorSteps } from './abstract-ops/internal-methods';\nimport { IsNonNegativeNumber } from './abstract-ops/miscellaneous';\nimport { ExtractHighWaterMark, ExtractSizeAlgorithm } from './abstract-ops/queuing-strategy';\nimport { convertQueuingStrategy } from './validators/queuing-strategy';\nimport {\n  UnderlyingSink,\n  UnderlyingSinkAbortCallback,\n  UnderlyingSinkCloseCallback,\n  UnderlyingSinkStartCallback,\n  UnderlyingSinkWriteCallback,\n  ValidatedUnderlyingSink\n} from './writable-stream/underlying-sink';\nimport { assertObject, assertRequiredArgument } from './validators/basic';\nimport { convertUnderlyingSink } from './validators/underlying-sink';\nimport { assertWritableStream } from './validators/writable-stream';\n\ntype WritableStreamState = 'writable' | 'closed' | 'erroring' | 'errored';\n\ninterface WriteOrCloseRequest {\n  _resolve: (value?: undefined) => void;\n  _reject: (reason: any) => void;\n}\n\ntype WriteRequest = WriteOrCloseRequest;\ntype CloseRequest = WriteOrCloseRequest;\n\ninterface PendingAbortRequest {\n  _promise: Promise<void>;\n  _resolve: () => void;\n  _reject: (reason: any) => void;\n  _reason: any;\n  _wasAlreadyErroring: boolean;\n}\n\n/**\n * A writable stream represents a destination for data, into which you can write.\n *\n * @public\n */\nclass WritableStream<W = any> {\n  /** @internal */\n  _state!: WritableStreamState;\n  /** @internal */\n  _storedError: any;\n  /** @internal */\n  _writer: WritableStreamDefaultWriter<W> | undefined;\n  /** @internal */\n  _writableStreamController!: WritableStreamDefaultController<W>;\n  /** @internal */\n  _writeRequests!: SimpleQueue<WriteRequest>;\n  /** @internal */\n  _inFlightWriteRequest: WriteRequest | undefined;\n  /** @internal */\n  _closeRequest: CloseRequest | undefined;\n  /** @internal */\n  _inFlightCloseRequest: CloseRequest | undefined;\n  /** @internal */\n  _pendingAbortRequest: PendingAbortRequest | undefined;\n  /** @internal */\n  _backpressure!: boolean;\n\n  constructor(underlyingSink?: UnderlyingSink<W>, strategy?: QueuingStrategy<W>);\n  constructor(rawUnderlyingSink: UnderlyingSink<W> | null | undefined = {},\n              rawStrategy: QueuingStrategy<W> | null | undefined = {}) {\n    if (rawUnderlyingSink === undefined) {\n      rawUnderlyingSink = null;\n    } else {\n      assertObject(rawUnderlyingSink, 'First parameter');\n    }\n\n    const strategy = convertQueuingStrategy(rawStrategy, 'Second parameter');\n    const underlyingSink = convertUnderlyingSink(rawUnderlyingSink, 'First parameter');\n\n    InitializeWritableStream(this);\n\n    const type = underlyingSink.type;\n    if (type !== undefined) {\n      throw new RangeError('Invalid type is specified');\n    }\n\n    const sizeAlgorithm = ExtractSizeAlgorithm(strategy);\n    const highWaterMark = ExtractHighWaterMark(strategy, 1);\n\n    SetUpWritableStreamDefaultControllerFromUnderlyingSink(this, underlyingSink, highWaterMark, sizeAlgorithm);\n  }\n\n  /**\n   * Returns whether or not the writable stream is locked to a writer.\n   */\n  get locked(): boolean {\n    if (!IsWritableStream(this)) {\n      throw streamBrandCheckException('locked');\n    }\n\n    return IsWritableStreamLocked(this);\n  }\n\n  /**\n   * Aborts the stream, signaling that the producer can no longer successfully write to the stream and it is to be\n   * immediately moved to an errored state, with any queued-up writes discarded. This will also execute any abort\n   * mechanism of the underlying sink.\n   *\n   * The returned promise will fulfill if the stream shuts down successfully, or reject if the underlying sink signaled\n   * that there was an error doing so. Additionally, it will reject with a `TypeError` (without attempting to cancel\n   * the stream) if the stream is currently locked.\n   */\n  abort(reason: any = undefined): Promise<void> {\n    if (!IsWritableStream(this)) {\n      return promiseRejectedWith(streamBrandCheckException('abort'));\n    }\n\n    if (IsWritableStreamLocked(this)) {\n      return promiseRejectedWith(new TypeError('Cannot abort a stream that already has a writer'));\n    }\n\n    return WritableStreamAbort(this, reason);\n  }\n\n  /**\n   * Closes the stream. The underlying sink will finish processing any previously-written chunks, before invoking its\n   * close behavior. During this time any further attempts to write will fail (without erroring the stream).\n   *\n   * The method returns a promise that will fulfill if all remaining chunks are successfully written and the stream\n   * successfully closes, or rejects if an error is encountered during this process. Additionally, it will reject with\n   * a `TypeError` (without attempting to cancel the stream) if the stream is currently locked.\n   */\n  close() {\n    if (!IsWritableStream(this)) {\n      return promiseRejectedWith(streamBrandCheckException('close'));\n    }\n\n    if (IsWritableStreamLocked(this)) {\n      return promiseRejectedWith(new TypeError('Cannot close a stream that already has a writer'));\n    }\n\n    if (WritableStreamCloseQueuedOrInFlight(this)) {\n      return promiseRejectedWith(new TypeError('Cannot close an already-closing stream'));\n    }\n\n    return WritableStreamClose(this);\n  }\n\n  /**\n   * Creates a {@link WritableStreamDefaultWriter | writer} and locks the stream to the new writer. While the stream\n   * is locked, no other writer can be acquired until this one is released.\n   *\n   * This functionality is especially useful for creating abstractions that desire the ability to write to a stream\n   * without interruption or interleaving. By getting a writer for the stream, you can ensure nobody else can write at\n   * the same time, which would cause the resulting written data to be unpredictable and probably useless.\n   */\n  getWriter(): WritableStreamDefaultWriter<W> {\n    if (!IsWritableStream(this)) {\n      throw streamBrandCheckException('getWriter');\n    }\n\n    return AcquireWritableStreamDefaultWriter(this);\n  }\n}\n\nObject.defineProperties(WritableStream.prototype, {\n  abort: { enumerable: true },\n  close: { enumerable: true },\n  getWriter: { enumerable: true },\n  locked: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(WritableStream.prototype, Symbol.toStringTag, {\n    value: 'WritableStream',\n    configurable: true\n  });\n}\n\nexport {\n  AcquireWritableStreamDefaultWriter,\n  CreateWritableStream,\n  IsWritableStream,\n  IsWritableStreamLocked,\n  WritableStream,\n  WritableStreamAbort,\n  WritableStreamDefaultControllerErrorIfNeeded,\n  WritableStreamDefaultWriterCloseWithErrorPropagation,\n  WritableStreamDefaultWriterRelease,\n  WritableStreamDefaultWriterWrite,\n  WritableStreamCloseQueuedOrInFlight,\n  UnderlyingSink,\n  UnderlyingSinkStartCallback,\n  UnderlyingSinkWriteCallback,\n  UnderlyingSinkCloseCallback,\n  UnderlyingSinkAbortCallback\n};\n\n// Abstract operations for the WritableStream.\n\nfunction AcquireWritableStreamDefaultWriter<W>(stream: WritableStream<W>): WritableStreamDefaultWriter<W> {\n  return new WritableStreamDefaultWriter(stream);\n}\n\n// Throws if and only if startAlgorithm throws.\nfunction CreateWritableStream<W>(startAlgorithm: () => void | PromiseLike<void>,\n                                 writeAlgorithm: (chunk: W) => Promise<void>,\n                                 closeAlgorithm: () => Promise<void>,\n                                 abortAlgorithm: (reason: any) => Promise<void>,\n                                 highWaterMark = 1,\n                                 sizeAlgorithm: QueuingStrategySizeCallback<W> = () => 1) {\n  assert(IsNonNegativeNumber(highWaterMark));\n\n  const stream: WritableStream<W> = Object.create(WritableStream.prototype);\n  InitializeWritableStream(stream);\n\n  const controller: WritableStreamDefaultController<W> = Object.create(WritableStreamDefaultController.prototype);\n\n  SetUpWritableStreamDefaultController(stream, controller, startAlgorithm, writeAlgorithm, closeAlgorithm,\n                                       abortAlgorithm, highWaterMark, sizeAlgorithm);\n  return stream;\n}\n\nfunction InitializeWritableStream<W>(stream: WritableStream<W>) {\n  stream._state = 'writable';\n\n  // The error that will be reported by new method calls once the state becomes errored. Only set when [[state]] is\n  // 'erroring' or 'errored'. May be set to an undefined value.\n  stream._storedError = undefined;\n\n  stream._writer = undefined;\n\n  // Initialize to undefined first because the constructor of the controller checks this\n  // variable to validate the caller.\n  stream._writableStreamController = undefined!;\n\n  // This queue is placed here instead of the writer class in order to allow for passing a writer to the next data\n  // producer without waiting for the queued writes to finish.\n  stream._writeRequests = new SimpleQueue();\n\n  // Write requests are removed from _writeRequests when write() is called on the underlying sink. This prevents\n  // them from being erroneously rejected on error. If a write() call is in-flight, the request is stored here.\n  stream._inFlightWriteRequest = undefined;\n\n  // The promise that was returned from writer.close(). Stored here because it may be fulfilled after the writer\n  // has been detached.\n  stream._closeRequest = undefined;\n\n  // Close request is removed from _closeRequest when close() is called on the underlying sink. This prevents it\n  // from being erroneously rejected on error. If a close() call is in-flight, the request is stored here.\n  stream._inFlightCloseRequest = undefined;\n\n  // The promise that was returned from writer.abort(). This may also be fulfilled after the writer has detached.\n  stream._pendingAbortRequest = undefined;\n\n  // The backpressure signal set by the controller.\n  stream._backpressure = false;\n}\n\nfunction IsWritableStream(x: unknown): x is WritableStream {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_writableStreamController')) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction IsWritableStreamLocked(stream: WritableStream): boolean {\n  assert(IsWritableStream(stream));\n\n  if (stream._writer === undefined) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction WritableStreamAbort(stream: WritableStream, reason: any): Promise<void> {\n  const state = stream._state;\n  if (state === 'closed' || state === 'errored') {\n    return promiseResolvedWith(undefined);\n  }\n  if (stream._pendingAbortRequest !== undefined) {\n    return stream._pendingAbortRequest._promise;\n  }\n\n  assert(state === 'writable' || state === 'erroring');\n\n  let wasAlreadyErroring = false;\n  if (state === 'erroring') {\n    wasAlreadyErroring = true;\n    // reason will not be used, so don't keep a reference to it.\n    reason = undefined;\n  }\n\n  const promise = newPromise<void>((resolve, reject) => {\n    stream._pendingAbortRequest = {\n      _promise: undefined!,\n      _resolve: resolve,\n      _reject: reject,\n      _reason: reason,\n      _wasAlreadyErroring: wasAlreadyErroring\n    };\n  });\n  stream._pendingAbortRequest!._promise = promise;\n\n  if (!wasAlreadyErroring) {\n    WritableStreamStartErroring(stream, reason);\n  }\n\n  return promise;\n}\n\nfunction WritableStreamClose(stream: WritableStream<any>): Promise<void> {\n  const state = stream._state;\n  if (state === 'closed' || state === 'errored') {\n    return promiseRejectedWith(new TypeError(\n      `The stream (in ${state} state) is not in the writable state and cannot be closed`));\n  }\n\n  assert(state === 'writable' || state === 'erroring');\n  assert(!WritableStreamCloseQueuedOrInFlight(stream));\n\n  const promise = newPromise<void>((resolve, reject) => {\n    const closeRequest: CloseRequest = {\n      _resolve: resolve,\n      _reject: reject\n    };\n\n    stream._closeRequest = closeRequest;\n  });\n\n  const writer = stream._writer;\n  if (writer !== undefined && stream._backpressure && state === 'writable') {\n    defaultWriterReadyPromiseResolve(writer);\n  }\n\n  WritableStreamDefaultControllerClose(stream._writableStreamController);\n\n  return promise;\n}\n\n// WritableStream API exposed for controllers.\n\nfunction WritableStreamAddWriteRequest(stream: WritableStream): Promise<void> {\n  assert(IsWritableStreamLocked(stream));\n  assert(stream._state === 'writable');\n\n  const promise = newPromise<void>((resolve, reject) => {\n    const writeRequest: WriteRequest = {\n      _resolve: resolve,\n      _reject: reject\n    };\n\n    stream._writeRequests.push(writeRequest);\n  });\n\n  return promise;\n}\n\nfunction WritableStreamDealWithRejection(stream: WritableStream, error: any) {\n  const state = stream._state;\n\n  if (state === 'writable') {\n    WritableStreamStartErroring(stream, error);\n    return;\n  }\n\n  assert(state === 'erroring');\n  WritableStreamFinishErroring(stream);\n}\n\nfunction WritableStreamStartErroring(stream: WritableStream, reason: any) {\n  assert(stream._storedError === undefined);\n  assert(stream._state === 'writable');\n\n  const controller = stream._writableStreamController;\n  assert(controller !== undefined);\n\n  stream._state = 'erroring';\n  stream._storedError = reason;\n  const writer = stream._writer;\n  if (writer !== undefined) {\n    WritableStreamDefaultWriterEnsureReadyPromiseRejected(writer, reason);\n  }\n\n  if (!WritableStreamHasOperationMarkedInFlight(stream) && controller._started) {\n    WritableStreamFinishErroring(stream);\n  }\n}\n\nfunction WritableStreamFinishErroring(stream: WritableStream) {\n  assert(stream._state === 'erroring');\n  assert(!WritableStreamHasOperationMarkedInFlight(stream));\n  stream._state = 'errored';\n  stream._writableStreamController[ErrorSteps]();\n\n  const storedError = stream._storedError;\n  stream._writeRequests.forEach(writeRequest => {\n    writeRequest._reject(storedError);\n  });\n  stream._writeRequests = new SimpleQueue();\n\n  if (stream._pendingAbortRequest === undefined) {\n    WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n    return;\n  }\n\n  const abortRequest = stream._pendingAbortRequest;\n  stream._pendingAbortRequest = undefined;\n\n  if (abortRequest._wasAlreadyErroring) {\n    abortRequest._reject(storedError);\n    WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n    return;\n  }\n\n  const promise = stream._writableStreamController[AbortSteps](abortRequest._reason);\n  uponPromise(\n    promise,\n    () => {\n      abortRequest._resolve();\n      WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n    },\n    (reason: any) => {\n      abortRequest._reject(reason);\n      WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n    });\n}\n\nfunction WritableStreamFinishInFlightWrite(stream: WritableStream) {\n  assert(stream._inFlightWriteRequest !== undefined);\n  stream._inFlightWriteRequest!._resolve(undefined);\n  stream._inFlightWriteRequest = undefined;\n}\n\nfunction WritableStreamFinishInFlightWriteWithError(stream: WritableStream, error: any) {\n  assert(stream._inFlightWriteRequest !== undefined);\n  stream._inFlightWriteRequest!._reject(error);\n  stream._inFlightWriteRequest = undefined;\n\n  assert(stream._state === 'writable' || stream._state === 'erroring');\n\n  WritableStreamDealWithRejection(stream, error);\n}\n\nfunction WritableStreamFinishInFlightClose(stream: WritableStream) {\n  assert(stream._inFlightCloseRequest !== undefined);\n  stream._inFlightCloseRequest!._resolve(undefined);\n  stream._inFlightCloseRequest = undefined;\n\n  const state = stream._state;\n\n  assert(state === 'writable' || state === 'erroring');\n\n  if (state === 'erroring') {\n    // The error was too late to do anything, so it is ignored.\n    stream._storedError = undefined;\n    if (stream._pendingAbortRequest !== undefined) {\n      stream._pendingAbortRequest._resolve();\n      stream._pendingAbortRequest = undefined;\n    }\n  }\n\n  stream._state = 'closed';\n\n  const writer = stream._writer;\n  if (writer !== undefined) {\n    defaultWriterClosedPromiseResolve(writer);\n  }\n\n  assert(stream._pendingAbortRequest === undefined);\n  assert(stream._storedError === undefined);\n}\n\nfunction WritableStreamFinishInFlightCloseWithError(stream: WritableStream, error: any) {\n  assert(stream._inFlightCloseRequest !== undefined);\n  stream._inFlightCloseRequest!._reject(error);\n  stream._inFlightCloseRequest = undefined;\n\n  assert(stream._state === 'writable' || stream._state === 'erroring');\n\n  // Never execute sink abort() after sink close().\n  if (stream._pendingAbortRequest !== undefined) {\n    stream._pendingAbortRequest._reject(error);\n    stream._pendingAbortRequest = undefined;\n  }\n  WritableStreamDealWithRejection(stream, error);\n}\n\n// TODO(ricea): Fix alphabetical order.\nfunction WritableStreamCloseQueuedOrInFlight(stream: WritableStream): boolean {\n  if (stream._closeRequest === undefined && stream._inFlightCloseRequest === undefined) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction WritableStreamHasOperationMarkedInFlight(stream: WritableStream): boolean {\n  if (stream._inFlightWriteRequest === undefined && stream._inFlightCloseRequest === undefined) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction WritableStreamMarkCloseRequestInFlight(stream: WritableStream) {\n  assert(stream._inFlightCloseRequest === undefined);\n  assert(stream._closeRequest !== undefined);\n  stream._inFlightCloseRequest = stream._closeRequest;\n  stream._closeRequest = undefined;\n}\n\nfunction WritableStreamMarkFirstWriteRequestInFlight(stream: WritableStream) {\n  assert(stream._inFlightWriteRequest === undefined);\n  assert(stream._writeRequests.length !== 0);\n  stream._inFlightWriteRequest = stream._writeRequests.shift();\n}\n\nfunction WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream: WritableStream) {\n  assert(stream._state === 'errored');\n  if (stream._closeRequest !== undefined) {\n    assert(stream._inFlightCloseRequest === undefined);\n\n    stream._closeRequest._reject(stream._storedError);\n    stream._closeRequest = undefined;\n  }\n  const writer = stream._writer;\n  if (writer !== undefined) {\n    defaultWriterClosedPromiseReject(writer, stream._storedError);\n  }\n}\n\nfunction WritableStreamUpdateBackpressure(stream: WritableStream, backpressure: boolean) {\n  assert(stream._state === 'writable');\n  assert(!WritableStreamCloseQueuedOrInFlight(stream));\n\n  const writer = stream._writer;\n  if (writer !== undefined && backpressure !== stream._backpressure) {\n    if (backpressure) {\n      defaultWriterReadyPromiseReset(writer);\n    } else {\n      assert(!backpressure);\n\n      defaultWriterReadyPromiseResolve(writer);\n    }\n  }\n\n  stream._backpressure = backpressure;\n}\n\n/**\n * A default writer vended by a {@link WritableStream}.\n *\n * @public\n */\nexport class WritableStreamDefaultWriter<W = any> {\n  /** @internal */\n  _ownerWritableStream: WritableStream<W>;\n  /** @internal */\n  _closedPromise!: Promise<void>;\n  /** @internal */\n  _closedPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _closedPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _closedPromiseState!: 'pending' | 'resolved' | 'rejected';\n  /** @internal */\n  _readyPromise!: Promise<void>;\n  /** @internal */\n  _readyPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _readyPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _readyPromiseState!: 'pending' | 'fulfilled' | 'rejected';\n\n  constructor(stream: WritableStream<W>) {\n    assertRequiredArgument(stream, 1, 'WritableStreamDefaultWriter');\n    assertWritableStream(stream, 'First parameter');\n\n    if (IsWritableStreamLocked(stream)) {\n      throw new TypeError('This stream has already been locked for exclusive writing by another writer');\n    }\n\n    this._ownerWritableStream = stream;\n    stream._writer = this;\n\n    const state = stream._state;\n\n    if (state === 'writable') {\n      if (!WritableStreamCloseQueuedOrInFlight(stream) && stream._backpressure) {\n        defaultWriterReadyPromiseInitialize(this);\n      } else {\n        defaultWriterReadyPromiseInitializeAsResolved(this);\n      }\n\n      defaultWriterClosedPromiseInitialize(this);\n    } else if (state === 'erroring') {\n      defaultWriterReadyPromiseInitializeAsRejected(this, stream._storedError);\n      defaultWriterClosedPromiseInitialize(this);\n    } else if (state === 'closed') {\n      defaultWriterReadyPromiseInitializeAsResolved(this);\n      defaultWriterClosedPromiseInitializeAsResolved(this);\n    } else {\n      assert(state === 'errored');\n\n      const storedError = stream._storedError;\n      defaultWriterReadyPromiseInitializeAsRejected(this, storedError);\n      defaultWriterClosedPromiseInitializeAsRejected(this, storedError);\n    }\n  }\n\n  /**\n   * Returns a promise that will be fulfilled when the stream becomes closed, or rejected if the stream ever errors or\n   * the writer’s lock is released before the stream finishes closing.\n   */\n  get closed(): Promise<void> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('closed'));\n    }\n\n    return this._closedPromise;\n  }\n\n  /**\n   * Returns the desired size to fill the stream’s internal queue. It can be negative, if the queue is over-full.\n   * A producer can use this information to determine the right amount of data to write.\n   *\n   * It will be `null` if the stream cannot be successfully written to (due to either being errored, or having an abort\n   * queued up). It will return zero if the stream is closed. And the getter will throw an exception if invoked when\n   * the writer’s lock is released.\n   */\n  get desiredSize(): number | null {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      throw defaultWriterBrandCheckException('desiredSize');\n    }\n\n    if (this._ownerWritableStream === undefined) {\n      throw defaultWriterLockException('desiredSize');\n    }\n\n    return WritableStreamDefaultWriterGetDesiredSize(this);\n  }\n\n  /**\n   * Returns a promise that will be fulfilled when the desired size to fill the stream’s internal queue transitions\n   * from non-positive to positive, signaling that it is no longer applying backpressure. Once the desired size dips\n   * back to zero or below, the getter will return a new promise that stays pending until the next transition.\n   *\n   * If the stream becomes errored or aborted, or the writer’s lock is released, the returned promise will become\n   * rejected.\n   */\n  get ready(): Promise<void> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('ready'));\n    }\n\n    return this._readyPromise;\n  }\n\n  /**\n   * If the reader is active, behaves the same as {@link WritableStream.abort | stream.abort(reason)}.\n   */\n  abort(reason: any = undefined): Promise<void> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('abort'));\n    }\n\n    if (this._ownerWritableStream === undefined) {\n      return promiseRejectedWith(defaultWriterLockException('abort'));\n    }\n\n    return WritableStreamDefaultWriterAbort(this, reason);\n  }\n\n  /**\n   * If the reader is active, behaves the same as {@link WritableStream.close | stream.close()}.\n   */\n  close(): Promise<void> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('close'));\n    }\n\n    const stream = this._ownerWritableStream;\n\n    if (stream === undefined) {\n      return promiseRejectedWith(defaultWriterLockException('close'));\n    }\n\n    if (WritableStreamCloseQueuedOrInFlight(stream)) {\n      return promiseRejectedWith(new TypeError('Cannot close an already-closing stream'));\n    }\n\n    return WritableStreamDefaultWriterClose(this);\n  }\n\n  /**\n   * Releases the writer’s lock on the corresponding stream. After the lock is released, the writer is no longer active.\n   * If the associated stream is errored when the lock is released, the writer will appear errored in the same way from\n   * now on; otherwise, the writer will appear closed.\n   *\n   * Note that the lock can still be released even if some ongoing writes have not yet finished (i.e. even if the\n   * promises returned from previous calls to {@link WritableStreamDefaultWriter.write | write()} have not yet settled).\n   * It’s not necessary to hold the lock on the writer for the duration of the write; the lock instead simply prevents\n   * other producers from writing in an interleaved manner.\n   */\n  releaseLock(): void {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      throw defaultWriterBrandCheckException('releaseLock');\n    }\n\n    const stream = this._ownerWritableStream;\n\n    if (stream === undefined) {\n      return;\n    }\n\n    assert(stream._writer !== undefined);\n\n    WritableStreamDefaultWriterRelease(this);\n  }\n\n  /**\n   * Writes the given chunk to the writable stream, by waiting until any previous writes have finished successfully,\n   * and then sending the chunk to the underlying sink's {@link UnderlyingSink.write | write()} method. It will return\n   * a promise that fulfills with undefined upon a successful write, or rejects if the write fails or stream becomes\n   * errored before the writing process is initiated.\n   *\n   * Note that what \"success\" means is up to the underlying sink; it might indicate simply that the chunk has been\n   * accepted, and not necessarily that it is safely saved to its ultimate destination.\n   */\n  write(chunk: W): Promise<void>;\n  write(chunk: W = undefined!): Promise<void> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('write'));\n    }\n\n    if (this._ownerWritableStream === undefined) {\n      return promiseRejectedWith(defaultWriterLockException('write to'));\n    }\n\n    return WritableStreamDefaultWriterWrite(this, chunk);\n  }\n}\n\nObject.defineProperties(WritableStreamDefaultWriter.prototype, {\n  abort: { enumerable: true },\n  close: { enumerable: true },\n  releaseLock: { enumerable: true },\n  write: { enumerable: true },\n  closed: { enumerable: true },\n  desiredSize: { enumerable: true },\n  ready: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(WritableStreamDefaultWriter.prototype, Symbol.toStringTag, {\n    value: 'WritableStreamDefaultWriter',\n    configurable: true\n  });\n}\n\n// Abstract operations for the WritableStreamDefaultWriter.\n\nfunction IsWritableStreamDefaultWriter<W = any>(x: any): x is WritableStreamDefaultWriter<W> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_ownerWritableStream')) {\n    return false;\n  }\n\n  return true;\n}\n\n// A client of WritableStreamDefaultWriter may use these functions directly to bypass state check.\n\nfunction WritableStreamDefaultWriterAbort(writer: WritableStreamDefaultWriter, reason: any) {\n  const stream = writer._ownerWritableStream;\n\n  assert(stream !== undefined);\n\n  return WritableStreamAbort(stream, reason);\n}\n\nfunction WritableStreamDefaultWriterClose(writer: WritableStreamDefaultWriter): Promise<void> {\n  const stream = writer._ownerWritableStream;\n\n  assert(stream !== undefined);\n\n  return WritableStreamClose(stream);\n}\n\nfunction WritableStreamDefaultWriterCloseWithErrorPropagation(writer: WritableStreamDefaultWriter): Promise<void> {\n  const stream = writer._ownerWritableStream;\n\n  assert(stream !== undefined);\n\n  const state = stream._state;\n  if (WritableStreamCloseQueuedOrInFlight(stream) || state === 'closed') {\n    return promiseResolvedWith(undefined);\n  }\n\n  if (state === 'errored') {\n    return promiseRejectedWith(stream._storedError);\n  }\n\n  assert(state === 'writable' || state === 'erroring');\n\n  return WritableStreamDefaultWriterClose(writer);\n}\n\nfunction WritableStreamDefaultWriterEnsureClosedPromiseRejected(writer: WritableStreamDefaultWriter, error: any) {\n  if (writer._closedPromiseState === 'pending') {\n    defaultWriterClosedPromiseReject(writer, error);\n  } else {\n    defaultWriterClosedPromiseResetToRejected(writer, error);\n  }\n}\n\nfunction WritableStreamDefaultWriterEnsureReadyPromiseRejected(writer: WritableStreamDefaultWriter, error: any) {\n  if (writer._readyPromiseState === 'pending') {\n    defaultWriterReadyPromiseReject(writer, error);\n  } else {\n    defaultWriterReadyPromiseResetToRejected(writer, error);\n  }\n}\n\nfunction WritableStreamDefaultWriterGetDesiredSize(writer: WritableStreamDefaultWriter): number | null {\n  const stream = writer._ownerWritableStream;\n  const state = stream._state;\n\n  if (state === 'errored' || state === 'erroring') {\n    return null;\n  }\n\n  if (state === 'closed') {\n    return 0;\n  }\n\n  return WritableStreamDefaultControllerGetDesiredSize(stream._writableStreamController);\n}\n\nfunction WritableStreamDefaultWriterRelease(writer: WritableStreamDefaultWriter) {\n  const stream = writer._ownerWritableStream;\n  assert(stream !== undefined);\n  assert(stream._writer === writer);\n\n  const releasedError = new TypeError(\n    `Writer was released and can no longer be used to monitor the stream's closedness`);\n\n  WritableStreamDefaultWriterEnsureReadyPromiseRejected(writer, releasedError);\n\n  // The state transitions to \"errored\" before the sink abort() method runs, but the writer.closed promise is not\n  // rejected until afterwards. This means that simply testing state will not work.\n  WritableStreamDefaultWriterEnsureClosedPromiseRejected(writer, releasedError);\n\n  stream._writer = undefined;\n  writer._ownerWritableStream = undefined!;\n}\n\nfunction WritableStreamDefaultWriterWrite<W>(writer: WritableStreamDefaultWriter<W>, chunk: W): Promise<void> {\n  const stream = writer._ownerWritableStream;\n\n  assert(stream !== undefined);\n\n  const controller = stream._writableStreamController;\n\n  const chunkSize = WritableStreamDefaultControllerGetChunkSize(controller, chunk);\n\n  if (stream !== writer._ownerWritableStream) {\n    return promiseRejectedWith(defaultWriterLockException('write to'));\n  }\n\n  const state = stream._state;\n  if (state === 'errored') {\n    return promiseRejectedWith(stream._storedError);\n  }\n  if (WritableStreamCloseQueuedOrInFlight(stream) || state === 'closed') {\n    return promiseRejectedWith(new TypeError('The stream is closing or closed and cannot be written to'));\n  }\n  if (state === 'erroring') {\n    return promiseRejectedWith(stream._storedError);\n  }\n\n  assert(state === 'writable');\n\n  const promise = WritableStreamAddWriteRequest(stream);\n\n  WritableStreamDefaultControllerWrite(controller, chunk, chunkSize);\n\n  return promise;\n}\n\nconst closeSentinel: unique symbol = {} as any;\n\ntype QueueRecord<W> = W | typeof closeSentinel;\n\n/**\n * Allows control of a {@link WritableStream | writable stream}'s state and internal queue.\n *\n * @public\n */\nexport class WritableStreamDefaultController<W = any> {\n  /** @internal */\n  _controlledWritableStream!: WritableStream<W>;\n  /** @internal */\n  _queue!: SimpleQueue<QueuePair<QueueRecord<W>>>;\n  /** @internal */\n  _queueTotalSize!: number;\n  /** @internal */\n  _started!: boolean;\n  /** @internal */\n  _strategySizeAlgorithm!: QueuingStrategySizeCallback<W>;\n  /** @internal */\n  _strategyHWM!: number;\n  /** @internal */\n  _writeAlgorithm!: (chunk: W) => Promise<void>;\n  /** @internal */\n  _closeAlgorithm!: () => Promise<void>;\n  /** @internal */\n  _abortAlgorithm!: (reason: any) => Promise<void>;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Closes the controlled writable stream, making all future interactions with it fail with the given error `e`.\n   *\n   * This method is rarely used, since usually it suffices to return a rejected promise from one of the underlying\n   * sink's methods. However, it can be useful for suddenly shutting down a stream in response to an event outside the\n   * normal lifecycle of interactions with the underlying sink.\n   */\n  error(e: any = undefined): void {\n    if (!IsWritableStreamDefaultController(this)) {\n      throw new TypeError(\n        'WritableStreamDefaultController.prototype.error can only be used on a WritableStreamDefaultController');\n    }\n    const state = this._controlledWritableStream._state;\n    if (state !== 'writable') {\n      // The stream is closed, errored or will be soon. The sink can't do anything useful if it gets an error here, so\n      // just treat it as a no-op.\n      return;\n    }\n\n    WritableStreamDefaultControllerError(this, e);\n  }\n\n  /** @internal */\n  [AbortSteps](reason: any) {\n    const result = this._abortAlgorithm(reason);\n    WritableStreamDefaultControllerClearAlgorithms(this);\n    return result;\n  }\n\n  /** @internal */\n  [ErrorSteps]() {\n    ResetQueue(this);\n  }\n}\n\nObject.defineProperties(WritableStreamDefaultController.prototype, {\n  error: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(WritableStreamDefaultController.prototype, Symbol.toStringTag, {\n    value: 'WritableStreamDefaultController',\n    configurable: true\n  });\n}\n\n// Abstract operations implementing interface required by the WritableStream.\n\nfunction IsWritableStreamDefaultController(x: any): x is WritableStreamDefaultController<any> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_controlledWritableStream')) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction SetUpWritableStreamDefaultController<W>(stream: WritableStream<W>,\n                                                 controller: WritableStreamDefaultController<W>,\n                                                 startAlgorithm: () => void | PromiseLike<void>,\n                                                 writeAlgorithm: (chunk: W) => Promise<void>,\n                                                 closeAlgorithm: () => Promise<void>,\n                                                 abortAlgorithm: (reason: any) => Promise<void>,\n                                                 highWaterMark: number,\n                                                 sizeAlgorithm: QueuingStrategySizeCallback<W>) {\n  assert(IsWritableStream(stream));\n  assert(stream._writableStreamController === undefined);\n\n  controller._controlledWritableStream = stream;\n  stream._writableStreamController = controller;\n\n  // Need to set the slots so that the assert doesn't fire. In the spec the slots already exist implicitly.\n  controller._queue = undefined!;\n  controller._queueTotalSize = undefined!;\n  ResetQueue(controller);\n\n  controller._started = false;\n\n  controller._strategySizeAlgorithm = sizeAlgorithm;\n  controller._strategyHWM = highWaterMark;\n\n  controller._writeAlgorithm = writeAlgorithm;\n  controller._closeAlgorithm = closeAlgorithm;\n  controller._abortAlgorithm = abortAlgorithm;\n\n  const backpressure = WritableStreamDefaultControllerGetBackpressure(controller);\n  WritableStreamUpdateBackpressure(stream, backpressure);\n\n  const startResult = startAlgorithm();\n  const startPromise = promiseResolvedWith(startResult);\n  uponPromise(\n    startPromise,\n    () => {\n      assert(stream._state === 'writable' || stream._state === 'erroring');\n      controller._started = true;\n      WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n    },\n    r => {\n      assert(stream._state === 'writable' || stream._state === 'erroring');\n      controller._started = true;\n      WritableStreamDealWithRejection(stream, r);\n    }\n  );\n}\n\nfunction SetUpWritableStreamDefaultControllerFromUnderlyingSink<W>(stream: WritableStream<W>,\n                                                                   underlyingSink: ValidatedUnderlyingSink<W>,\n                                                                   highWaterMark: number,\n                                                                   sizeAlgorithm: QueuingStrategySizeCallback<W>) {\n  const controller = Object.create(WritableStreamDefaultController.prototype);\n\n  let startAlgorithm: () => void | PromiseLike<void> = () => undefined;\n  let writeAlgorithm: (chunk: W) => Promise<void> = () => promiseResolvedWith(undefined);\n  let closeAlgorithm: () => Promise<void> = () => promiseResolvedWith(undefined);\n  let abortAlgorithm: (reason: any) => Promise<void> = () => promiseResolvedWith(undefined);\n\n  if (underlyingSink.start !== undefined) {\n    startAlgorithm = () => underlyingSink.start!(controller);\n  }\n  if (underlyingSink.write !== undefined) {\n    writeAlgorithm = chunk => underlyingSink.write!(chunk, controller);\n  }\n  if (underlyingSink.close !== undefined) {\n    closeAlgorithm = () => underlyingSink.close!();\n  }\n  if (underlyingSink.abort !== undefined) {\n    abortAlgorithm = reason => underlyingSink.abort!(reason);\n  }\n\n  SetUpWritableStreamDefaultController(\n    stream, controller, startAlgorithm, writeAlgorithm, closeAlgorithm, abortAlgorithm, highWaterMark, sizeAlgorithm\n  );\n}\n\n// ClearAlgorithms may be called twice. Erroring the same stream in multiple ways will often result in redundant calls.\nfunction WritableStreamDefaultControllerClearAlgorithms(controller: WritableStreamDefaultController<any>) {\n  controller._writeAlgorithm = undefined!;\n  controller._closeAlgorithm = undefined!;\n  controller._abortAlgorithm = undefined!;\n  controller._strategySizeAlgorithm = undefined!;\n}\n\nfunction WritableStreamDefaultControllerClose<W>(controller: WritableStreamDefaultController<W>) {\n  EnqueueValueWithSize(controller, closeSentinel, 0);\n  WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n}\n\nfunction WritableStreamDefaultControllerGetChunkSize<W>(controller: WritableStreamDefaultController<W>,\n                                                        chunk: W): number {\n  try {\n    return controller._strategySizeAlgorithm(chunk);\n  } catch (chunkSizeE) {\n    WritableStreamDefaultControllerErrorIfNeeded(controller, chunkSizeE);\n    return 1;\n  }\n}\n\nfunction WritableStreamDefaultControllerGetDesiredSize(controller: WritableStreamDefaultController<any>): number {\n  return controller._strategyHWM - controller._queueTotalSize;\n}\n\nfunction WritableStreamDefaultControllerWrite<W>(controller: WritableStreamDefaultController<W>,\n                                                 chunk: W,\n                                                 chunkSize: number) {\n  try {\n    EnqueueValueWithSize(controller, chunk, chunkSize);\n  } catch (enqueueE) {\n    WritableStreamDefaultControllerErrorIfNeeded(controller, enqueueE);\n    return;\n  }\n\n  const stream = controller._controlledWritableStream;\n  if (!WritableStreamCloseQueuedOrInFlight(stream) && stream._state === 'writable') {\n    const backpressure = WritableStreamDefaultControllerGetBackpressure(controller);\n    WritableStreamUpdateBackpressure(stream, backpressure);\n  }\n\n  WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n}\n\n// Abstract operations for the WritableStreamDefaultController.\n\nfunction WritableStreamDefaultControllerAdvanceQueueIfNeeded<W>(controller: WritableStreamDefaultController<W>) {\n  const stream = controller._controlledWritableStream;\n\n  if (!controller._started) {\n    return;\n  }\n\n  if (stream._inFlightWriteRequest !== undefined) {\n    return;\n  }\n\n  const state = stream._state;\n  assert(state !== 'closed' && state !== 'errored');\n  if (state === 'erroring') {\n    WritableStreamFinishErroring(stream);\n    return;\n  }\n\n  if (controller._queue.length === 0) {\n    return;\n  }\n\n  const value = PeekQueueValue(controller);\n  if (value === closeSentinel) {\n    WritableStreamDefaultControllerProcessClose(controller);\n  } else {\n    WritableStreamDefaultControllerProcessWrite(controller, value);\n  }\n}\n\nfunction WritableStreamDefaultControllerErrorIfNeeded(controller: WritableStreamDefaultController<any>, error: any) {\n  if (controller._controlledWritableStream._state === 'writable') {\n    WritableStreamDefaultControllerError(controller, error);\n  }\n}\n\nfunction WritableStreamDefaultControllerProcessClose(controller: WritableStreamDefaultController<any>) {\n  const stream = controller._controlledWritableStream;\n\n  WritableStreamMarkCloseRequestInFlight(stream);\n\n  DequeueValue(controller);\n  assert(controller._queue.length === 0);\n\n  const sinkClosePromise = controller._closeAlgorithm();\n  WritableStreamDefaultControllerClearAlgorithms(controller);\n  uponPromise(\n    sinkClosePromise,\n    () => {\n      WritableStreamFinishInFlightClose(stream);\n    },\n    reason => {\n      WritableStreamFinishInFlightCloseWithError(stream, reason);\n    }\n  );\n}\n\nfunction WritableStreamDefaultControllerProcessWrite<W>(controller: WritableStreamDefaultController<W>, chunk: W) {\n  const stream = controller._controlledWritableStream;\n\n  WritableStreamMarkFirstWriteRequestInFlight(stream);\n\n  const sinkWritePromise = controller._writeAlgorithm(chunk);\n  uponPromise(\n    sinkWritePromise,\n    () => {\n      WritableStreamFinishInFlightWrite(stream);\n\n      const state = stream._state;\n      assert(state === 'writable' || state === 'erroring');\n\n      DequeueValue(controller);\n\n      if (!WritableStreamCloseQueuedOrInFlight(stream) && state === 'writable') {\n        const backpressure = WritableStreamDefaultControllerGetBackpressure(controller);\n        WritableStreamUpdateBackpressure(stream, backpressure);\n      }\n\n      WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n    },\n    reason => {\n      if (stream._state === 'writable') {\n        WritableStreamDefaultControllerClearAlgorithms(controller);\n      }\n      WritableStreamFinishInFlightWriteWithError(stream, reason);\n    }\n  );\n}\n\nfunction WritableStreamDefaultControllerGetBackpressure(controller: WritableStreamDefaultController<any>): boolean {\n  const desiredSize = WritableStreamDefaultControllerGetDesiredSize(controller);\n  return desiredSize <= 0;\n}\n\n// A client of WritableStreamDefaultController may use these functions directly to bypass state check.\n\nfunction WritableStreamDefaultControllerError(controller: WritableStreamDefaultController<any>, error: any) {\n  const stream = controller._controlledWritableStream;\n\n  assert(stream._state === 'writable');\n\n  WritableStreamDefaultControllerClearAlgorithms(controller);\n  WritableStreamStartErroring(stream, error);\n}\n\n// Helper functions for the WritableStream.\n\nfunction streamBrandCheckException(name: string): TypeError {\n  return new TypeError(`WritableStream.prototype.${name} can only be used on a WritableStream`);\n}\n\n// Helper functions for the WritableStreamDefaultWriter.\n\nfunction defaultWriterBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `WritableStreamDefaultWriter.prototype.${name} can only be used on a WritableStreamDefaultWriter`);\n}\n\nfunction defaultWriterLockException(name: string): TypeError {\n  return new TypeError('Cannot ' + name + ' a stream using a released writer');\n}\n\nfunction defaultWriterClosedPromiseInitialize(writer: WritableStreamDefaultWriter) {\n  writer._closedPromise = newPromise((resolve, reject) => {\n    writer._closedPromise_resolve = resolve;\n    writer._closedPromise_reject = reject;\n    writer._closedPromiseState = 'pending';\n  });\n}\n\nfunction defaultWriterClosedPromiseInitializeAsRejected(writer: WritableStreamDefaultWriter, reason: any) {\n  defaultWriterClosedPromiseInitialize(writer);\n  defaultWriterClosedPromiseReject(writer, reason);\n}\n\nfunction defaultWriterClosedPromiseInitializeAsResolved(writer: WritableStreamDefaultWriter) {\n  defaultWriterClosedPromiseInitialize(writer);\n  defaultWriterClosedPromiseResolve(writer);\n}\n\nfunction defaultWriterClosedPromiseReject(writer: WritableStreamDefaultWriter, reason: any) {\n  if (writer._closedPromise_reject === undefined) {\n    return;\n  }\n  assert(writer._closedPromiseState === 'pending');\n\n  setPromiseIsHandledToTrue(writer._closedPromise);\n  writer._closedPromise_reject(reason);\n  writer._closedPromise_resolve = undefined;\n  writer._closedPromise_reject = undefined;\n  writer._closedPromiseState = 'rejected';\n}\n\nfunction defaultWriterClosedPromiseResetToRejected(writer: WritableStreamDefaultWriter, reason: any) {\n  assert(writer._closedPromise_resolve === undefined);\n  assert(writer._closedPromise_reject === undefined);\n  assert(writer._closedPromiseState !== 'pending');\n\n  defaultWriterClosedPromiseInitializeAsRejected(writer, reason);\n}\n\nfunction defaultWriterClosedPromiseResolve(writer: WritableStreamDefaultWriter) {\n  if (writer._closedPromise_resolve === undefined) {\n    return;\n  }\n  assert(writer._closedPromiseState === 'pending');\n\n  writer._closedPromise_resolve(undefined);\n  writer._closedPromise_resolve = undefined;\n  writer._closedPromise_reject = undefined;\n  writer._closedPromiseState = 'resolved';\n}\n\nfunction defaultWriterReadyPromiseInitialize(writer: WritableStreamDefaultWriter) {\n  writer._readyPromise = newPromise((resolve, reject) => {\n    writer._readyPromise_resolve = resolve;\n    writer._readyPromise_reject = reject;\n  });\n  writer._readyPromiseState = 'pending';\n}\n\nfunction defaultWriterReadyPromiseInitializeAsRejected(writer: WritableStreamDefaultWriter, reason: any) {\n  defaultWriterReadyPromiseInitialize(writer);\n  defaultWriterReadyPromiseReject(writer, reason);\n}\n\nfunction defaultWriterReadyPromiseInitializeAsResolved(writer: WritableStreamDefaultWriter) {\n  defaultWriterReadyPromiseInitialize(writer);\n  defaultWriterReadyPromiseResolve(writer);\n}\n\nfunction defaultWriterReadyPromiseReject(writer: WritableStreamDefaultWriter, reason: any) {\n  if (writer._readyPromise_reject === undefined) {\n    return;\n  }\n\n  setPromiseIsHandledToTrue(writer._readyPromise);\n  writer._readyPromise_reject(reason);\n  writer._readyPromise_resolve = undefined;\n  writer._readyPromise_reject = undefined;\n  writer._readyPromiseState = 'rejected';\n}\n\nfunction defaultWriterReadyPromiseReset(writer: WritableStreamDefaultWriter) {\n  assert(writer._readyPromise_resolve === undefined);\n  assert(writer._readyPromise_reject === undefined);\n\n  defaultWriterReadyPromiseInitialize(writer);\n}\n\nfunction defaultWriterReadyPromiseResetToRejected(writer: WritableStreamDefaultWriter, reason: any) {\n  assert(writer._readyPromise_resolve === undefined);\n  assert(writer._readyPromise_reject === undefined);\n\n  defaultWriterReadyPromiseInitializeAsRejected(writer, reason);\n}\n\nfunction defaultWriterReadyPromiseResolve(writer: WritableStreamDefaultWriter) {\n  if (writer._readyPromise_resolve === undefined) {\n    return;\n  }\n\n  writer._readyPromise_resolve(undefined);\n  writer._readyPromise_resolve = undefined;\n  writer._readyPromise_reject = undefined;\n  writer._readyPromiseState = 'fulfilled';\n}\n", "/// <reference lib=\"dom\" />\nexport const NativeDOMException: typeof DOMException | undefined = typeof DOMException !== 'undefined' ? DOMException : undefined;\n", "/// <reference types=\"node\" />\nimport { NativeDOMException } from './native';\n\ndeclare class DOMExceptionClass extends Error {\n  constructor(message?: string, name?: string);\n\n  name: string;\n  message: string;\n}\n\ntype DOMException = DOMExceptionClass;\ntype DOMExceptionConstructor = typeof DOMExceptionClass;\n\nfunction isDOMExceptionConstructor(ctor: unknown): ctor is DOMExceptionConstructor {\n  if (!(typeof ctor === 'function' || typeof ctor === 'object')) {\n    return false;\n  }\n  try {\n    new (ctor as DOMExceptionConstructor)();\n    return true;\n  } catch {\n    return false;\n  }\n}\n\nfunction createDOMExceptionPolyfill(): DOMExceptionConstructor {\n  const ctor = function DOMException(this: DOMException, message?: string, name?: string) {\n    this.message = message || '';\n    this.name = name || 'Error';\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor);\n    }\n  } as any;\n  ctor.prototype = Object.create(Error.prototype);\n  Object.defineProperty(ctor.prototype, 'constructor', { value: ctor, writable: true, configurable: true });\n  return ctor;\n}\n\nconst DOMException: DOMExceptionConstructor =\n  isDOMExceptionConstructor(NativeDOMException) ? NativeDOMException : createDOMExceptionPolyfill();\n\nexport { DOMException };\n", "import { IsReadableStream, IsReadableStreamLocked, ReadableStream, ReadableStreamCancel } from '../readable-stream';\nimport { AcquireReadableStreamDefaultReader, ReadableStreamDefaultReaderRead } from './default-reader';\nimport { ReadableStreamReaderGenericRelease } from './generic-reader';\nimport {\n  AcquireWritableStreamDefaultWriter,\n  IsWritableStream,\n  IsWritableStreamLocked,\n  WritableStream,\n  WritableStreamAbort,\n  WritableStreamCloseQueuedOrInFlight,\n  WritableStreamDefaultWriterCloseWithErrorPropagation,\n  WritableStreamDefaultWriterRelease,\n  WritableStreamDefaultWriterWrite\n} from '../writable-stream';\nimport assert from '../../stub/assert';\nimport {\n  newPromise,\n  PerformPromiseThen,\n  promiseResolvedWith,\n  setPromiseIsHandledToTrue,\n  uponFulfillment,\n  uponPromise,\n  uponRejection\n} from '../helpers/webidl';\nimport { noop } from '../../utils';\nimport { AbortSignal, isAbortSignal } from '../abort-signal';\nimport { DOMException } from '../../stub/dom-exception';\n\nexport function ReadableStreamPipeTo<T>(source: ReadableStream<T>,\n                                        dest: WritableStream<T>,\n                                        preventClose: boolean,\n                                        preventAbort: boolean,\n                                        preventCancel: boolean,\n                                        signal: AbortSignal | undefined): Promise<void> {\n  assert(IsReadableStream(source));\n  assert(IsWritableStream(dest));\n  assert(typeof preventClose === 'boolean');\n  assert(typeof preventAbort === 'boolean');\n  assert(typeof preventCancel === 'boolean');\n  assert(signal === undefined || isAbortSignal(signal));\n  assert(!IsReadableStreamLocked(source));\n  assert(!IsWritableStreamLocked(dest));\n\n  const reader = AcquireReadableStreamDefaultReader<T>(source);\n  const writer = AcquireWritableStreamDefaultWriter<T>(dest);\n\n  source._disturbed = true;\n\n  let shuttingDown = false;\n\n  // This is used to keep track of the spec's requirement that we wait for ongoing writes during shutdown.\n  let currentWrite = promiseResolvedWith<void>(undefined);\n\n  return newPromise((resolve, reject) => {\n    let abortAlgorithm: () => void;\n    if (signal !== undefined) {\n      abortAlgorithm = () => {\n        const error = new DOMException('Aborted', 'AbortError');\n        const actions: Array<() => Promise<void>> = [];\n        if (!preventAbort) {\n          actions.push(() => {\n            if (dest._state === 'writable') {\n              return WritableStreamAbort(dest, error);\n            }\n            return promiseResolvedWith(undefined);\n          });\n        }\n        if (!preventCancel) {\n          actions.push(() => {\n            if (source._state === 'readable') {\n              return ReadableStreamCancel(source, error);\n            }\n            return promiseResolvedWith(undefined);\n          });\n        }\n        shutdownWithAction(() => Promise.all(actions.map(action => action())), true, error);\n      };\n\n      if (signal.aborted) {\n        abortAlgorithm();\n        return;\n      }\n\n      signal.addEventListener('abort', abortAlgorithm);\n    }\n\n    // Using reader and writer, read all chunks from this and write them to dest\n    // - Backpressure must be enforced\n    // - Shutdown must stop all activity\n    function pipeLoop() {\n      return newPromise<void>((resolveLoop, rejectLoop) => {\n        function next(done: boolean) {\n          if (done) {\n            resolveLoop();\n          } else {\n            // Use `PerformPromiseThen` instead of `uponPromise` to avoid\n            // adding unnecessary `.catch(rethrowAssertionErrorRejection)` handlers\n            PerformPromiseThen(pipeStep(), next, rejectLoop);\n          }\n        }\n\n        next(false);\n      });\n    }\n\n    function pipeStep(): Promise<boolean> {\n      if (shuttingDown) {\n        return promiseResolvedWith(true);\n      }\n\n      return PerformPromiseThen(writer._readyPromise, () => {\n        return newPromise<boolean>((resolveRead, rejectRead) => {\n          ReadableStreamDefaultReaderRead(\n            reader,\n            {\n              _chunkSteps: chunk => {\n                currentWrite = PerformPromiseThen(WritableStreamDefaultWriterWrite(writer, chunk), undefined, noop);\n                resolveRead(false);\n              },\n              _closeSteps: () => resolveRead(true),\n              _errorSteps: rejectRead\n            }\n          );\n        });\n      });\n    }\n\n    // Errors must be propagated forward\n    isOrBecomesErrored(source, reader._closedPromise, storedError => {\n      if (!preventAbort) {\n        shutdownWithAction(() => WritableStreamAbort(dest, storedError), true, storedError);\n      } else {\n        shutdown(true, storedError);\n      }\n    });\n\n    // Errors must be propagated backward\n    isOrBecomesErrored(dest, writer._closedPromise, storedError => {\n      if (!preventCancel) {\n        shutdownWithAction(() => ReadableStreamCancel(source, storedError), true, storedError);\n      } else {\n        shutdown(true, storedError);\n      }\n    });\n\n    // Closing must be propagated forward\n    isOrBecomesClosed(source, reader._closedPromise, () => {\n      if (!preventClose) {\n        shutdownWithAction(() => WritableStreamDefaultWriterCloseWithErrorPropagation(writer));\n      } else {\n        shutdown();\n      }\n    });\n\n    // Closing must be propagated backward\n    if (WritableStreamCloseQueuedOrInFlight(dest) || dest._state === 'closed') {\n      const destClosed = new TypeError('the destination writable stream closed before all data could be piped to it');\n\n      if (!preventCancel) {\n        shutdownWithAction(() => ReadableStreamCancel(source, destClosed), true, destClosed);\n      } else {\n        shutdown(true, destClosed);\n      }\n    }\n\n    setPromiseIsHandledToTrue(pipeLoop());\n\n    function waitForWritesToFinish(): Promise<void> {\n      // Another write may have started while we were waiting on this currentWrite, so we have to be sure to wait\n      // for that too.\n      const oldCurrentWrite = currentWrite;\n      return PerformPromiseThen(\n        currentWrite,\n        () => oldCurrentWrite !== currentWrite ? waitForWritesToFinish() : undefined\n      );\n    }\n\n    function isOrBecomesErrored(stream: ReadableStream | WritableStream,\n                                promise: Promise<void>,\n                                action: (reason: any) => void) {\n      if (stream._state === 'errored') {\n        action(stream._storedError);\n      } else {\n        uponRejection(promise, action);\n      }\n    }\n\n    function isOrBecomesClosed(stream: ReadableStream | WritableStream, promise: Promise<void>, action: () => void) {\n      if (stream._state === 'closed') {\n        action();\n      } else {\n        uponFulfillment(promise, action);\n      }\n    }\n\n    function shutdownWithAction(action: () => Promise<unknown>, originalIsError?: boolean, originalError?: any) {\n      if (shuttingDown) {\n        return;\n      }\n      shuttingDown = true;\n\n      if (dest._state === 'writable' && !WritableStreamCloseQueuedOrInFlight(dest)) {\n        uponFulfillment(waitForWritesToFinish(), doTheRest);\n      } else {\n        doTheRest();\n      }\n\n      function doTheRest() {\n        uponPromise(\n          action(),\n          () => finalize(originalIsError, originalError),\n          newError => finalize(true, newError)\n        );\n      }\n    }\n\n    function shutdown(isError?: boolean, error?: any) {\n      if (shuttingDown) {\n        return;\n      }\n      shuttingDown = true;\n\n      if (dest._state === 'writable' && !WritableStreamCloseQueuedOrInFlight(dest)) {\n        uponFulfillment(waitForWritesToFinish(), () => finalize(isError, error));\n      } else {\n        finalize(isError, error);\n      }\n    }\n\n    function finalize(isError?: boolean, error?: any) {\n      WritableStreamDefaultWriterRelease(writer);\n      ReadableStreamReaderGenericRelease(reader);\n\n      if (signal !== undefined) {\n        signal.removeEventListener('abort', abortAlgorithm);\n      }\n      if (isError) {\n        reject(error);\n      } else {\n        resolve(undefined);\n      }\n    }\n  });\n}\n", "import { QueuingStrategySizeCallback } from '../queuing-strategy';\nimport assert from '../../stub/assert';\nimport { DequeueValue, EnqueueValueWithSize, QueuePair, ResetQueue } from '../abstract-ops/queue-with-sizes';\nimport {\n  ReadableStreamAddReadRequest,\n  ReadableStreamFulfillReadRequest,\n  ReadableStreamGetNumReadRequests,\n  ReadRequest\n} from './default-reader';\nimport { SimpleQueue } from '../simple-queue';\nimport { IsReadableStreamLocked, ReadableStream, ReadableStreamClose, ReadableStreamError } from '../readable-stream';\nimport { ValidatedUnderlyingSource } from './underlying-source';\nimport { typeIsObject } from '../helpers/miscellaneous';\nimport { CancelSteps, PullSteps } from '../abstract-ops/internal-methods';\nimport { promiseResolvedWith, uponPromise } from '../helpers/webidl';\n\n/**\n * Allows control of a {@link ReadableStream | readable stream}'s state and internal queue.\n *\n * @public\n */\nexport class ReadableStreamDefaultController<R> {\n  /** @internal */\n  _controlledReadableStream!: ReadableStream<R>;\n  /** @internal */\n  _queue!: SimpleQueue<QueuePair<R>>;\n  /** @internal */\n  _queueTotalSize!: number;\n  /** @internal */\n  _started!: boolean;\n  /** @internal */\n  _closeRequested!: boolean;\n  /** @internal */\n  _pullAgain!: boolean;\n  /** @internal */\n  _pulling !: boolean;\n  /** @internal */\n  _strategySizeAlgorithm!: QueuingStrategySizeCallback<R>;\n  /** @internal */\n  _strategyHWM!: number;\n  /** @internal */\n  _pullAlgorithm!: () => Promise<void>;\n  /** @internal */\n  _cancelAlgorithm!: (reason: any) => Promise<void>;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Returns the desired size to fill the controlled stream's internal queue. It can be negative, if the queue is\n   * over-full. An underlying source ought to use this information to determine when and how to apply backpressure.\n   */\n  get desiredSize(): number | null {\n    if (!IsReadableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('desiredSize');\n    }\n\n    return ReadableStreamDefaultControllerGetDesiredSize(this);\n  }\n\n  /**\n   * Closes the controlled readable stream. Consumers will still be able to read any previously-enqueued chunks from\n   * the stream, but once those are read, the stream will become closed.\n   */\n  close(): void {\n    if (!IsReadableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('close');\n    }\n\n    if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(this)) {\n      throw new TypeError('The stream is not in a state that permits close');\n    }\n\n    ReadableStreamDefaultControllerClose(this);\n  }\n\n  /**\n   * Enqueues the given chunk `chunk` in the controlled readable stream.\n   */\n  enqueue(chunk: R): void;\n  enqueue(chunk: R = undefined!): void {\n    if (!IsReadableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('enqueue');\n    }\n\n    if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(this)) {\n      throw new TypeError('The stream is not in a state that permits enqueue');\n    }\n\n    return ReadableStreamDefaultControllerEnqueue(this, chunk);\n  }\n\n  /**\n   * Errors the controlled readable stream, making all future interactions with it fail with the given error `e`.\n   */\n  error(e: any = undefined): void {\n    if (!IsReadableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('error');\n    }\n\n    ReadableStreamDefaultControllerError(this, e);\n  }\n\n  /** @internal */\n  [CancelSteps](reason: any): Promise<void> {\n    ResetQueue(this);\n    const result = this._cancelAlgorithm(reason);\n    ReadableStreamDefaultControllerClearAlgorithms(this);\n    return result;\n  }\n\n  /** @internal */\n  [PullSteps](readRequest: ReadRequest<R>): void {\n    const stream = this._controlledReadableStream;\n\n    if (this._queue.length > 0) {\n      const chunk = DequeueValue(this);\n\n      if (this._closeRequested && this._queue.length === 0) {\n        ReadableStreamDefaultControllerClearAlgorithms(this);\n        ReadableStreamClose(stream);\n      } else {\n        ReadableStreamDefaultControllerCallPullIfNeeded(this);\n      }\n\n      readRequest._chunkSteps(chunk);\n    } else {\n      ReadableStreamAddReadRequest(stream, readRequest);\n      ReadableStreamDefaultControllerCallPullIfNeeded(this);\n    }\n  }\n}\n\nObject.defineProperties(ReadableStreamDefaultController.prototype, {\n  close: { enumerable: true },\n  enqueue: { enumerable: true },\n  error: { enumerable: true },\n  desiredSize: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStreamDefaultController.prototype, Symbol.toStringTag, {\n    value: 'ReadableStreamDefaultController',\n    configurable: true\n  });\n}\n\n// Abstract operations for the ReadableStreamDefaultController.\n\nfunction IsReadableStreamDefaultController<R = any>(x: any): x is ReadableStreamDefaultController<R> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_controlledReadableStream')) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction ReadableStreamDefaultControllerCallPullIfNeeded(controller: ReadableStreamDefaultController<any>): void {\n  const shouldPull = ReadableStreamDefaultControllerShouldCallPull(controller);\n  if (!shouldPull) {\n    return;\n  }\n\n  if (controller._pulling) {\n    controller._pullAgain = true;\n    return;\n  }\n\n  assert(!controller._pullAgain);\n\n  controller._pulling = true;\n\n  const pullPromise = controller._pullAlgorithm();\n  uponPromise(\n    pullPromise,\n    () => {\n      controller._pulling = false;\n\n      if (controller._pullAgain) {\n        controller._pullAgain = false;\n        ReadableStreamDefaultControllerCallPullIfNeeded(controller);\n      }\n    },\n    e => {\n      ReadableStreamDefaultControllerError(controller, e);\n    }\n  );\n}\n\nfunction ReadableStreamDefaultControllerShouldCallPull(controller: ReadableStreamDefaultController<any>): boolean {\n  const stream = controller._controlledReadableStream;\n\n  if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(controller)) {\n    return false;\n  }\n\n  if (!controller._started) {\n    return false;\n  }\n\n  if (IsReadableStreamLocked(stream) && ReadableStreamGetNumReadRequests(stream) > 0) {\n    return true;\n  }\n\n  const desiredSize = ReadableStreamDefaultControllerGetDesiredSize(controller);\n  assert(desiredSize !== null);\n  if (desiredSize! > 0) {\n    return true;\n  }\n\n  return false;\n}\n\nfunction ReadableStreamDefaultControllerClearAlgorithms(controller: ReadableStreamDefaultController<any>) {\n  controller._pullAlgorithm = undefined!;\n  controller._cancelAlgorithm = undefined!;\n  controller._strategySizeAlgorithm = undefined!;\n}\n\n// A client of ReadableStreamDefaultController may use these functions directly to bypass state check.\n\nexport function ReadableStreamDefaultControllerClose(controller: ReadableStreamDefaultController<any>) {\n  if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(controller)) {\n    return;\n  }\n\n  const stream = controller._controlledReadableStream;\n\n  controller._closeRequested = true;\n\n  if (controller._queue.length === 0) {\n    ReadableStreamDefaultControllerClearAlgorithms(controller);\n    ReadableStreamClose(stream);\n  }\n}\n\nexport function ReadableStreamDefaultControllerEnqueue<R>(controller: ReadableStreamDefaultController<R>, chunk: R): void {\n  if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(controller)) {\n    return;\n  }\n\n  const stream = controller._controlledReadableStream;\n\n  if (IsReadableStreamLocked(stream) && ReadableStreamGetNumReadRequests(stream) > 0) {\n    ReadableStreamFulfillReadRequest(stream, chunk, false);\n  } else {\n    let chunkSize;\n    try {\n      chunkSize = controller._strategySizeAlgorithm(chunk);\n    } catch (chunkSizeE) {\n      ReadableStreamDefaultControllerError(controller, chunkSizeE);\n      throw chunkSizeE;\n    }\n\n    try {\n      EnqueueValueWithSize(controller, chunk, chunkSize);\n    } catch (enqueueE) {\n      ReadableStreamDefaultControllerError(controller, enqueueE);\n      throw enqueueE;\n    }\n  }\n\n  ReadableStreamDefaultControllerCallPullIfNeeded(controller);\n}\n\nexport function ReadableStreamDefaultControllerError(controller: ReadableStreamDefaultController<any>, e: any) {\n  const stream = controller._controlledReadableStream;\n\n  if (stream._state !== 'readable') {\n    return;\n  }\n\n  ResetQueue(controller);\n\n  ReadableStreamDefaultControllerClearAlgorithms(controller);\n  ReadableStreamError(stream, e);\n}\n\nexport function ReadableStreamDefaultControllerGetDesiredSize(controller: ReadableStreamDefaultController<any>): number | null {\n  const state = controller._controlledReadableStream._state;\n\n  if (state === 'errored') {\n    return null;\n  }\n  if (state === 'closed') {\n    return 0;\n  }\n\n  return controller._strategyHWM - controller._queueTotalSize;\n}\n\n// This is used in the implementation of TransformStream.\nexport function ReadableStreamDefaultControllerHasBackpressure(controller: ReadableStreamDefaultController<any>): boolean {\n  if (ReadableStreamDefaultControllerShouldCallPull(controller)) {\n    return false;\n  }\n\n  return true;\n}\n\nexport function ReadableStreamDefaultControllerCanCloseOrEnqueue(controller: ReadableStreamDefaultController<any>): boolean {\n  const state = controller._controlledReadableStream._state;\n\n  if (!controller._closeRequested && state === 'readable') {\n    return true;\n  }\n\n  return false;\n}\n\nexport function SetUpReadableStreamDefaultController<R>(stream: ReadableStream<R>,\n                                                        controller: ReadableStreamDefaultController<R>,\n                                                        startAlgorithm: () => void | PromiseLike<void>,\n                                                        pullAlgorithm: () => Promise<void>,\n                                                        cancelAlgorithm: (reason: any) => Promise<void>,\n                                                        highWaterMark: number,\n                                                        sizeAlgorithm: QueuingStrategySizeCallback<R>) {\n  assert(stream._readableStreamController === undefined);\n\n  controller._controlledReadableStream = stream;\n\n  controller._queue = undefined!;\n  controller._queueTotalSize = undefined!;\n  ResetQueue(controller);\n\n  controller._started = false;\n  controller._closeRequested = false;\n  controller._pullAgain = false;\n  controller._pulling = false;\n\n  controller._strategySizeAlgorithm = sizeAlgorithm;\n  controller._strategyHWM = highWaterMark;\n\n  controller._pullAlgorithm = pullAlgorithm;\n  controller._cancelAlgorithm = cancelAlgorithm;\n\n  stream._readableStreamController = controller;\n\n  const startResult = startAlgorithm();\n  uponPromise(\n    promiseResolvedWith(startResult),\n    () => {\n      controller._started = true;\n\n      assert(!controller._pulling);\n      assert(!controller._pullAgain);\n\n      ReadableStreamDefaultControllerCallPullIfNeeded(controller);\n    },\n    r => {\n      ReadableStreamDefaultControllerError(controller, r);\n    }\n  );\n}\n\nexport function SetUpReadableStreamDefaultControllerFromUnderlyingSource<R>(\n  stream: ReadableStream<R>,\n  underlyingSource: ValidatedUnderlyingSource<R>,\n  highWaterMark: number,\n  sizeAlgorithm: QueuingStrategySizeCallback<R>\n) {\n  const controller: ReadableStreamDefaultController<R> = Object.create(ReadableStreamDefaultController.prototype);\n\n  let startAlgorithm: () => void | PromiseLike<void> = () => undefined;\n  let pullAlgorithm: () => Promise<void> = () => promiseResolvedWith(undefined);\n  let cancelAlgorithm: (reason: any) => Promise<void> = () => promiseResolvedWith(undefined);\n\n  if (underlyingSource.start !== undefined) {\n    startAlgorithm = () => underlyingSource.start!(controller);\n  }\n  if (underlyingSource.pull !== undefined) {\n    pullAlgorithm = () => underlyingSource.pull!(controller);\n  }\n  if (underlyingSource.cancel !== undefined) {\n    cancelAlgorithm = reason => underlyingSource.cancel!(reason);\n  }\n\n  SetUpReadableStreamDefaultController(\n    stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, sizeAlgorithm\n  );\n}\n\n// Helper functions for the ReadableStreamDefaultController.\n\nfunction defaultControllerBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableStreamDefaultController.prototype.${name} can only be used on a ReadableStreamDefaultController`);\n}\n", "import { assertDictionary, assertFunction, convertUnsignedLongLongWithEnforceRange } from './basic';\nimport {\n  ReadableStreamController,\n  UnderlyingByteSource,\n  UnderlyingDefaultOrByteSource,\n  UnderlyingDefaultOrByteSourcePullCallback,\n  UnderlyingDefaultOrByteSourceStartCallback,\n  UnderlyingSource,\n  UnderlyingSourceCancelCallback,\n  ValidatedUnderlyingDefaultOrByteSource\n} from '../readable-stream/underlying-source';\nimport { promiseCall, reflectCall } from '../helpers/webidl';\n\nexport function convertUnderlyingDefaultOrByteSource<R>(\n  source: UnderlyingSource<R> | UnderlyingByteSource | null,\n  context: string\n): ValidatedUnderlyingDefaultOrByteSource<R> {\n  assertDictionary(source, context);\n  const original = source as (UnderlyingDefaultOrByteSource<R> | null);\n  const autoAllocateChunkSize = original?.autoAllocateChunkSize;\n  const cancel = original?.cancel;\n  const pull = original?.pull;\n  const start = original?.start;\n  const type = original?.type;\n  return {\n    autoAllocateChunkSize: autoAllocateChunkSize === undefined ?\n      undefined :\n      convertUnsignedLongLongWithEnforceRange(\n        autoAllocateChunkSize,\n        `${context} has member 'autoAllocateChunkSize' that`\n      ),\n    cancel: cancel === undefined ?\n      undefined :\n      convertUnderlyingSourceCancelCallback(cancel, original!, `${context} has member 'cancel' that`),\n    pull: pull === undefined ?\n      undefined :\n      convertUnderlyingSourcePullCallback(pull, original!, `${context} has member 'pull' that`),\n    start: start === undefined ?\n      undefined :\n      convertUnderlyingSourceStartCallback(start, original!, `${context} has member 'start' that`),\n    type: type === undefined ? undefined : convertReadableStreamType(type, `${context} has member 'type' that`)\n  };\n}\n\nfunction convertUnderlyingSourceCancelCallback(\n  fn: UnderlyingSourceCancelCallback,\n  original: UnderlyingDefaultOrByteSource,\n  context: string\n): (reason: any) => Promise<void> {\n  assertFunction(fn, context);\n  return (reason: any) => promiseCall(fn, original, [reason]);\n}\n\nfunction convertUnderlyingSourcePullCallback<R>(\n  fn: UnderlyingDefaultOrByteSourcePullCallback<R>,\n  original: UnderlyingDefaultOrByteSource<R>,\n  context: string\n): (controller: ReadableStreamController<R>) => Promise<void> {\n  assertFunction(fn, context);\n  return (controller: ReadableStreamController<R>) => promiseCall(fn, original, [controller]);\n}\n\nfunction convertUnderlyingSourceStartCallback<R>(\n  fn: UnderlyingDefaultOrByteSourceStartCallback<R>,\n  original: UnderlyingDefaultOrByteSource<R>,\n  context: string\n): UnderlyingDefaultOrByteSourceStartCallback<R> {\n  assertFunction(fn, context);\n  return (controller: ReadableStreamController<R>) => reflectCall(fn, original, [controller]);\n}\n\nfunction convertReadableStreamType(type: string, context: string): 'bytes' {\n  type = `${type}`;\n  if (type !== 'bytes') {\n    throw new TypeError(`${context} '${type}' is not a valid enumeration value for ReadableStreamType`);\n  }\n  return type;\n}\n", "import { assertDictionary } from './basic';\nimport { ReadableStreamGetReaderOptions } from '../readable-stream/reader-options';\n\nexport function convertReaderOptions(options: ReadableStreamGetReaderOptions | null | undefined,\n                                     context: string): ReadableStreamGetReaderOptions {\n  assertDictionary(options, context);\n  const mode = options?.mode;\n  return {\n    mode: mode === undefined ? undefined : convertReadableStreamReaderMode(mode, `${context} has member 'mode' that`)\n  };\n}\n\nfunction convertReadableStreamReaderMode(mode: string, context: string): 'byob' {\n  mode = `${mode}`;\n  if (mode !== 'byob') {\n    throw new TypeError(`${context} '${mode}' is not a valid enumeration value for ReadableStreamReaderMode`);\n  }\n  return mode;\n}\n", "import { assertDictionary } from './basic';\nimport { StreamPipeOptions, ValidatedStreamPipeOptions } from '../readable-stream/pipe-options';\nimport { AbortSignal, isAbortSignal } from '../abort-signal';\n\nexport function convertPipeOptions(options: StreamPipeOptions | null | undefined,\n                                   context: string): ValidatedStreamPipeOptions {\n  assertDictionary(options, context);\n  const preventAbort = options?.preventAbort;\n  const preventCancel = options?.preventCancel;\n  const preventClose = options?.preventClose;\n  const signal = options?.signal;\n  if (signal !== undefined) {\n    assertAbortSignal(signal, `${context} has member 'signal' that`);\n  }\n  return {\n    preventAbort: <PERSON><PERSON><PERSON>(preventAbort),\n    preventCancel: <PERSON><PERSON>an(preventCancel),\n    preventClose: Boolean(preventClose),\n    signal\n  };\n}\n\nfunction assertAbortSignal(signal: unknown, context: string): asserts signal is AbortSignal {\n  if (!isAbortSignal(signal)) {\n    throw new TypeError(`${context} is not an AbortSignal.`);\n  }\n}\n", "/**\n * A signal object that allows you to communicate with a request and abort it if required\n * via its associated `AbortController` object.\n *\n * @remarks\n *   This interface is compatible with the `AbortSignal` interface defined in TypeScript's DOM types.\n *   It is redefined here, so it can be polyfilled without a DOM, for example with\n *   {@link https://www.npmjs.com/package/abortcontroller-polyfill | abortcontroller-polyfill} in a Node environment.\n *\n * @public\n */\nexport interface AbortSignal {\n  /**\n   * Whether the request is aborted.\n   */\n  readonly aborted: boolean;\n\n  /**\n   * Add an event listener to be triggered when this signal becomes aborted.\n   */\n  addEventListener(type: 'abort', listener: () => void): void;\n\n  /**\n   * Remove an event listener that was previously added with {@link AbortSignal.addEventListener}.\n   */\n  removeEventListener(type: 'abort', listener: () => void): void;\n}\n\nexport function isAbortSignal(value: unknown): value is AbortSignal {\n  if (typeof value !== 'object' || value === null) {\n    return false;\n  }\n  try {\n    return typeof (value as AbortSignal).aborted === 'boolean';\n  } catch {\n    // AbortSignal.prototype.aborted throws if its brand check fails\n    return false;\n  }\n}\n", "import assert from '../stub/assert';\nimport {\n  promiseRejectedWith,\n  promiseResolvedWith,\n  setPromiseIsHandledToTrue,\n  transformPromiseWith\n} from './helpers/webidl';\nimport { QueuingStrategy, QueuingStrategySizeCallback } from './queuing-strategy';\nimport { AcquireReadableStreamAsyncIterator, ReadableStreamAsyncIterator } from './readable-stream/async-iterator';\nimport { defaultReaderClosedPromiseReject, defaultReaderClosedPromiseResolve } from './readable-stream/generic-reader';\nimport {\n  AcquireReadableStreamDefaultReader,\n  IsReadableStreamDefaultReader,\n  ReadableStreamDefaultReader,\n  ReadableStreamDefaultReadResult\n} from './readable-stream/default-reader';\nimport {\n  AcquireReadableStreamBYOBReader,\n  IsReadableStreamBYOBReader,\n  ReadableStreamBYOBReader,\n  ReadableStreamBYOBReadResult\n} from './readable-stream/byob-reader';\nimport { ReadableStreamPipeTo } from './readable-stream/pipe';\nimport { ReadableStreamTee } from './readable-stream/tee';\nimport { IsWritableStream, IsWritableStreamLocked, WritableStream } from './writable-stream';\nimport NumberIsInteger from '../stub/number-isinteger';\nimport { SimpleQueue } from './simple-queue';\nimport {\n  ReadableByteStreamController,\n  ReadableStreamBYOBRequest,\n  SetUpReadableByteStreamController,\n  SetUpReadableByteStreamControllerFromUnderlyingSource\n} from './readable-stream/byte-stream-controller';\nimport {\n  ReadableStreamDefaultController,\n  SetUpReadableStreamDefaultController,\n  SetUpReadableStreamDefaultControllerFromUnderlyingSource\n} from './readable-stream/default-controller';\nimport {\n  UnderlyingByteSource,\n  UnderlyingByteSourcePullCallback,\n  UnderlyingByteSourceStartCallback,\n  UnderlyingSource,\n  UnderlyingSourceCancelCallback,\n  UnderlyingSourcePullCallback,\n  UnderlyingSourceStartCallback\n} from './readable-stream/underlying-source';\nimport { noop } from '../utils';\nimport { typeIsObject } from './helpers/miscellaneous';\nimport { CreateArrayFromList } from './abstract-ops/ecmascript';\nimport { CancelSteps } from './abstract-ops/internal-methods';\nimport { IsNonNegativeNumber } from './abstract-ops/miscellaneous';\nimport { assertObject, assertRequiredArgument } from './validators/basic';\nimport { convertQueuingStrategy } from './validators/queuing-strategy';\nimport { ExtractHighWaterMark, ExtractSizeAlgorithm } from './abstract-ops/queuing-strategy';\nimport { convertUnderlyingDefaultOrByteSource } from './validators/underlying-source';\nimport { ReadableStreamGetReaderOptions } from './readable-stream/reader-options';\nimport { convertReaderOptions } from './validators/reader-options';\nimport { StreamPipeOptions, ValidatedStreamPipeOptions } from './readable-stream/pipe-options';\nimport { ReadableStreamIteratorOptions } from './readable-stream/iterator-options';\nimport { convertIteratorOptions } from './validators/iterator-options';\nimport { convertPipeOptions } from './validators/pipe-options';\nimport { ReadableWritablePair } from './readable-stream/readable-writable-pair';\nimport { convertReadableWritablePair } from './validators/readable-writable-pair';\n\nexport type ReadableByteStream = ReadableStream<Uint8Array>;\n\ntype ReadableStreamState = 'readable' | 'closed' | 'errored';\n\n/**\n * A readable stream represents a source of data, from which you can read.\n *\n * @public\n */\nexport class ReadableStream<R = any> {\n  /** @internal */\n  _state!: ReadableStreamState;\n  /** @internal */\n  _reader: ReadableStreamReader<R> | undefined;\n  /** @internal */\n  _storedError: any;\n  /** @internal */\n  _disturbed!: boolean;\n  /** @internal */\n  _readableStreamController!: ReadableStreamDefaultController<R> | ReadableByteStreamController;\n\n  constructor(underlyingSource: UnderlyingByteSource, strategy?: { highWaterMark?: number; size?: undefined });\n  constructor(underlyingSource?: UnderlyingSource<R>, strategy?: QueuingStrategy<R>);\n  constructor(rawUnderlyingSource: UnderlyingSource<R> | UnderlyingByteSource | null | undefined = {},\n              rawStrategy: QueuingStrategy<R> | null | undefined = {}) {\n    if (rawUnderlyingSource === undefined) {\n      rawUnderlyingSource = null;\n    } else {\n      assertObject(rawUnderlyingSource, 'First parameter');\n    }\n\n    const strategy = convertQueuingStrategy(rawStrategy, 'Second parameter');\n    const underlyingSource = convertUnderlyingDefaultOrByteSource(rawUnderlyingSource, 'First parameter');\n\n    InitializeReadableStream(this);\n\n    if (underlyingSource.type === 'bytes') {\n      if (strategy.size !== undefined) {\n        throw new RangeError('The strategy for a byte stream cannot have a size function');\n      }\n      const highWaterMark = ExtractHighWaterMark(strategy, 0);\n      SetUpReadableByteStreamControllerFromUnderlyingSource(\n        this as unknown as ReadableByteStream,\n        underlyingSource,\n        highWaterMark\n      );\n    } else {\n      assert(underlyingSource.type === undefined);\n      const sizeAlgorithm = ExtractSizeAlgorithm(strategy);\n      const highWaterMark = ExtractHighWaterMark(strategy, 1);\n      SetUpReadableStreamDefaultControllerFromUnderlyingSource(\n        this,\n        underlyingSource,\n        highWaterMark,\n        sizeAlgorithm\n      );\n    }\n  }\n\n  /**\n   * Whether or not the readable stream is locked to a {@link ReadableStreamDefaultReader | reader}.\n   */\n  get locked(): boolean {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('locked');\n    }\n\n    return IsReadableStreamLocked(this);\n  }\n\n  /**\n   * Cancels the stream, signaling a loss of interest in the stream by a consumer.\n   *\n   * The supplied `reason` argument will be given to the underlying source's {@link UnderlyingSource.cancel | cancel()}\n   * method, which might or might not use it.\n   */\n  cancel(reason: any = undefined): Promise<void> {\n    if (!IsReadableStream(this)) {\n      return promiseRejectedWith(streamBrandCheckException('cancel'));\n    }\n\n    if (IsReadableStreamLocked(this)) {\n      return promiseRejectedWith(new TypeError('Cannot cancel a stream that already has a reader'));\n    }\n\n    return ReadableStreamCancel(this, reason);\n  }\n\n  /**\n   * Creates a {@link ReadableStreamBYOBReader} and locks the stream to the new reader.\n   *\n   * This call behaves the same way as the no-argument variant, except that it only works on readable byte streams,\n   * i.e. streams which were constructed specifically with the ability to handle \"bring your own buffer\" reading.\n   * The returned BYOB reader provides the ability to directly read individual chunks from the stream via its\n   * {@link ReadableStreamBYOBReader.read | read()} method, into developer-supplied buffers, allowing more precise\n   * control over allocation.\n   */\n  getReader({ mode }: { mode: 'byob' }): ReadableStreamBYOBReader;\n  /**\n   * Creates a {@link ReadableStreamDefaultReader} and locks the stream to the new reader.\n   * While the stream is locked, no other reader can be acquired until this one is released.\n   *\n   * This functionality is especially useful for creating abstractions that desire the ability to consume a stream\n   * in its entirety. By getting a reader for the stream, you can ensure nobody else can interleave reads with yours\n   * or cancel the stream, which would interfere with your abstraction.\n   */\n  getReader(): ReadableStreamDefaultReader<R>;\n  getReader(\n    rawOptions: ReadableStreamGetReaderOptions | null | undefined = undefined\n  ): ReadableStreamDefaultReader<R> | ReadableStreamBYOBReader {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('getReader');\n    }\n\n    const options = convertReaderOptions(rawOptions, 'First parameter');\n\n    if (options.mode === undefined) {\n      return AcquireReadableStreamDefaultReader(this);\n    }\n\n    assert(options.mode === 'byob');\n    return AcquireReadableStreamBYOBReader(this as unknown as ReadableByteStream);\n  }\n\n  /**\n   * Provides a convenient, chainable way of piping this readable stream through a transform stream\n   * (or any other `{ writable, readable }` pair). It simply {@link ReadableStream.pipeTo | pipes} the stream\n   * into the writable side of the supplied pair, and returns the readable side for further use.\n   *\n   * Piping a stream will lock it for the duration of the pipe, preventing any other consumer from acquiring a reader.\n   */\n  pipeThrough<T>(transform: ReadableWritablePair<T, R>, options?: StreamPipeOptions): ReadableStream<T>;\n  pipeThrough<T>(rawTransform: ReadableWritablePair<T, R> | null | undefined,\n                 rawOptions: StreamPipeOptions | null | undefined = {}): ReadableStream<T> {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('pipeThrough');\n    }\n    assertRequiredArgument(rawTransform, 1, 'pipeThrough');\n\n    const transform = convertReadableWritablePair(rawTransform, 'First parameter');\n    const options = convertPipeOptions(rawOptions, 'Second parameter');\n\n    if (IsReadableStreamLocked(this)) {\n      throw new TypeError('ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream');\n    }\n    if (IsWritableStreamLocked(transform.writable)) {\n      throw new TypeError('ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream');\n    }\n\n    const promise = ReadableStreamPipeTo(\n      this, transform.writable, options.preventClose, options.preventAbort, options.preventCancel, options.signal\n    );\n\n    setPromiseIsHandledToTrue(promise);\n\n    return transform.readable;\n  }\n\n  /**\n   * Pipes this readable stream to a given writable stream. The way in which the piping process behaves under\n   * various error conditions can be customized with a number of passed options. It returns a promise that fulfills\n   * when the piping process completes successfully, or rejects if any errors were encountered.\n   *\n   * Piping a stream will lock it for the duration of the pipe, preventing any other consumer from acquiring a reader.\n   */\n  pipeTo(destination: WritableStream<R>, options?: StreamPipeOptions): Promise<void>;\n  pipeTo(destination: WritableStream<R> | null | undefined,\n         rawOptions: StreamPipeOptions | null | undefined = {}): Promise<void> {\n    if (!IsReadableStream(this)) {\n      return promiseRejectedWith(streamBrandCheckException('pipeTo'));\n    }\n\n    if (destination === undefined) {\n      return promiseRejectedWith(`Parameter 1 is required in 'pipeTo'.`);\n    }\n    if (!IsWritableStream(destination)) {\n      return promiseRejectedWith(\n        new TypeError(`ReadableStream.prototype.pipeTo's first argument must be a WritableStream`)\n      );\n    }\n\n    let options: ValidatedStreamPipeOptions;\n    try {\n      options = convertPipeOptions(rawOptions, 'Second parameter');\n    } catch (e) {\n      return promiseRejectedWith(e);\n    }\n\n    if (IsReadableStreamLocked(this)) {\n      return promiseRejectedWith(\n        new TypeError('ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream')\n      );\n    }\n    if (IsWritableStreamLocked(destination)) {\n      return promiseRejectedWith(\n        new TypeError('ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream')\n      );\n    }\n\n    return ReadableStreamPipeTo<R>(\n      this, destination, options.preventClose, options.preventAbort, options.preventCancel, options.signal\n    );\n  }\n\n  /**\n   * Tees this readable stream, returning a two-element array containing the two resulting branches as\n   * new {@link ReadableStream} instances.\n   *\n   * Teeing a stream will lock it, preventing any other consumer from acquiring a reader.\n   * To cancel the stream, cancel both of the resulting branches; a composite cancellation reason will then be\n   * propagated to the stream's underlying source.\n   *\n   * Note that the chunks seen in each branch will be the same object. If the chunks are not immutable,\n   * this could allow interference between the two branches.\n   */\n  tee(): [ReadableStream<R>, ReadableStream<R>] {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('tee');\n    }\n\n    const branches = ReadableStreamTee(this, false);\n    return CreateArrayFromList(branches);\n  }\n\n  /**\n   * Asynchronously iterates over the chunks in the stream's internal queue.\n   *\n   * Asynchronously iterating over the stream will lock it, preventing any other consumer from acquiring a reader.\n   * The lock will be released if the async iterator's {@link ReadableStreamAsyncIterator.return | return()} method\n   * is called, e.g. by breaking out of the loop.\n   *\n   * By default, calling the async iterator's {@link ReadableStreamAsyncIterator.return | return()} method will also\n   * cancel the stream. To prevent this, use the stream's {@link ReadableStream.values | values()} method, passing\n   * `true` for the `preventCancel` option.\n   */\n  values(options?: ReadableStreamIteratorOptions): ReadableStreamAsyncIterator<R>;\n  values(rawOptions: ReadableStreamIteratorOptions | null | undefined = undefined): ReadableStreamAsyncIterator<R> {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('values');\n    }\n\n    const options = convertIteratorOptions(rawOptions, 'First parameter');\n    return AcquireReadableStreamAsyncIterator<R>(this, options.preventCancel);\n  }\n\n  /**\n   * {@inheritDoc ReadableStream.values}\n   */\n  [Symbol.asyncIterator]: (options?: ReadableStreamIteratorOptions) => ReadableStreamAsyncIterator<R>;\n}\n\nObject.defineProperties(ReadableStream.prototype, {\n  cancel: { enumerable: true },\n  getReader: { enumerable: true },\n  pipeThrough: { enumerable: true },\n  pipeTo: { enumerable: true },\n  tee: { enumerable: true },\n  values: { enumerable: true },\n  locked: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStream.prototype, Symbol.toStringTag, {\n    value: 'ReadableStream',\n    configurable: true\n  });\n}\nif (typeof Symbol.asyncIterator === 'symbol') {\n  Object.defineProperty(ReadableStream.prototype, Symbol.asyncIterator, {\n    value: ReadableStream.prototype.values,\n    writable: true,\n    configurable: true\n  });\n}\n\nexport {\n  ReadableStreamAsyncIterator,\n  ReadableStreamDefaultReadResult,\n  ReadableStreamBYOBReadResult,\n  UnderlyingByteSource,\n  UnderlyingSource,\n  UnderlyingSourceStartCallback,\n  UnderlyingSourcePullCallback,\n  UnderlyingSourceCancelCallback,\n  UnderlyingByteSourceStartCallback,\n  UnderlyingByteSourcePullCallback,\n  StreamPipeOptions,\n  ReadableWritablePair,\n  ReadableStreamIteratorOptions\n};\n\n// Abstract operations for the ReadableStream.\n\n// Throws if and only if startAlgorithm throws.\nexport function CreateReadableStream<R>(startAlgorithm: () => void | PromiseLike<void>,\n                                        pullAlgorithm: () => Promise<void>,\n                                        cancelAlgorithm: (reason: any) => Promise<void>,\n                                        highWaterMark = 1,\n                                        sizeAlgorithm: QueuingStrategySizeCallback<R> = () => 1): ReadableStream<R> {\n  assert(IsNonNegativeNumber(highWaterMark));\n\n  const stream: ReadableStream<R> = Object.create(ReadableStream.prototype);\n  InitializeReadableStream(stream);\n\n  const controller: ReadableStreamDefaultController<R> = Object.create(ReadableStreamDefaultController.prototype);\n  SetUpReadableStreamDefaultController(\n    stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, sizeAlgorithm\n  );\n\n  return stream;\n}\n\n// Throws if and only if startAlgorithm throws.\nexport function CreateReadableByteStream(startAlgorithm: () => void | PromiseLike<void>,\n                                         pullAlgorithm: () => Promise<void>,\n                                         cancelAlgorithm: (reason: any) => Promise<void>,\n                                         highWaterMark = 0,\n                                         autoAllocateChunkSize: number | undefined = undefined): ReadableStream<Uint8Array> {\n  assert(IsNonNegativeNumber(highWaterMark));\n  if (autoAllocateChunkSize !== undefined) {\n    assert(NumberIsInteger(autoAllocateChunkSize));\n    assert(autoAllocateChunkSize > 0);\n  }\n\n  const stream: ReadableStream<Uint8Array> = Object.create(ReadableStream.prototype);\n  InitializeReadableStream(stream);\n\n  const controller: ReadableByteStreamController = Object.create(ReadableByteStreamController.prototype);\n\n  SetUpReadableByteStreamController(stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark,\n                                    autoAllocateChunkSize);\n\n  return stream;\n}\n\nfunction InitializeReadableStream(stream: ReadableStream) {\n  stream._state = 'readable';\n  stream._reader = undefined;\n  stream._storedError = undefined;\n  stream._disturbed = false;\n}\n\nexport function IsReadableStream(x: unknown): x is ReadableStream {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_readableStreamController')) {\n    return false;\n  }\n\n  return true;\n}\n\nexport function IsReadableStreamDisturbed(stream: ReadableStream): boolean {\n  assert(IsReadableStream(stream));\n\n  return stream._disturbed;\n}\n\nexport function IsReadableStreamLocked(stream: ReadableStream): boolean {\n  assert(IsReadableStream(stream));\n\n  if (stream._reader === undefined) {\n    return false;\n  }\n\n  return true;\n}\n\n// ReadableStream API exposed for controllers.\n\nexport function ReadableStreamCancel<R>(stream: ReadableStream<R>, reason: any): Promise<void> {\n  stream._disturbed = true;\n\n  if (stream._state === 'closed') {\n    return promiseResolvedWith(undefined);\n  }\n  if (stream._state === 'errored') {\n    return promiseRejectedWith(stream._storedError);\n  }\n\n  ReadableStreamClose(stream);\n\n  const sourceCancelPromise = stream._readableStreamController[CancelSteps](reason);\n  return transformPromiseWith(sourceCancelPromise, noop);\n}\n\nexport function ReadableStreamClose<R>(stream: ReadableStream<R>): void {\n  assert(stream._state === 'readable');\n\n  stream._state = 'closed';\n\n  const reader = stream._reader;\n\n  if (reader === undefined) {\n    return;\n  }\n\n  if (IsReadableStreamDefaultReader<R>(reader)) {\n    reader._readRequests.forEach(readRequest => {\n      readRequest._closeSteps();\n    });\n    reader._readRequests = new SimpleQueue();\n  }\n\n  defaultReaderClosedPromiseResolve(reader);\n}\n\nexport function ReadableStreamError<R>(stream: ReadableStream<R>, e: any): void {\n  assert(IsReadableStream(stream));\n  assert(stream._state === 'readable');\n\n  stream._state = 'errored';\n  stream._storedError = e;\n\n  const reader = stream._reader;\n\n  if (reader === undefined) {\n    return;\n  }\n\n  if (IsReadableStreamDefaultReader<R>(reader)) {\n    reader._readRequests.forEach(readRequest => {\n      readRequest._errorSteps(e);\n    });\n\n    reader._readRequests = new SimpleQueue();\n  } else {\n    assert(IsReadableStreamBYOBReader(reader));\n\n    reader._readIntoRequests.forEach(readIntoRequest => {\n      readIntoRequest._errorSteps(e);\n    });\n\n    reader._readIntoRequests = new SimpleQueue();\n  }\n\n  defaultReaderClosedPromiseReject(reader, e);\n}\n\n// Readers\n\nexport type ReadableStreamReader<R> = ReadableStreamDefaultReader<R> | ReadableStreamBYOBReader;\n\nexport {\n  ReadableStreamDefaultReader,\n  ReadableStreamBYOBReader\n};\n\n// Controllers\n\nexport {\n  ReadableStreamDefaultController,\n  ReadableStreamBYOBRequest,\n  ReadableByteStreamController\n};\n\n// Helper functions for the ReadableStream.\n\nfunction streamBrandCheckException(name: string): TypeError {\n  return new TypeError(`ReadableStream.prototype.${name} can only be used on a ReadableStream`);\n}\n", "import { assertDictionary, assertRequiredField } from './basic';\nimport { ReadableWritablePair } from '../readable-stream/readable-writable-pair';\nimport { assertReadableStream } from './readable-stream';\nimport { assertWritableStream } from './writable-stream';\n\nexport function convertReadableWritablePair<R, W>(pair: ReadableWritablePair<R, W> | null | undefined,\n                                                  context: string): ReadableWritablePair<R, W> {\n  assertDictionary(pair, context);\n\n  const readable = pair?.readable;\n  assertRequiredField(readable, 'readable', 'ReadableWritablePair');\n  assertReadableStream(readable, `${context} has member 'readable' that`);\n\n  const writable = pair?.writable;\n  assertRequiredField(writable, 'writable', 'ReadableWritablePair');\n  assertWritableStream(writable, `${context} has member 'writable' that`);\n\n  return { readable, writable };\n}\n", "import { CreateReadableStream, IsReadableStream, ReadableStream, ReadableStreamCancel } from '../readable-stream';\nimport { AcquireReadableStreamDefaultReader, ReadableStreamDefaultReaderRead, ReadRequest } from './default-reader';\nimport assert from '../../stub/assert';\nimport { newPromise, promiseResolvedWith, queueMicrotask, uponRejection } from '../helpers/webidl';\nimport {\n  ReadableStreamDefaultController,\n  ReadableStreamDefaultControllerClose,\n  ReadableStreamDefaultControllerEnqueue,\n  ReadableStreamDefaultControllerError\n} from './default-controller';\nimport { CreateArrayFromList } from '../abstract-ops/ecmascript';\n\nexport function ReadableStreamTee<R>(stream: ReadableStream<R>,\n                                     cloneForBranch2: boolean): [ReadableStream<R>, ReadableStream<R>] {\n  assert(IsReadableStream(stream));\n  assert(typeof cloneForBranch2 === 'boolean');\n\n  const reader = AcquireReadableStreamDefaultReader<R>(stream);\n\n  let reading = false;\n  let canceled1 = false;\n  let canceled2 = false;\n  let reason1: any;\n  let reason2: any;\n  let branch1: ReadableStream<R>;\n  let branch2: ReadableStream<R>;\n\n  let resolveCancelPromise: (reason: any) => void;\n  const cancelPromise = newPromise<any>(resolve => {\n    resolveCancelPromise = resolve;\n  });\n\n  function pullAlgorithm(): Promise<void> {\n    if (reading) {\n      return promiseResolvedWith(undefined);\n    }\n\n    reading = true;\n\n    const readRequest: ReadRequest<R> = {\n      _chunkSteps: value => {\n        // This needs to be delayed a microtask because it takes at least a microtask to detect errors (using\n        // reader._closedPromise below), and we want errors in stream to error both branches immediately. We cannot let\n        // successful synchronously-available reads get ahead of asynchronously-available errors.\n        queueMicrotask(() => {\n          reading = false;\n          const value1 = value;\n          const value2 = value;\n\n          // There is no way to access the cloning code right now in the reference implementation.\n          // If we add one then we'll need an implementation for serializable objects.\n          // if (!canceled2 && cloneForBranch2) {\n          //   value2 = StructuredDeserialize(StructuredSerialize(value2));\n          // }\n\n          if (!canceled1) {\n            ReadableStreamDefaultControllerEnqueue(\n              branch1._readableStreamController as ReadableStreamDefaultController<R>,\n              value1\n            );\n          }\n\n          if (!canceled2) {\n            ReadableStreamDefaultControllerEnqueue(\n              branch2._readableStreamController as ReadableStreamDefaultController<R>,\n              value2\n            );\n          }\n\n          resolveCancelPromise(undefined);\n        });\n      },\n      _closeSteps: () => {\n        reading = false;\n        if (!canceled1) {\n          ReadableStreamDefaultControllerClose(branch1._readableStreamController as ReadableStreamDefaultController<R>);\n        }\n        if (!canceled2) {\n          ReadableStreamDefaultControllerClose(branch2._readableStreamController as ReadableStreamDefaultController<R>);\n        }\n      },\n      _errorSteps: () => {\n        reading = false;\n      }\n    };\n    ReadableStreamDefaultReaderRead(reader, readRequest);\n\n    return promiseResolvedWith(undefined);\n  }\n\n  function cancel1Algorithm(reason: any): Promise<void> {\n    canceled1 = true;\n    reason1 = reason;\n    if (canceled2) {\n      const compositeReason = CreateArrayFromList([reason1, reason2]);\n      const cancelResult = ReadableStreamCancel(stream, compositeReason);\n      resolveCancelPromise(cancelResult);\n    }\n    return cancelPromise;\n  }\n\n  function cancel2Algorithm(reason: any): Promise<void> {\n    canceled2 = true;\n    reason2 = reason;\n    if (canceled1) {\n      const compositeReason = CreateArrayFromList([reason1, reason2]);\n      const cancelResult = ReadableStreamCancel(stream, compositeReason);\n      resolveCancelPromise(cancelResult);\n    }\n    return cancelPromise;\n  }\n\n  function startAlgorithm() {\n    // do nothing\n  }\n\n  branch1 = CreateReadableStream(startAlgorithm, pullAlgorithm, cancel1Algorithm);\n  branch2 = CreateReadableStream(startAlgorithm, pullAlgorithm, cancel2Algorithm);\n\n  uponRejection(reader._closedPromise, (r: any) => {\n    ReadableStreamDefaultControllerError(branch1._readableStreamController as ReadableStreamDefaultController<R>, r);\n    ReadableStreamDefaultControllerError(branch2._readableStreamController as ReadableStreamDefaultController<R>, r);\n    resolveCancelPromise(undefined);\n  });\n\n  return [branch1, branch2];\n}\n", "import { assertDictionary } from './basic';\nimport {\n  ReadableStreamIteratorOptions,\n  ValidatedReadableStreamIteratorOptions\n} from '../readable-stream/iterator-options';\n\nexport function convertIteratorOptions(options: ReadableStreamIteratorOptions | null | undefined,\n                                       context: string): ValidatedReadableStreamIteratorOptions {\n  assertDictionary(options, context);\n  const preventCancel = options?.preventCancel;\n  return { preventCancel: Boolean(preventCancel) };\n}\n", "import { QueuingStrategyInit } from '../queuing-strategy';\nimport { assertDictionary, assertRequiredField, convertUnrestrictedDouble } from './basic';\n\nexport function convertQueuingStrategyInit(init: QueuingStrategyInit | null | undefined,\n                                           context: string): QueuingStrategyInit {\n  assertDictionary(init, context);\n  const highWaterMark = init?.highWaterMark;\n  assertRequiredField(highWaterMark, 'highWaterMark', 'QueuingStrategyInit');\n  return {\n    highWaterMark: convertUnrestrictedDouble(highWaterMark)\n  };\n}\n", "import { QueuingStrategy, QueuingStrategyInit } from './queuing-strategy';\nimport { typeIsObject } from './helpers/miscellaneous';\nimport { assertRequiredArgument } from './validators/basic';\nimport { convertQueuingStrategyInit } from './validators/queuing-strategy-init';\n\nconst byteLengthSizeFunction = function size(chunk: ArrayBufferView): number {\n  return chunk.byteLength;\n};\n\n/**\n * A queuing strategy that counts the number of bytes in each chunk.\n *\n * @public\n */\nexport default class ByteLengthQueuingStrategy implements QueuingStrategy<ArrayBufferView> {\n  /** @internal */\n  readonly _byteLengthQueuingStrategyHighWaterMark: number;\n\n  constructor(options: QueuingStrategyInit) {\n    assertRequiredArgument(options, 1, 'ByteLengthQueuingStrategy');\n    options = convertQueuingStrategyInit(options, 'First parameter');\n    this._byteLengthQueuingStrategyHighWaterMark = options.highWaterMark;\n  }\n\n  /**\n   * Returns the high water mark provided to the constructor.\n   */\n  get highWaterMark(): number {\n    if (!IsByteLengthQueuingStrategy(this)) {\n      throw byteLengthBrandCheckException('highWaterMark');\n    }\n    return this._byteLengthQueuingStrategyHighWaterMark;\n  }\n\n  /**\n   * Measures the size of `chunk` by returning the value of its `byteLength` property.\n   */\n  get size(): (chunk: ArrayBufferView) => number {\n    if (!IsByteLengthQueuingStrategy(this)) {\n      throw byteLengthBrandCheckException('size');\n    }\n    return byteLengthSizeFunction;\n  }\n}\n\nObject.defineProperties(ByteLengthQueuingStrategy.prototype, {\n  highWaterMark: { enumerable: true },\n  size: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ByteLengthQueuingStrategy.prototype, Symbol.toStringTag, {\n    value: 'ByteLengthQueuingStrategy',\n    configurable: true\n  });\n}\n\n// Helper functions for the ByteLengthQueuingStrategy.\n\nfunction byteLengthBrandCheckException(name: string): TypeError {\n  return new TypeError(`ByteLengthQueuingStrategy.prototype.${name} can only be used on a ByteLengthQueuingStrategy`);\n}\n\nexport function IsByteLengthQueuingStrategy(x: any): x is ByteLengthQueuingStrategy {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_byteLengthQueuingStrategyHighWaterMark')) {\n    return false;\n  }\n\n  return true;\n}\n", "import { QueuingStrategy, QueuingStrategyInit } from './queuing-strategy';\nimport { typeIsObject } from './helpers/miscellaneous';\nimport { assertRequiredArgument } from './validators/basic';\nimport { convertQueuingStrategyInit } from './validators/queuing-strategy-init';\n\nconst countSizeFunction = function size(): 1 {\n  return 1;\n};\n\n/**\n * A queuing strategy that counts the number of chunks.\n *\n * @public\n */\nexport default class CountQueuingStrategy implements QueuingStrategy<any> {\n  /** @internal */\n  readonly _countQueuingStrategyHighWaterMark!: number;\n\n  constructor(options: QueuingStrategyInit) {\n    assertRequiredArgument(options, 1, 'CountQueuingStrategy');\n    options = convertQueuingStrategyInit(options, 'First parameter');\n    this._countQueuingStrategyHighWaterMark = options.highWaterMark;\n  }\n\n  /**\n   * Returns the high water mark provided to the constructor.\n   */\n  get highWaterMark(): number {\n    if (!IsCountQueuingStrategy(this)) {\n      throw countBrandCheckException('highWaterMark');\n    }\n    return this._countQueuingStrategyHighWaterMark;\n  }\n\n  /**\n   * Measures the size of `chunk` by always returning 1.\n   * This ensures that the total queue size is a count of the number of chunks in the queue.\n   */\n  get size(): (chunk: any) => 1 {\n    if (!IsCountQueuingStrategy(this)) {\n      throw countBrandCheckException('size');\n    }\n    return countSizeFunction;\n  }\n}\n\nObject.defineProperties(CountQueuingStrategy.prototype, {\n  highWaterMark: { enumerable: true },\n  size: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(CountQueuingStrategy.prototype, Symbol.toStringTag, {\n    value: 'CountQueuingStrategy',\n    configurable: true\n  });\n}\n\n// Helper functions for the CountQueuingStrategy.\n\nfunction countBrandCheckException(name: string): TypeError {\n  return new TypeError(`CountQueuingStrategy.prototype.${name} can only be used on a CountQueuingStrategy`);\n}\n\nexport function IsCountQueuingStrategy(x: any): x is CountQueuingStrategy {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_countQueuingStrategyHighWaterMark')) {\n    return false;\n  }\n\n  return true;\n}\n", "import { assertDictionary, assertFunction } from './basic';\nimport { promiseCall, reflectCall } from '../helpers/webidl';\nimport {\n  Transformer,\n  TransformerFlushCallback,\n  TransformerStartCallback,\n  TransformerTransformCallback,\n  ValidatedTransformer\n} from '../transform-stream/transformer';\nimport { TransformStreamDefaultController } from '../transform-stream';\n\nexport function convertTransformer<I, O>(original: Transformer<I, O> | null,\n                                         context: string): ValidatedTransformer<I, O> {\n  assertDictionary(original, context);\n  const flush = original?.flush;\n  const readableType = original?.readableType;\n  const start = original?.start;\n  const transform = original?.transform;\n  const writableType = original?.writableType;\n  return {\n    flush: flush === undefined ?\n      undefined :\n      convertTransformerFlushCallback(flush, original!, `${context} has member 'flush' that`),\n    readableType,\n    start: start === undefined ?\n      undefined :\n      convertTransformerStartCallback(start, original!, `${context} has member 'start' that`),\n    transform: transform === undefined ?\n      undefined :\n      convertTransformerTransformCallback(transform, original!, `${context} has member 'transform' that`),\n    writableType\n  };\n}\n\nfunction convertTransformerFlushCallback<I, O>(\n  fn: TransformerFlushCallback<O>,\n  original: Transformer<I, O>,\n  context: string\n): (controller: TransformStreamDefaultController<O>) => Promise<void> {\n  assertFunction(fn, context);\n  return (controller: TransformStreamDefaultController<O>) => promiseCall(fn, original, [controller]);\n}\n\nfunction convertTransformerStartCallback<I, O>(\n  fn: TransformerStartCallback<O>,\n  original: Transformer<I, O>,\n  context: string\n): TransformerStartCallback<O> {\n  assertFunction(fn, context);\n  return (controller: TransformStreamDefaultController<O>) => reflectCall(fn, original, [controller]);\n}\n\nfunction convertTransformerTransformCallback<I, O>(\n  fn: TransformerTransformCallback<I, O>,\n  original: Transformer<I, O>,\n  context: string\n): (chunk: I, controller: TransformStreamDefaultController<O>) => Promise<void> {\n  assertFunction(fn, context);\n  return (chunk: I, controller: TransformStreamDefaultController<O>) => promiseCall(fn, original, [chunk, controller]);\n}\n", "import assert from '../stub/assert';\nimport { newPromise, promiseRejectedWith, promiseResolvedWith, transformPromiseWith } from './helpers/webidl';\nimport { CreateReadableStream, ReadableStream, ReadableStreamDefaultController } from './readable-stream';\nimport {\n  ReadableStreamDefaultControllerCanCloseOrEnqueue,\n  ReadableStreamDefaultControllerClose,\n  ReadableStreamDefaultControllerEnqueue,\n  ReadableStreamDefaultControllerError,\n  ReadableStreamDefaultControllerGetDesiredSize,\n  ReadableStreamDefaultControllerHasBackpressure\n} from './readable-stream/default-controller';\nimport { QueuingStrategy, QueuingStrategySizeCallback } from './queuing-strategy';\nimport { CreateWritableStream, WritableStream, WritableStreamDefaultControllerErrorIfNeeded } from './writable-stream';\nimport { typeIsObject } from './helpers/miscellaneous';\nimport { IsNonNegativeNumber } from './abstract-ops/miscellaneous';\nimport { convertQueuingStrategy } from './validators/queuing-strategy';\nimport { ExtractHighWaterMark, ExtractSizeAlgorithm } from './abstract-ops/queuing-strategy';\nimport {\n  Transformer,\n  TransformerFlushCallback,\n  TransformerStartCallback,\n  TransformerTransformCallback,\n  ValidatedTransformer\n} from './transform-stream/transformer';\nimport { convertTransformer } from './validators/transformer';\n\n// Class TransformStream\n\n/**\n * A transform stream consists of a pair of streams: a {@link WritableStream | writable stream},\n * known as its writable side, and a {@link ReadableStream | readable stream}, known as its readable side.\n * In a manner specific to the transform stream in question, writes to the writable side result in new data being\n * made available for reading from the readable side.\n *\n * @public\n */\nexport class TransformStream<I = any, O = any> {\n  /** @internal */\n  _writable!: WritableStream<I>;\n  /** @internal */\n  _readable!: ReadableStream<O>;\n  /** @internal */\n  _backpressure!: boolean;\n  /** @internal */\n  _backpressureChangePromise!: Promise<void>;\n  /** @internal */\n  _backpressureChangePromise_resolve!: () => void;\n  /** @internal */\n  _transformStreamController!: TransformStreamDefaultController<O>;\n\n  constructor(\n    transformer?: Transformer<I, O>,\n    writableStrategy?: QueuingStrategy<I>,\n    readableStrategy?: QueuingStrategy<O>\n  );\n  constructor(rawTransformer: Transformer<I, O> | null | undefined = {},\n              rawWritableStrategy: QueuingStrategy<I> | null | undefined = {},\n              rawReadableStrategy: QueuingStrategy<O> | null | undefined = {}) {\n    if (rawTransformer === undefined) {\n      rawTransformer = null;\n    }\n\n    const writableStrategy = convertQueuingStrategy(rawWritableStrategy, 'Second parameter');\n    const readableStrategy = convertQueuingStrategy(rawReadableStrategy, 'Third parameter');\n\n    const transformer = convertTransformer(rawTransformer, 'First parameter');\n    if (transformer.readableType !== undefined) {\n      throw new RangeError('Invalid readableType specified');\n    }\n    if (transformer.writableType !== undefined) {\n      throw new RangeError('Invalid writableType specified');\n    }\n\n    const readableHighWaterMark = ExtractHighWaterMark(readableStrategy, 0);\n    const readableSizeAlgorithm = ExtractSizeAlgorithm(readableStrategy);\n    const writableHighWaterMark = ExtractHighWaterMark(writableStrategy, 1);\n    const writableSizeAlgorithm = ExtractSizeAlgorithm(writableStrategy);\n\n    let startPromise_resolve!: (value: void | PromiseLike<void>) => void;\n    const startPromise = newPromise<void>(resolve => {\n      startPromise_resolve = resolve;\n    });\n\n    InitializeTransformStream(\n      this, startPromise, writableHighWaterMark, writableSizeAlgorithm, readableHighWaterMark, readableSizeAlgorithm\n    );\n    SetUpTransformStreamDefaultControllerFromTransformer(this, transformer);\n\n    if (transformer.start !== undefined) {\n      startPromise_resolve(transformer.start(this._transformStreamController));\n    } else {\n      startPromise_resolve(undefined);\n    }\n  }\n\n  /**\n   * The readable side of the transform stream.\n   */\n  get readable(): ReadableStream<O> {\n    if (!IsTransformStream(this)) {\n      throw streamBrandCheckException('readable');\n    }\n\n    return this._readable;\n  }\n\n  /**\n   * The writable side of the transform stream.\n   */\n  get writable(): WritableStream<I> {\n    if (!IsTransformStream(this)) {\n      throw streamBrandCheckException('writable');\n    }\n\n    return this._writable;\n  }\n}\n\nObject.defineProperties(TransformStream.prototype, {\n  readable: { enumerable: true },\n  writable: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(TransformStream.prototype, Symbol.toStringTag, {\n    value: 'TransformStream',\n    configurable: true\n  });\n}\n\nexport {\n  Transformer,\n  TransformerStartCallback,\n  TransformerFlushCallback,\n  TransformerTransformCallback\n};\n\n// Transform Stream Abstract Operations\n\nexport function CreateTransformStream<I, O>(startAlgorithm: () => void | PromiseLike<void>,\n                                            transformAlgorithm: (chunk: I) => Promise<void>,\n                                            flushAlgorithm: () => Promise<void>,\n                                            writableHighWaterMark = 1,\n                                            writableSizeAlgorithm: QueuingStrategySizeCallback<I> = () => 1,\n                                            readableHighWaterMark = 0,\n                                            readableSizeAlgorithm: QueuingStrategySizeCallback<O> = () => 1) {\n  assert(IsNonNegativeNumber(writableHighWaterMark));\n  assert(IsNonNegativeNumber(readableHighWaterMark));\n\n  const stream: TransformStream<I, O> = Object.create(TransformStream.prototype);\n\n  let startPromise_resolve!: (value: void | PromiseLike<void>) => void;\n  const startPromise = newPromise<void>(resolve => {\n    startPromise_resolve = resolve;\n  });\n\n  InitializeTransformStream(stream, startPromise, writableHighWaterMark, writableSizeAlgorithm, readableHighWaterMark,\n                            readableSizeAlgorithm);\n\n  const controller: TransformStreamDefaultController<O> = Object.create(TransformStreamDefaultController.prototype);\n\n  SetUpTransformStreamDefaultController(stream, controller, transformAlgorithm, flushAlgorithm);\n\n  const startResult = startAlgorithm();\n  startPromise_resolve(startResult);\n  return stream;\n}\n\nfunction InitializeTransformStream<I, O>(stream: TransformStream<I, O>,\n                                         startPromise: Promise<void>,\n                                         writableHighWaterMark: number,\n                                         writableSizeAlgorithm: QueuingStrategySizeCallback<I>,\n                                         readableHighWaterMark: number,\n                                         readableSizeAlgorithm: QueuingStrategySizeCallback<O>) {\n  function startAlgorithm(): Promise<void> {\n    return startPromise;\n  }\n\n  function writeAlgorithm(chunk: I): Promise<void> {\n    return TransformStreamDefaultSinkWriteAlgorithm(stream, chunk);\n  }\n\n  function abortAlgorithm(reason: any): Promise<void> {\n    return TransformStreamDefaultSinkAbortAlgorithm(stream, reason);\n  }\n\n  function closeAlgorithm(): Promise<void> {\n    return TransformStreamDefaultSinkCloseAlgorithm(stream);\n  }\n\n  stream._writable = CreateWritableStream(startAlgorithm, writeAlgorithm, closeAlgorithm, abortAlgorithm,\n                                          writableHighWaterMark, writableSizeAlgorithm);\n\n  function pullAlgorithm(): Promise<void> {\n    return TransformStreamDefaultSourcePullAlgorithm(stream);\n  }\n\n  function cancelAlgorithm(reason: any): Promise<void> {\n    TransformStreamErrorWritableAndUnblockWrite(stream, reason);\n    return promiseResolvedWith(undefined);\n  }\n\n  stream._readable = CreateReadableStream(startAlgorithm, pullAlgorithm, cancelAlgorithm, readableHighWaterMark,\n                                          readableSizeAlgorithm);\n\n  // The [[backpressure]] slot is set to undefined so that it can be initialised by TransformStreamSetBackpressure.\n  stream._backpressure = undefined!;\n  stream._backpressureChangePromise = undefined!;\n  stream._backpressureChangePromise_resolve = undefined!;\n  TransformStreamSetBackpressure(stream, true);\n\n  stream._transformStreamController = undefined!;\n}\n\nfunction IsTransformStream(x: unknown): x is TransformStream {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_transformStreamController')) {\n    return false;\n  }\n\n  return true;\n}\n\n// This is a no-op if both sides are already errored.\nfunction TransformStreamError(stream: TransformStream, e: any) {\n  ReadableStreamDefaultControllerError(stream._readable._readableStreamController as ReadableStreamDefaultController<any>,\n                                       e);\n  TransformStreamErrorWritableAndUnblockWrite(stream, e);\n}\n\nfunction TransformStreamErrorWritableAndUnblockWrite(stream: TransformStream, e: any) {\n  TransformStreamDefaultControllerClearAlgorithms(stream._transformStreamController);\n  WritableStreamDefaultControllerErrorIfNeeded(stream._writable._writableStreamController, e);\n  if (stream._backpressure) {\n    // Pretend that pull() was called to permit any pending write() calls to complete. TransformStreamSetBackpressure()\n    // cannot be called from enqueue() or pull() once the ReadableStream is errored, so this will will be the final time\n    // _backpressure is set.\n    TransformStreamSetBackpressure(stream, false);\n  }\n}\n\nfunction TransformStreamSetBackpressure(stream: TransformStream, backpressure: boolean) {\n  // Passes also when called during construction.\n  assert(stream._backpressure !== backpressure);\n\n  if (stream._backpressureChangePromise !== undefined) {\n    stream._backpressureChangePromise_resolve();\n  }\n\n  stream._backpressureChangePromise = newPromise(resolve => {\n    stream._backpressureChangePromise_resolve = resolve;\n  });\n\n  stream._backpressure = backpressure;\n}\n\n// Class TransformStreamDefaultController\n\n/**\n * Allows control of the {@link ReadableStream} and {@link WritableStream} of the associated {@link TransformStream}.\n *\n * @public\n */\nexport class TransformStreamDefaultController<O> {\n  /** @internal */\n  _controlledTransformStream: TransformStream<any, O>;\n  /** @internal */\n  _transformAlgorithm: (chunk: any) => Promise<void>;\n  /** @internal */\n  _flushAlgorithm: () => Promise<void>;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Returns the desired size to fill the readable side’s internal queue. It can be negative, if the queue is over-full.\n   */\n  get desiredSize(): number | null {\n    if (!IsTransformStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('desiredSize');\n    }\n\n    const readableController = this._controlledTransformStream._readable._readableStreamController;\n    return ReadableStreamDefaultControllerGetDesiredSize(readableController as ReadableStreamDefaultController<O>);\n  }\n\n  /**\n   * Enqueues the given chunk `chunk` in the readable side of the controlled transform stream.\n   */\n  enqueue(chunk: O): void;\n  enqueue(chunk: O = undefined!): void {\n    if (!IsTransformStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('enqueue');\n    }\n\n    TransformStreamDefaultControllerEnqueue(this, chunk);\n  }\n\n  /**\n   * Errors both the readable side and the writable side of the controlled transform stream, making all future\n   * interactions with it fail with the given error `e`. Any chunks queued for transformation will be discarded.\n   */\n  error(reason: any = undefined): void {\n    if (!IsTransformStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('error');\n    }\n\n    TransformStreamDefaultControllerError(this, reason);\n  }\n\n  /**\n   * Closes the readable side and errors the writable side of the controlled transform stream. This is useful when the\n   * transformer only needs to consume a portion of the chunks written to the writable side.\n   */\n  terminate(): void {\n    if (!IsTransformStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('terminate');\n    }\n\n    TransformStreamDefaultControllerTerminate(this);\n  }\n}\n\nObject.defineProperties(TransformStreamDefaultController.prototype, {\n  enqueue: { enumerable: true },\n  error: { enumerable: true },\n  terminate: { enumerable: true },\n  desiredSize: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(TransformStreamDefaultController.prototype, Symbol.toStringTag, {\n    value: 'TransformStreamDefaultController',\n    configurable: true\n  });\n}\n\n// Transform Stream Default Controller Abstract Operations\n\nfunction IsTransformStreamDefaultController<O = any>(x: any): x is TransformStreamDefaultController<O> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_controlledTransformStream')) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction SetUpTransformStreamDefaultController<I, O>(stream: TransformStream<I, O>,\n                                                     controller: TransformStreamDefaultController<O>,\n                                                     transformAlgorithm: (chunk: I) => Promise<void>,\n                                                     flushAlgorithm: () => Promise<void>) {\n  assert(IsTransformStream(stream));\n  assert(stream._transformStreamController === undefined);\n\n  controller._controlledTransformStream = stream;\n  stream._transformStreamController = controller;\n\n  controller._transformAlgorithm = transformAlgorithm;\n  controller._flushAlgorithm = flushAlgorithm;\n}\n\nfunction SetUpTransformStreamDefaultControllerFromTransformer<I, O>(stream: TransformStream<I, O>,\n                                                                    transformer: ValidatedTransformer<I, O>) {\n  const controller: TransformStreamDefaultController<O> = Object.create(TransformStreamDefaultController.prototype);\n\n  let transformAlgorithm = (chunk: I): Promise<void> => {\n    try {\n      TransformStreamDefaultControllerEnqueue(controller, chunk as unknown as O);\n      return promiseResolvedWith(undefined);\n    } catch (transformResultE) {\n      return promiseRejectedWith(transformResultE);\n    }\n  };\n\n  let flushAlgorithm: () => Promise<void> = () => promiseResolvedWith(undefined);\n\n  if (transformer.transform !== undefined) {\n    transformAlgorithm = chunk => transformer.transform!(chunk, controller);\n  }\n  if (transformer.flush !== undefined) {\n    flushAlgorithm = () => transformer.flush!(controller);\n  }\n\n  SetUpTransformStreamDefaultController(stream, controller, transformAlgorithm, flushAlgorithm);\n}\n\nfunction TransformStreamDefaultControllerClearAlgorithms(controller: TransformStreamDefaultController<any>) {\n  controller._transformAlgorithm = undefined!;\n  controller._flushAlgorithm = undefined!;\n}\n\nfunction TransformStreamDefaultControllerEnqueue<O>(controller: TransformStreamDefaultController<O>, chunk: O) {\n  const stream = controller._controlledTransformStream;\n  const readableController = stream._readable._readableStreamController as ReadableStreamDefaultController<O>;\n  if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(readableController)) {\n    throw new TypeError('Readable side is not in a state that permits enqueue');\n  }\n\n  // We throttle transform invocations based on the backpressure of the ReadableStream, but we still\n  // accept TransformStreamDefaultControllerEnqueue() calls.\n\n  try {\n    ReadableStreamDefaultControllerEnqueue(readableController, chunk);\n  } catch (e) {\n    // This happens when readableStrategy.size() throws.\n    TransformStreamErrorWritableAndUnblockWrite(stream, e);\n\n    throw stream._readable._storedError;\n  }\n\n  const backpressure = ReadableStreamDefaultControllerHasBackpressure(readableController);\n  if (backpressure !== stream._backpressure) {\n    assert(backpressure);\n    TransformStreamSetBackpressure(stream, true);\n  }\n}\n\nfunction TransformStreamDefaultControllerError(controller: TransformStreamDefaultController<any>, e: any) {\n  TransformStreamError(controller._controlledTransformStream, e);\n}\n\nfunction TransformStreamDefaultControllerPerformTransform<I, O>(controller: TransformStreamDefaultController<O>,\n                                                                chunk: I) {\n  const transformPromise = controller._transformAlgorithm(chunk);\n  return transformPromiseWith(transformPromise, undefined, r => {\n    TransformStreamError(controller._controlledTransformStream, r);\n    throw r;\n  });\n}\n\nfunction TransformStreamDefaultControllerTerminate<O>(controller: TransformStreamDefaultController<O>) {\n  const stream = controller._controlledTransformStream;\n  const readableController = stream._readable._readableStreamController as ReadableStreamDefaultController<O>;\n\n  ReadableStreamDefaultControllerClose(readableController);\n\n  const error = new TypeError('TransformStream terminated');\n  TransformStreamErrorWritableAndUnblockWrite(stream, error);\n}\n\n// TransformStreamDefaultSink Algorithms\n\nfunction TransformStreamDefaultSinkWriteAlgorithm<I, O>(stream: TransformStream<I, O>, chunk: I): Promise<void> {\n  assert(stream._writable._state === 'writable');\n\n  const controller = stream._transformStreamController;\n\n  if (stream._backpressure) {\n    const backpressureChangePromise = stream._backpressureChangePromise;\n    assert(backpressureChangePromise !== undefined);\n    return transformPromiseWith(backpressureChangePromise, () => {\n      const writable = stream._writable;\n      const state = writable._state;\n      if (state === 'erroring') {\n        throw writable._storedError;\n      }\n      assert(state === 'writable');\n      return TransformStreamDefaultControllerPerformTransform<I, O>(controller, chunk);\n    });\n  }\n\n  return TransformStreamDefaultControllerPerformTransform<I, O>(controller, chunk);\n}\n\nfunction TransformStreamDefaultSinkAbortAlgorithm(stream: TransformStream, reason: any): Promise<void> {\n  // abort() is not called synchronously, so it is possible for abort() to be called when the stream is already\n  // errored.\n  TransformStreamError(stream, reason);\n  return promiseResolvedWith(undefined);\n}\n\nfunction TransformStreamDefaultSinkCloseAlgorithm<I, O>(stream: TransformStream<I, O>): Promise<void> {\n  // stream._readable cannot change after construction, so caching it across a call to user code is safe.\n  const readable = stream._readable;\n\n  const controller = stream._transformStreamController;\n  const flushPromise = controller._flushAlgorithm();\n  TransformStreamDefaultControllerClearAlgorithms(controller);\n\n  // Return a promise that is fulfilled with undefined on success.\n  return transformPromiseWith(flushPromise, () => {\n    if (readable._state === 'errored') {\n      throw readable._storedError;\n    }\n    ReadableStreamDefaultControllerClose(readable._readableStreamController as ReadableStreamDefaultController<O>);\n  }, r => {\n    TransformStreamError(stream, r);\n    throw readable._storedError;\n  });\n}\n\n// TransformStreamDefaultSource Algorithms\n\nfunction TransformStreamDefaultSourcePullAlgorithm(stream: TransformStream): Promise<void> {\n  // Invariant. Enforced by the promises returned by start() and pull().\n  assert(stream._backpressure);\n\n  assert(stream._backpressureChangePromise !== undefined);\n\n  TransformStreamSetBackpressure(stream, false);\n\n  // Prevent the next pull() call until there is backpressure.\n  return stream._backpressureChangePromise;\n}\n\n// Helper functions for the TransformStreamDefaultController.\n\nfunction defaultControllerBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `TransformStreamDefaultController.prototype.${name} can only be used on a TransformStreamDefaultController`);\n}\n\n// Helper functions for the TransformStream.\n\nfunction streamBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `TransformStream.prototype.${name} can only be used on a TransformStream`);\n}\n", "import {\n  ByteLengthQueuingStrategy,\n  CountQueuingStrategy,\n  ReadableByteStreamController,\n  ReadableStream,\n  ReadableStreamBYOBReader,\n  ReadableStreamBYOBRequest,\n  ReadableStreamDefaultController,\n  ReadableStreamDefaultReader,\n  TransformStream,\n  TransformStreamDefaultController,\n  WritableStream,\n  WritableStreamDefaultController,\n  WritableStreamDefaultWriter\n} from './ponyfill';\nimport { globals } from './utils';\n\n// Export\nexport * from './ponyfill';\n\nconst exports = {\n  ReadableStream,\n  ReadableStreamDefaultController,\n  ReadableByteStreamController,\n  ReadableStreamBYOBRequest,\n  ReadableStreamDefaultReader,\n  ReadableStreamBYOBReader,\n\n  WritableStream,\n  WritableStreamDefaultController,\n  WritableStreamDefaultWriter,\n\n  ByteLengthQueuingStrategy,\n  CountQueuingStrategy,\n\n  TransformStream,\n  TransformStreamDefaultController\n};\n\n// Add classes to global scope\nif (typeof globals !== 'undefined') {\n  for (const prop in exports) {\n    if (Object.prototype.hasOwnProperty.call(exports, prop)) {\n      Object.defineProperty(globals, prop, {\n        value: exports[prop as (keyof typeof exports)],\n        writable: true,\n        configurable: true\n      });\n    }\n  }\n}\n"], "names": ["SymbolPolyfill", "Symbol", "iterator", "description", "noop", "globals", "self", "window", "global", "typeIsObject", "x", "rethrowAssertionErrorRejection", "originalPromise", "Promise", "originalPromiseThen", "prototype", "then", "originalPromiseResolve", "resolve", "bind", "originalPromiseReject", "reject", "newPromise", "executor", "promiseResolvedWith", "value", "promiseRejectedWith", "reason", "PerformPromiseThen", "promise", "onFulfilled", "onRejected", "call", "uponPromise", "undefined", "uponFulfillment", "uponRejection", "transformPromiseWith", "fulfillmentH<PERSON>ler", "<PERSON><PERSON><PERSON><PERSON>", "setPromiseIsHandledToTrue", "queueMicrotask", "globalQueueMicrotask", "resolvedPromise", "fn", "reflectCall", "F", "V", "args", "TypeError", "Function", "apply", "promiseCall", "this", "_front", "_elements", "_next", "_back", "_cursor", "_size", "Object", "SimpleQueue", "element", "oldBack", "newBack", "QUEUE_MAX_ARRAY_SIZE", "length", "push", "oldFront", "newFront", "old<PERSON>ursor", "newCursor", "elements", "callback", "i", "node", "front", "cursor", "ReadableStreamReaderGenericInitialize", "reader", "stream", "_ownerReadableStream", "_reader", "_state", "defaultReaderClosedPromiseInitialize", "defaultReaderClosedPromiseResolve", "defaultReaderClosedPromiseInitializeAsResolved", "defaultReaderClosedPromiseInitializeAsRejected", "_storedError", "ReadableStreamReaderGenericCancel", "ReadableStreamCancel", "ReadableStreamReaderGenericRelease", "defaultReaderClosedPromiseReject", "defaultReaderClosedPromiseResetToRejected", "readerLockException", "name", "_closedPromise", "_closedPromise_resolve", "_closedPromise_reject", "AbortSteps", "ErrorSteps", "CancelSteps", "PullSteps", "NumberIsFinite", "Number", "isFinite", "MathTrunc", "Math", "trunc", "v", "ceil", "floor", "assertDictionary", "obj", "context", "assertFunction", "assertObject", "isObject", "assertRequiredArgument", "position", "assertRequiredField", "field", "convertUnrestrictedDouble", "censorNegativeZero", "convertUnsignedLongLongWithEnforceRange", "upperBound", "MAX_SAFE_INTEGER", "integerPart", "assertReadableStream", "IsReadableStream", "AcquireReadableStreamDefaultReader", "ReadableStreamDefaultReader", "ReadableStreamAddReadRequest", "readRequest", "_readRequests", "ReadableStreamFulfillReadRequest", "chunk", "done", "shift", "_closeSteps", "_chunkSteps", "ReadableStreamGetNumReadRequests", "ReadableStreamHasDefaultReader", "IsReadableStreamDefaultReader", "AsyncIteratorPrototype", "IsReadableStreamLocked", "defaultReaderBrandCheckException", "resolvePromise", "rejectPromise", "ReadableStreamDefaultReaderRead", "_errorSteps", "e", "hasOwnProperty", "_disturbed", "_readableStreamController", "defineProperties", "cancel", "enumerable", "read", "releaseLock", "closed", "toStringTag", "defineProperty", "configurable", "asyncIterator", "preventCancel", "_preventCancel", "ReadableStreamAsyncIteratorImpl", "nextSteps", "_this", "_nextSteps", "_ongoingPromise", "returnSteps", "_returnSteps", "_isFinished", "result", "ReadableStreamAsyncIteratorPrototype", "next", "IsReadableStreamAsyncIterator", "_asyncIteratorImpl", "streamAsyncIteratorBrandCheckException", "return", "setPrototypeOf", "NumberIsNaN", "isNaN", "IsFiniteNonNegativeNumber", "IsNonNegativeNumber", "Infinity", "DequeueValue", "container", "pair", "_queue", "_queueTotalSize", "size", "EnqueueValueWithSize", "RangeError", "ResetQueue", "CreateArrayFromList", "slice", "ReadableStreamBYOBRequest", "IsReadableStreamBYOBRequest", "byobRequestBrandCheckException", "_view", "bytes<PERSON>ritten", "_associatedReadableByteStreamController", "buffer", "controller", "ReadableByteStreamControllerRespondInternal", "ReadableByteStreamControllerRespond", "view", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "byteLength", "firstDescriptor", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "peek", "byteOffset", "bytesFilled", "ReadableByteStreamControllerRespondWithNewView", "respond", "respondWithNewView", "ReadableByteStreamController", "IsReadableByteStreamController", "byteStreamControllerBrandCheckException", "_byobRequest", "Uint8Array", "byobRequest", "create", "request", "SetUpReadableStreamBYOBRequest", "ReadableByteStreamControllerGetDesiredSize", "_closeRequested", "state", "_controlledReadableByteStream", "ReadableByteStreamControllerError", "ReadableByteStreamControllerClearAlgorithms", "ReadableStreamClose", "ReadableByteStreamControllerClose", "<PERSON><PERSON><PERSON><PERSON>", "ReadableByteStreamControllerEnqueueChunkToQueue", "transferredView", "ReadableStreamHasBYOBReader", "ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue", "ReadableByteStreamControllerCallPullIfNeeded", "ReadableByteStreamControllerEnqueue", "_cancelAlgorithm", "entry", "ReadableByteStreamControllerHandleQueueDrain", "autoAllocateChunkSize", "_autoAllocateChunkSize", "bufferE", "pullIntoDescriptor", "elementSize", "viewConstructor", "readerType", "_started", "ReadableStreamGetNumReadIntoRequests", "ReadableByteStreamControllerShouldCallPull", "_pulling", "_pullAgain", "_pullAlgorithm", "ReadableByteStreamControllerCommitPullIntoDescriptor", "<PERSON><PERSON><PERSON><PERSON>", "ReadableByteStreamControllerConvertPullIntoDescriptor", "readIntoRequest", "_readIntoRequests", "ReadableStreamFulfillReadIntoRequest", "ReadableByteStreamControllerFillPullIntoDescriptorFromQueue", "currentAlignedBytes", "maxBytesToCopy", "min", "maxBytesFilled", "maxAlignedBytes", "totalBytesToCopyRemaining", "ready", "dest", "destOffset", "src", "srcOffset", "n", "queue", "headOfQueue", "bytesToCopy", "destStart", "set", "ReadableByteStreamControllerFillHeadPullIntoDescriptor", "ReadableByteStreamControllerInvalidateBYOBRequest", "ReadableByteStreamControllerShiftPendingPullInto", "ReadableByteStreamControllerRespondInClosedState", "remainderSize", "end", "remainder", "ReadableByteStreamControllerRespondInReadableState", "descriptor", "ReadableByteStreamControllerClearPendingPullIntos", "ReadableStreamError", "_strategyHWM", "SetUpReadableByteStreamControllerFromUnderlyingSource", "underlyingByteSource", "highWaterMark", "startAlgorithm", "pullAlgorithm", "cancelAlgorithm", "start", "pull", "r", "SetUpReadableByteStreamController", "ReadableStreamAddReadIntoRequest", "IsReadableStreamBYOBReader", "close", "enqueue", "error", "desiredSize", "ReadableStreamBYOBReader", "byobReaderBrandCheckException", "constructor", "DataView", "BYTES_PER_ELEMENT", "ctor", "emptyView", "ReadableByteStreamControllerPullInto", "ReadableStreamBYOBReaderRead", "ExtractHighWaterMark", "strategy", "defaultHWM", "ExtractSizeAlgorithm", "convertQueuingStrategy", "init", "convertQueuingStrategySize", "convertUnderlyingSinkAbortCallback", "original", "convertUnderlyingSinkCloseCallback", "convertUnderlyingSinkStartCallback", "convertUnderlyingSinkWriteCallback", "assertWritableStream", "IsWritableStream", "rawUnderlyingSink", "rawStrategy", "underlyingSink", "abort", "type", "write", "convertUnderlyingSink", "InitializeWritableStream", "sizeAlgorithm", "WritableStreamDefaultController", "writeAlgorithm", "closeAlgorithm", "abortAlgorithm", "SetUpWritableStreamDefaultController", "SetUpWritableStreamDefaultControllerFromUnderlyingSink", "WritableStream", "streamBrandCheckException", "IsWritableStreamLocked", "WritableStreamAbort", "WritableStreamCloseQueuedOrInFlight", "WritableStreamClose", "AcquireWritableStreamDefaultWriter", "WritableStreamDefaultWriter", "_writer", "_writableStreamController", "_writeRequests", "_inFlightWriteRequest", "_closeRequest", "_inFlightCloseRequest", "_pendingAbortRequest", "_backpressure", "_promise", "wasAlreadyErroring", "_resolve", "_reject", "_reason", "_wasAlreadyErroring", "WritableStreamStartErroring", "closeRequest", "writer", "defaultWriterReadyPromiseResolve", "closeSentinel", "WritableStreamDefaultControllerAdvanceQueueIfNeeded", "WritableStreamDealWithRejection", "WritableStreamFinishErroring", "WritableStreamDefaultWriterEnsureReadyPromiseRejected", "WritableStreamHasOperationMarkedInFlight", "storedError", "for<PERSON>ach", "writeRequest", "abortRequest", "WritableStreamRejectCloseAndClosedPromiseIfNeeded", "defaultWriterClosedPromiseReject", "WritableStreamUpdateBackpressure", "backpressure", "defaultWriterReadyPromiseInitialize", "defaultWriterReadyPromiseReset", "getWriter", "locked", "_ownerWritableStream", "defaultWriterReadyPromiseInitializeAsResolved", "defaultWriterClosedPromiseInitialize", "defaultWriterReadyPromiseInitializeAsRejected", "defaultWriterClosedPromiseResolve", "defaultWriterClosedPromiseInitializeAsRejected", "IsWritableStreamDefaultWriter", "defaultWriterBrandCheckException", "defaultWriterLockException", "WritableStreamDefaultControllerGetDesiredSize", "WritableStreamDefaultWriterGetDesiredSize", "_readyPromise", "WritableStreamDefaultWriterAbort", "WritableStreamDefaultWriterClose", "WritableStreamDefaultWriterRelease", "WritableStreamDefaultWriterWrite", "WritableStreamDefaultWriterEnsureClosedPromiseRejected", "_closedPromiseState", "defaultWriterClosedPromiseResetToRejected", "_readyPromiseState", "defaultWriterReadyPromiseReject", "defaultWriterReadyPromiseResetToRejected", "releasedError", "chunkSize", "_strategySizeAlgorithm", "chunkSizeE", "WritableStreamDefaultControllerErrorIfNeeded", "WritableStreamDefaultControllerGetChunkSize", "WritableStreamAddWriteRequest", "enqueueE", "_controlledWritableStream", "WritableStreamDefaultControllerGetBackpressure", "WritableStreamDefaultControllerWrite", "IsWritableStreamDefaultController", "WritableStreamDefaultControllerError", "_abortAlgorithm", "WritableStreamDefaultControllerClearAlgorithms", "_writeAlgorithm", "_closeAlgorithm", "WritableStreamMarkCloseRequestInFlight", "sinkClosePromise", "WritableStreamFinishInFlightClose", "WritableStreamFinishInFlightCloseWithError", "WritableStreamDefaultControllerProcessClose", "WritableStreamMarkFirstWriteRequestInFlight", "WritableStreamFinishInFlightWrite", "WritableStreamFinishInFlightWriteWithError", "WritableStreamDefaultControllerProcessWrite", "_readyPromise_resolve", "_readyPromise_reject", "NativeDOMException", "DOMException", "isDOMExceptionConstructor", "message", "Error", "captureStackTrace", "writable", "ReadableStreamPipeTo", "source", "preventClose", "preventAbort", "signal", "shuttingDown", "currentWrite", "action", "actions", "shutdownWithAction", "all", "map", "aborted", "addEventListener", "isOrBecomesErrored", "shutdown", "WritableStreamDefaultWriterCloseWithErrorPropagation", "destClosed_1", "waitForWritesToFinish", "oldCurrentWrite", "originalIsError", "originalError", "doTheRest", "finalize", "newError", "isError", "removeEventListener", "resolveLoop", "rejectLoop", "resolveRead", "rejectRead", "ReadableStreamDefaultController", "IsReadableStreamDefaultController", "defaultControllerBrandCheckException", "ReadableStreamDefaultControllerGetDesiredSize", "ReadableStreamDefaultControllerCanCloseOrEnqueue", "ReadableStreamDefaultControllerClose", "ReadableStreamDefaultControllerEnqueue", "ReadableStreamDefaultControllerError", "ReadableStreamDefaultControllerClearAlgorithms", "_controlledReadableStream", "ReadableStreamDefaultControllerCallPullIfNeeded", "ReadableStreamDefaultControllerShouldCallPull", "SetUpReadableStreamDefaultController", "convertUnderlyingSourceCancelCallback", "convertUnderlyingSourcePullCallback", "convertUnderlyingSourceStartCallback", "convertReadableStreamType", "convertReadableStreamReaderMode", "mode", "convertPipeOptions", "options", "isAbortSignal", "assertAbortSignal", "Boolean", "rawUnderlyingSource", "underlyingSource", "convertUnderlyingDefaultOrByteSource", "InitializeReadableStream", "SetUpReadableStreamDefaultControllerFromUnderlyingSource", "ReadableStream", "rawOptions", "convertReaderOptions", "rawTransform", "transform", "readable", "convertReadableWritablePair", "destination", "branches", "cloneForBranch2", "reason1", "reason2", "branch1", "branch2", "resolveCancelPromise", "reading", "canceled1", "canceled2", "cancelPromise", "value1", "value2", "CreateReadableStream", "compositeReason", "cancelResult", "ReadableStreamTee", "impl", "convertIteratorOptions", "convertQueuingStrategyInit", "<PERSON><PERSON><PERSON><PERSON>", "pipeThrough", "pipeTo", "tee", "values", "byteLengthSizeFunction", "_byteLengthQueuingStrategyHighWaterMark", "ByteLengthQueuingStrategy", "IsByteLengthQueuingStrategy", "byteLengthBrandCheckException", "countSizeFunction", "_countQueuingStrategyHighWaterMark", "CountQueuingStrategy", "IsCountQueuingStrategy", "countBrandCheckException", "convertTransformerFlushCallback", "convertTransformerStartCallback", "convertTransformerTransformCallback", "rawTransformer", "rawWritableStrategy", "rawReadableStrategy", "writableStrategy", "readableStrategy", "transformer", "flush", "readableType", "writableType", "convertTransformer", "startPromise_resolve", "readableHighWaterMark", "readableSizeAlgorithm", "writableHighWaterMark", "writableSizeAlgorithm", "startPromise", "_writable", "CreateWritableStream", "_transformStreamController", "_backpressureChangePromise", "TransformStreamDefaultControllerPerformTransform", "TransformStreamDefaultSinkWriteAlgorithm", "_readable", "flushPromise", "_flushAlgorithm", "TransformStreamDefaultControllerClearAlgorithms", "TransformStreamError", "TransformStreamDefaultSinkCloseAlgorithm", "TransformStreamDefaultSinkAbortAlgorithm", "TransformStreamSetBackpressure", "TransformStreamDefaultSourcePullAlgorithm", "TransformStreamErrorWritableAndUnblockWrite", "_backpressureChangePromise_resolve", "InitializeTransformStream", "TransformStreamDefaultController", "transformAlgorithm", "TransformStreamDefaultControllerEnqueue", "transformResultE", "flushAlgorithm", "_controlledTransformStream", "_transformAlgorithm", "SetUpTransformStreamDefaultController", "SetUpTransformStreamDefaultControllerFromTransformer", "TransformStream", "IsTransformStream", "IsTransformStreamDefaultController", "TransformStreamDefaultControllerTerminate", "readableController", "ReadableStreamDefaultControllerHasBackpressure", "terminate", "exports", "prop"], "mappings": "gNAEA,IAAMA,EACc,mBAAXC,QAAoD,iBAApBA,OAAOC,SAC5CD,OACA,SAAAE,GAAe,MAAA,UAAUA,gBCHbC,KAeT,IAAMC,EAVS,oBAATC,KACFA,KACoB,oBAAXC,OACTA,OACoB,oBAAXC,OACTA,YADF,WCROC,EAAaC,GAC3B,MAAqB,iBAANA,GAAwB,OAANA,GAA4B,mBAANA,EAGlD,IAAMC,EAUPP,ECbAQ,EAAkBC,QAClBC,EAAsBD,QAAQE,UAAUC,KACxCC,EAAyBJ,QAAQK,QAAQC,KAAKP,GAC9CQ,EAAwBP,QAAQQ,OAAOF,KAAKP,YAElCU,EAAcC,GAI5B,OAAO,IAAIX,EAAgBW,YAGbC,EAAuBC,GACrC,OAAOR,EAAuBQ,YAGhBC,EAA+BC,GAC7C,OAAOP,EAAsBO,YAGfC,EACdC,EACAC,EACAC,GAGA,OAAOjB,EAAoBkB,KAAKH,EAASC,EAAaC,YAGxCE,EACdJ,EACAC,EACAC,GACAH,EACEA,EAAmBC,EAASC,EAAaC,QACzCG,EACAvB,YAIYwB,EAAmBN,EAAqBC,GACtDG,EAAYJ,EAASC,YAGPM,EAAcP,EAA2BE,GACvDE,EAAYJ,OAASK,EAAWH,YAGlBM,EACdR,EACAS,EACAC,GACA,OAAOX,EAAmBC,EAASS,EAAoBC,YAGzCC,EAA0BX,GACxCD,EAAmBC,OAASK,EAAWvB,GAGlC,IAAM8B,EAA2C,WACtD,IAAMC,EAAuBrC,GAAWA,EAAQoC,eAChD,GAAoC,mBAAzBC,EACT,OAAOA,EAGT,IAAMC,EAAkBnB,OAAoBU,GAC5C,OAAO,SAACU,GAAmB,OAAAhB,EAAmBe,EAAiBC,IAPT,YAUxCC,EAAmCC,EAA+BC,EAAMC,GACtF,GAAiB,mBAANF,EACT,MAAM,IAAIG,UAAU,8BAEtB,OAAOC,SAASnC,UAAUoC,MAAMnB,KAAKc,EAAGC,EAAGC,YAG7BI,EAAmCN,EACAC,EACAC,GAIjD,IACE,OAAOxB,EAAoBqB,EAAYC,EAAGC,EAAGC,IAC7C,MAAOvB,GACP,OAAOC,EAAoBD,ICpF/B,iBAmBE,aAHQ4B,aAAU,EACVA,WAAQ,EAIdA,KAAKC,OAAS,CACZC,UAAW,GACXC,WAAOtB,GAETmB,KAAKI,MAAQJ,KAAKC,OAIlBD,KAAKK,QAAU,EAEfL,KAAKM,MAAQ,EAsGjB,OAnGEC,sBAAIC,0BAAJ,WACE,OAAOR,KAAKM,uCAOdE,iBAAA,SAAKC,GACH,IAAMC,EAAUV,KAAKI,MACjBO,EAAUD,EAEmBE,QAA7BF,EAAQR,UAAUW,SACpBF,EAAU,CACRT,UAAW,GACXC,WAAOtB,IAMX6B,EAAQR,UAAUY,KAAKL,GACnBE,IAAYD,IACdV,KAAKI,MAAQO,EACbD,EAAQP,MAAQQ,KAEhBX,KAAKM,OAKTE,kBAAA,WAGE,IAAMO,EAAWf,KAAKC,OAClBe,EAAWD,EACTE,EAAYjB,KAAKK,QACnBa,EAAYD,EAAY,EAEtBE,EAAWJ,EAASb,UACpBO,EAAUU,EAASF,GAmBzB,OA7FyB,QA4ErBC,IAGFF,EAAWD,EAASZ,MACpBe,EAAY,KAIZlB,KAAKM,MACPN,KAAKK,QAAUa,EACXH,IAAaC,IACfhB,KAAKC,OAASe,GAIhBG,EAASF,QAAapC,EAEf4B,GAWTD,oBAAA,SAAQY,GAIN,IAHA,IAAIC,EAAIrB,KAAKK,QACTiB,EAAOtB,KAAKC,OACZkB,EAAWG,EAAKpB,YACbmB,IAAMF,EAASN,aAAyBhC,IAAfyC,EAAKnB,OAC/BkB,IAAMF,EAASN,SAKjBQ,EAAI,EACoB,KAFxBF,GADAG,EAAOA,EAAKnB,OACID,WAEHW,UAIfO,EAASD,EAASE,MAChBA,GAMNb,iBAAA,WAGE,IAAMe,EAAQvB,KAAKC,OACbuB,EAASxB,KAAKK,QACpB,OAAOkB,EAAMrB,UAAUsB,kBCpIXC,EAAyCC,EAAiCC,GACxFD,EAAOE,qBAAuBD,EAC9BA,EAAOE,QAAUH,EAEK,aAAlBC,EAAOG,OACTC,EAAqCL,GACV,WAAlBC,EAAOG,gBAwD2CJ,GAC7DK,EAAqCL,GACrCM,EAAkCN,GAzDhCO,CAA+CP,GAI/CQ,EAA+CR,EAAQC,EAAOQ,uBAOlDC,EAAkCV,EAAmCpD,GAGnF,OAAO+D,GAFQX,EAAOE,qBAEctD,YAGtBgE,EAAmCZ,GAIN,aAAvCA,EAAOE,qBAAqBE,OAC9BS,EACEb,EACA,IAAI9B,UAAU,8FA+CsC8B,EAAmCpD,GAI3F4D,EAA+CR,EAAQpD,GAjDrDkE,CACEd,EACA,IAAI9B,UAAU,qFAGlB8B,EAAOE,qBAAqBC,aAAUhD,EACtC6C,EAAOE,0BAAuB/C,WAKhB4D,EAAoBC,GAClC,OAAO,IAAI9C,UAAU,UAAY8C,EAAO,8CAK1BX,EAAqCL,GACnDA,EAAOiB,eAAiB1E,GAAW,SAACJ,EAASG,GAC3C0D,EAAOkB,uBAAyB/E,EAChC6D,EAAOmB,sBAAwB7E,cAInBkE,EAA+CR,EAAmCpD,GAChGyD,EAAqCL,GACrCa,EAAiCb,EAAQpD,YAQ3BiE,EAAiCb,EAAmCpD,QAC7CO,IAAjC6C,EAAOmB,wBAIX1D,EAA0BuC,EAAOiB,gBACjCjB,EAAOmB,sBAAsBvE,GAC7BoD,EAAOkB,4BAAyB/D,EAChC6C,EAAOmB,2BAAwBhE,YAUjBmD,EAAkCN,QACV7C,IAAlC6C,EAAOkB,yBAIXlB,EAAOkB,4BAAuB/D,GAC9B6C,EAAOkB,4BAAyB/D,EAChC6C,EAAOmB,2BAAwBhE,GChG1B,IAAMiE,EAAalG,EAAO,kBACpBmG,EAAanG,EAAO,kBACpBoG,EAAcpG,EAAO,mBACrBqG,EAAYrG,EAAO,iBCA1BsG,EAAyCC,OAAOC,UAAY,SAAU/F,GAC1E,MAAoB,iBAANA,GAAkB+F,SAAS/F,ICDrCgG,EAA+BC,KAAKC,OAAS,SAAUC,GAC3D,OAAOA,EAAI,EAAIF,KAAKG,KAAKD,GAAKF,KAAKI,MAAMF,aCI3BG,EAAiBC,EACAC,GAC/B,QAAYhF,IAAR+E,IALgB,iBADOvG,EAMYuG,IALM,mBAANvG,GAMrC,MAAM,IAAIuC,UAAaiE,4BAPExG,WAcbyG,EAAezG,EAAYwG,GACzC,GAAiB,mBAANxG,EACT,MAAM,IAAIuC,UAAaiE,kCASXE,EAAa1G,EACAwG,GAC3B,aANuBxG,GACvB,MAAqB,iBAANA,GAAwB,OAANA,GAA4B,mBAANA,EAKlD2G,CAAS3G,GACZ,MAAM,IAAIuC,UAAaiE,iCAIXI,EAAsC5G,EACA6G,EACAL,GACpD,QAAUhF,IAANxB,EACF,MAAM,IAAIuC,UAAU,aAAasE,sBAA4BL,iBAIjDM,EAAmC9G,EACA+G,EACAP,GACjD,QAAUhF,IAANxB,EACF,MAAM,IAAIuC,UAAawE,sBAAyBP,iBAKpCQ,EAA0BjG,GACxC,OAAO+E,OAAO/E,GAGhB,SAASkG,EAAmBjH,GAC1B,OAAa,IAANA,EAAU,EAAIA,WAQPkH,EAAwCnG,EAAgByF,GACtE,IACMW,EAAarB,OAAOsB,iBAEtBpH,EAAI8F,OAAO/E,GAGf,GAFAf,EAAIiH,EAAmBjH,IAElB6F,EAAe7F,GAClB,MAAM,IAAIuC,UAAaiE,6BAKzB,IAFAxG,EAhBF,SAAqBA,GACnB,OAAOiH,EAAmBjB,EAAUhG,IAehCqH,CAAYrH,IAVG,GAYGA,EAAImH,EACxB,MAAM,IAAI5E,UAAaiE,EAAAA,0CAA6DW,iBAGtF,OAAKtB,EAAe7F,IAAY,IAANA,EASnBA,EARE,WClFKsH,EAAqBtH,EAAYwG,GAC/C,IAAKe,GAAiBvH,GACpB,MAAM,IAAIuC,UAAaiE,wCC0BXgB,EAAsClD,GACpD,OAAO,IAAImD,EAA4BnD,YAKzBoD,EAAgCpD,EACAqD,GAI7CrD,EAAOE,QAA4CoD,cAAcnE,KAAKkE,YAGzDE,EAAoCvD,EAA2BwD,EAAsBC,GACnG,IAIMJ,EAJSrD,EAAOE,QAIKoD,cAAcI,QACrCD,EACFJ,EAAYM,cAEZN,EAAYO,YAAYJ,YAIZK,EAAoC7D,GAClD,OAAQA,EAAOE,QAA2CoD,cAAcpE,gBAG1D4E,EAA+B9D,GAC7C,IAAMD,EAASC,EAAOE,QAEtB,YAAehD,IAAX6C,KAICgE,GAA8BhE,SClE1BiE,eDoGT,qCAAYhE,GAIV,GAHAsC,EAAuBtC,EAAQ,EAAG,+BAClCgD,EAAqBhD,EAAQ,mBAEzBiE,GAAuBjE,GACzB,MAAM,IAAI/B,UAAU,+EAGtB6B,EAAsCzB,KAAM2B,GAE5C3B,KAAKiF,cAAgB,IAAIzE,EAmF7B,OA5EED,sBAAIuE,oDAAJ,WACE,OAAKY,GAA8B1F,MAI5BA,KAAK2C,eAHHtE,EAAoBwH,GAAiC,4CAShEf,6CAAA,SAAOxG,GACL,oBADKA,UACAoH,GAA8B1F,WAIDnB,IAA9BmB,KAAK4B,qBACAvD,EAAoBoE,EAAoB,WAG1CL,EAAkCpC,KAAM1B,GAPtCD,EAAoBwH,GAAiC,YAehEf,2CAAA,WACE,IAAKY,GAA8B1F,MACjC,OAAO3B,EAAoBwH,GAAiC,SAG9D,QAAkChH,IAA9BmB,KAAK4B,qBACP,OAAOvD,EAAoBoE,EAAoB,cAGjD,IAAIqD,EACAC,EACEvH,EAAUP,GAA+C,SAACJ,EAASG,GACvE8H,EAAiBjI,EACjBkI,EAAgB/H,KAQlB,OADAgI,GAAgChG,KALI,CAClCuF,YAAa,SAAAJ,GAAS,OAAAW,EAAe,CAAE1H,MAAO+G,EAAOC,MAAM,KAC3DE,YAAa,WAAM,OAAAQ,EAAe,CAAE1H,WAAOS,EAAWuG,MAAM,KAC5Da,YAAa,SAAAC,GAAK,OAAAH,EAAcG,MAG3B1H,GAYTsG,kDAAA,WACE,IAAKY,GAA8B1F,MACjC,MAAM6F,GAAiC,eAGzC,QAAkChH,IAA9BmB,KAAK4B,qBAAT,CAIA,GAAI5B,KAAKiF,cAAcpE,OAAS,EAC9B,MAAM,IAAIjB,UAAU,uFAGtB0C,EAAmCtC,gDAmBvB0F,GAAuCrI,GACrD,QAAKD,EAAaC,MAIbkD,OAAO7C,UAAUyI,eAAexH,KAAKtB,EAAG,0BAO/B2I,GAAmCtE,EACAsD,GACjD,IAAMrD,EAASD,EAAOE,qBAItBD,EAAOyE,YAAa,EAEE,WAAlBzE,EAAOG,OACTkD,EAAYM,cACe,YAAlB3D,EAAOG,OAChBkD,EAAYiB,YAAYtE,EAAOQ,cAG/BR,EAAO0E,0BAA0BpD,GAAW+B,GAMhD,SAASa,GAAiCnD,GACxC,OAAO,IAAI9C,UACT,yCAAyC8C,wDAjD7CnC,OAAO+F,iBAAiBxB,EAA4BpH,UAAW,CAC7D6I,OAAQ,CAAEC,YAAY,GACtBC,KAAM,CAAED,YAAY,GACpBE,YAAa,CAAEF,YAAY,GAC3BG,OAAQ,CAAEH,YAAY,KAEU,iBAAvB5J,EAAOgK,aAChBrG,OAAOsG,eAAe/B,EAA4BpH,UAAWd,EAAOgK,YAAa,CAC/ExI,MAAO,8BACP0I,cAAc,IC1MkB,iBAAzBlK,EAAOmK,uBAMbnK,EAAOmK,eAAR,WACE,OAAO/G,MAJX2F,IAOApF,OAAOsG,eAAelB,EAAwB/I,EAAOmK,cAAe,CAAEP,YAAY,KCuBpF,kBAME,WAAY9E,EAAwCsF,GAH5ChH,0BAA2EnB,EAC3EmB,kBAAc,EAGpBA,KAAK6B,QAAUH,EACf1B,KAAKiH,eAAiBD,EAgF1B,OA7EEE,iBAAA,WAAA,WACQC,EAAY,WAAM,OAAAC,EAAKC,cAI7B,OAHArH,KAAKsH,gBAAkBtH,KAAKsH,gBAC1BtI,EAAqBgB,KAAKsH,gBAAiBH,EAAWA,GACtDA,IACKnH,KAAKsH,iBAGdJ,mBAAA,SAAO9I,GAAP,WACQmJ,EAAc,WAAM,OAAAH,EAAKI,aAAapJ,IAC5C,OAAO4B,KAAKsH,gBACVtI,EAAqBgB,KAAKsH,gBAAiBC,EAAaA,GACxDA,KAGIL,uBAAR,WAAA,WACE,GAAIlH,KAAKyH,YACP,OAAOjK,QAAQK,QAAQ,CAAEO,WAAOS,EAAWuG,MAAM,IAGnD,IAKIU,EACAC,EANErE,EAAS1B,KAAK6B,QACpB,QAAoChD,IAAhC6C,EAAOE,qBACT,OAAOvD,EAAoBoE,EAAoB,YAKjD,IAAMjE,EAAUP,GAA+C,SAACJ,EAASG,GACvE8H,EAAiBjI,EACjBkI,EAAgB/H,KAuBlB,OADAgI,GAAgCtE,EApBI,CAClC6D,YAAa,SAAAJ,GACXiC,EAAKE,qBAAkBzI,EAGvBO,GAAe,WAAM,OAAA0G,EAAe,CAAE1H,MAAO+G,EAAOC,MAAM,QAE5DE,YAAa,WACX8B,EAAKE,qBAAkBzI,EACvBuI,EAAKK,aAAc,EACnBnF,EAAmCZ,GACnCoE,EAAe,CAAE1H,WAAOS,EAAWuG,MAAM,KAE3Ca,YAAa,SAAA3H,GACX8I,EAAKE,qBAAkBzI,EACvBuI,EAAKK,aAAc,EACnBnF,EAAmCZ,GACnCqE,EAAczH,MAIXE,GAGD0I,yBAAR,SAAqB9I,GACnB,GAAI4B,KAAKyH,YACP,OAAOjK,QAAQK,QAAQ,CAAEO,QAAOgH,MAAM,IAExCpF,KAAKyH,aAAc,EAEnB,IAAM/F,EAAS1B,KAAK6B,QACpB,QAAoChD,IAAhC6C,EAAOE,qBACT,OAAOvD,EAAoBoE,EAAoB,qBAKjD,IAAKzC,KAAKiH,eAAgB,CACxB,IAAMS,EAAStF,EAAkCV,EAAQtD,GAEzD,OADAkE,EAAmCZ,GAC5B1C,EAAqB0I,GAAQ,WAAM,OAAGtJ,QAAOgH,MAAM,MAI5D,OADA9C,EAAmCZ,GAC5BvD,EAAoB,CAAEC,QAAOgH,MAAM,UAaxCuC,GAAiF,CACrFC,KAAA,WACE,OAAKC,GAA8B7H,MAG5BA,KAAK8H,mBAAmBF,OAFtBvJ,EAAoB0J,GAAuC,UAKtEC,OAAA,SAAuD5J,GACrD,OAAKyJ,GAA8B7H,MAG5BA,KAAK8H,mBAAmBE,OAAO5J,GAF7BC,EAAoB0J,GAAuC,aAoBxE,SAASF,GAAuCxK,GAC9C,QAAKD,EAAaC,MAIbkD,OAAO7C,UAAUyI,eAAexH,KAAKtB,EAAG,sBAS/C,SAAS0K,GAAuCrF,GAC9C,OAAO,IAAI9C,UAAU,+BAA+B8C,4DA9BvB7D,IAA3B8G,GACFpF,OAAO0H,eAAeN,GAAsChC,GCrJ9D,IAAMuC,GAAmC/E,OAAOgF,OAAS,SAAU9K,GAEjE,OAAOA,GAAMA,YCHC+K,GAA0B5E,GACxC,iBAWkCA,GAClC,GAAiB,iBAANA,EACT,OAAO,EAGT,GAAI0E,GAAY1E,GACd,OAAO,EAGT,GAAIA,EAAI,EACN,OAAO,EAGT,OAAO,EAxBF6E,CAAoB7E,IAIrBA,IAAM8E,EAAAA,WCOIC,GAAgBC,GAI9B,IAAMC,EAAOD,EAAUE,OAAOrD,QAM9B,OALAmD,EAAUG,iBAAmBF,EAAKG,KAC9BJ,EAAUG,gBAAkB,IAC9BH,EAAUG,gBAAkB,GAGvBF,EAAKrK,eAGEyK,GAAwBL,EAAyCpK,EAAUwK,GAIzF,IAAKR,GADLQ,EAAOzF,OAAOyF,IAEZ,MAAM,IAAIE,WAAW,wDAGvBN,EAAUE,OAAO5H,KAAK,CAAE1C,QAAOwK,SAC/BJ,EAAUG,iBAAmBC,WAWfG,GAAcP,GAG5BA,EAAUE,OAAS,IAAIlI,EACvBgI,EAAUG,gBAAkB,WCnDdK,GAAqC7H,GAGnD,OAAOA,EAAS8H,0BCwChB,qCACE,MAAM,IAAIrJ,UAAU,uBAyExB,OAnEEW,sBAAI2I,gDAAJ,WACE,IAAKC,GAA4BnJ,MAC/B,MAAMoJ,GAA+B,QAGvC,OAAOpJ,KAAKqJ,uCAWdH,4CAAA,SAAQI,GACN,IAAKH,GAA4BnJ,MAC/B,MAAMoJ,GAA+B,WAKvC,GAHAnF,EAAuBqF,EAAc,EAAG,WACxCA,EAAe/E,EAAwC+E,EAAc,wBAEhBzK,IAAjDmB,KAAKuJ,wCACP,MAAM,IAAI3J,UAAU,0CAGDI,KAAKqJ,MAAOG,OAiwBrC,SAA6CC,EAA0CH,GAErF,IAAKlB,GADLkB,EAAenG,OAAOmG,IAEpB,MAAM,IAAIR,WAAW,iCAKvBY,GAA4CD,EAAYH,GAlwBtDK,CAAoC3J,KAAKuJ,wCAAyCD,IAWpFJ,uDAAA,SAAmBU,GACjB,IAAKT,GAA4BnJ,MAC/B,MAAMoJ,GAA+B,sBAIvC,GAFAnF,EAAuB2F,EAAM,EAAG,uBAE3BC,YAAYC,OAAOF,GACtB,MAAM,IAAIhK,UAAU,gDAEtB,GAAwB,IAApBgK,EAAKG,WACP,MAAM,IAAInK,UAAU,uCAEtB,GAA+B,IAA3BgK,EAAKJ,OAAOO,WACd,MAAM,IAAInK,UAAU,gDAGtB,QAAqDf,IAAjDmB,KAAKuJ,wCACP,MAAM,IAAI3J,UAAU,2CAyuB1B,SAAwD6J,EACAG,GAGtD,IAAMI,EAAkBP,EAAWQ,kBAAkBC,OAErD,GAAIF,EAAgBG,WAAaH,EAAgBI,cAAgBR,EAAKO,WACpE,MAAM,IAAIrB,WAAW,2DAEvB,GAAIkB,EAAgBD,aAAeH,EAAKG,WACtC,MAAM,IAAIjB,WAAW,8DAGvBkB,EAAgBR,OAASI,EAAKJ,OAE9BE,GAA4CD,EAAYG,EAAKG,YArvB3DM,CAA+CrK,KAAKuJ,wCAAyCK,iCAIjGrJ,OAAO+F,iBAAiB4C,GAA0BxL,UAAW,CAC3D4M,QAAS,CAAE9D,YAAY,GACvB+D,mBAAoB,CAAE/D,YAAY,GAClCoD,KAAM,CAAEpD,YAAY,KAEY,iBAAvB5J,EAAOgK,aAChBrG,OAAOsG,eAAeqC,GAA0BxL,UAAWd,EAAOgK,YAAa,CAC7ExI,MAAO,4BACP0I,cAAc,sBA0EhB,wCACE,MAAM,IAAIlH,UAAU,uBAiKxB,OA3JEW,sBAAIiK,0DAAJ,WACE,IAAKC,GAA+BzK,MAClC,MAAM0K,GAAwC,eAGhD,GAA0B,OAAtB1K,KAAK2K,cAAyB3K,KAAKiK,kBAAkBpJ,OAAS,EAAG,CACnE,IAAMmJ,EAAkBhK,KAAKiK,kBAAkBC,OACzCN,EAAO,IAAIgB,WAAWZ,EAAgBR,OAChBQ,EAAgBG,WAAaH,EAAgBI,YAC7CJ,EAAgBD,WAAaC,EAAgBI,aAEnES,EAAyCtK,OAAOuK,OAAO5B,GAA0BxL,YAmuB7F,SAAwCqN,EACAtB,EACAG,GAKtCmB,EAAQxB,wCAA0CE,EAClDsB,EAAQ1B,MAAQO,EA1uBZoB,CAA+BH,EAAa7K,KAAM4J,GAClD5J,KAAK2K,aAAeE,EAGtB,OAAO7K,KAAK2K,8CAOdpK,sBAAIiK,0DAAJ,WACE,IAAKC,GAA+BzK,MAClC,MAAM0K,GAAwC,eAGhD,OAAOO,GAA2CjL,uCAOpDwK,6CAAA,WACE,IAAKC,GAA+BzK,MAClC,MAAM0K,GAAwC,SAGhD,GAAI1K,KAAKkL,gBACP,MAAM,IAAItL,UAAU,8DAGtB,IAAMuL,EAAQnL,KAAKoL,8BAA8BtJ,OACjD,GAAc,aAAVqJ,EACF,MAAM,IAAIvL,UAAU,kBAAkBuL,gEAyf5C,SAA2C1B,GACzC,IAAM9H,EAAS8H,EAAW2B,8BAE1B,GAAI3B,EAAWyB,iBAAqC,aAAlBvJ,EAAOG,OACvC,OAGF,GAAI2H,EAAWd,gBAAkB,EAG/B,YAFAc,EAAWyB,iBAAkB,GAK/B,GAAIzB,EAAWQ,kBAAkBpJ,OAAS,EAAG,CAE3C,GAD6B4I,EAAWQ,kBAAkBC,OACjCE,YAAc,EAAG,CACxC,IAAMlE,EAAI,IAAItG,UAAU,2DAGxB,MAFAyL,GAAkC5B,EAAYvD,GAExCA,GAIVoF,GAA4C7B,GAC5C8B,GAAoB5J,GA9gBlB6J,CAAkCxL,OAQpCwK,+CAAA,SAAQrF,GACN,IAAKsF,GAA+BzK,MAClC,MAAM0K,GAAwC,WAIhD,GADAzG,EAAuBkB,EAAO,EAAG,YAC5B0E,YAAYC,OAAO3E,GACtB,MAAM,IAAIvF,UAAU,sCAEtB,GAAyB,IAArBuF,EAAM4E,WACR,MAAM,IAAInK,UAAU,uCAEtB,GAAgC,IAA5BuF,EAAMqE,OAAOO,WACf,MAAM,IAAInK,UAAU,gDAGtB,GAAII,KAAKkL,gBACP,MAAM,IAAItL,UAAU,gCAGtB,IAAMuL,EAAQnL,KAAKoL,8BAA8BtJ,OACjD,GAAc,aAAVqJ,EACF,MAAM,IAAIvL,UAAU,kBAAkBuL,qEAmf5C,SAA6C1B,EAA0CtE,GACrF,IAAMxD,EAAS8H,EAAW2B,8BAE1B,GAAI3B,EAAWyB,iBAAqC,aAAlBvJ,EAAOG,OACvC,OAGF,IAAM0H,EAASrE,EAAMqE,OACfW,EAAahF,EAAMgF,WACnBJ,EAAa5E,EAAM4E,WACnB0B,EAAwCjC,EAE9C,GAAI/D,EAA+B9D,GACjC,GAAiD,IAA7C6D,EAAiC7D,GACnC+J,GAAgDjC,EAAYgC,EAAmBtB,EAAYJ,OACtF,CAGL,IAAM4B,EAAkB,IAAIf,WAAWa,EAAmBtB,EAAYJ,GACtE7E,EAAiCvD,EAAQgK,GAAiB,QAEnDC,GAA4BjK,IAErC+J,GAAgDjC,EAAYgC,EAAmBtB,EAAYJ,GAC3F8B,GAAiEpC,IAGjEiC,GAAgDjC,EAAYgC,EAAmBtB,EAAYJ,GAG7F+B,GAA6CrC,GA9gB3CsC,CAAoC/L,KAAMmF,IAM5CqF,6CAAA,SAAMtE,GACJ,gBADIA,WACCuE,GAA+BzK,MAClC,MAAM0K,GAAwC,SAGhDW,GAAkCrL,KAAMkG,IAI1CsE,uCAACxH,GAAD,SAAc1E,GACR0B,KAAKiK,kBAAkBpJ,OAAS,IACVb,KAAKiK,kBAAkBC,OAC/BE,YAAc,GAGhCrB,GAAW/I,MAEX,IAAM0H,EAAS1H,KAAKgM,iBAAiB1N,GAErC,OADAgN,GAA4CtL,MACrC0H,GAIT8C,uCAACvH,GAAD,SAAY+B,GACV,IAAMrD,EAAS3B,KAAKoL,8BAGpB,GAAIpL,KAAK2I,gBAAkB,EAA3B,CAGE,IAAMsD,EAAQjM,KAAK0I,OAAOrD,QAC1BrF,KAAK2I,iBAAmBsD,EAAMlC,WAE9BmC,GAA6ClM,MAE7C,IAAM4J,EAAO,IAAIgB,WAAWqB,EAAMzC,OAAQyC,EAAM9B,WAAY8B,EAAMlC,YAElE/E,EAAYO,YAAYqE,OAV1B,CAcA,IAAMuC,EAAwBnM,KAAKoM,uBACnC,QAA8BvN,IAA1BsN,EAAqC,CACvC,IAAI3C,SACJ,IACEA,EAAS,IAAIK,YAAYsC,GACzB,MAAOE,GAEP,YADArH,EAAYiB,YAAYoG,GAI1B,IAAMC,EAAgD,CACpD9C,SACAW,WAAY,EACZJ,WAAYoC,EACZ/B,YAAa,EACbmC,YAAa,EACbC,gBAAiB5B,WACjB6B,WAAY,WAGdzM,KAAKiK,kBAAkBnJ,KAAKwL,GAG9BvH,EAA6BpD,EAAQqD,GACrC8G,GAA6C9L,iDAoBjCyK,GAA+BpN,GAC7C,QAAKD,EAAaC,MAIbkD,OAAO7C,UAAUyI,eAAexH,KAAKtB,EAAG,iCAO/C,SAAS8L,GAA4B9L,GACnC,QAAKD,EAAaC,MAIbkD,OAAO7C,UAAUyI,eAAexH,KAAKtB,EAAG,2CAO/C,SAASyO,GAA6CrC,IA2TtD,SAAoDA,GAClD,IAAM9H,EAAS8H,EAAW2B,8BAE1B,GAAsB,aAAlBzJ,EAAOG,OACT,OAAO,EAGT,GAAI2H,EAAWyB,gBACb,OAAO,EAGT,IAAKzB,EAAWiD,SACd,OAAO,EAGT,GAAIjH,EAA+B9D,IAAW6D,EAAiC7D,GAAU,EACvF,OAAO,EAGT,GAAIiK,GAA4BjK,IAAWgL,GAAqChL,GAAU,EACxF,OAAO,EAKT,GAFoBsJ,GAA2CxB,GAE5C,EACjB,OAAO,EAGT,OAAO,GAvVYmD,CAA2CnD,KAK1DA,EAAWoD,SACbpD,EAAWqD,YAAa,GAM1BrD,EAAWoD,UAAW,EAItBjO,EADoB6K,EAAWsD,kBAG7B,WACEtD,EAAWoD,UAAW,EAElBpD,EAAWqD,aACbrD,EAAWqD,YAAa,EACxBhB,GAA6CrC,OAGjD,SAAAvD,GACEmF,GAAkC5B,EAAYvD,QAUpD,SAAS8G,GAAgFrL,EACA2K,GAGvF,IAAIlH,GAAO,EACW,WAAlBzD,EAAOG,SAETsD,GAAO,GAGT,IAAM6H,EAAaC,GAAyDZ,GACtC,YAAlCA,EAAmBG,WACrBvH,EAAiCvD,EAAQsL,EAAqC7H,YC1Z7BzD,EACAwD,EACAC,GACnD,IAIM+H,EAJSxL,EAAOE,QAISuL,kBAAkB/H,QAC7CD,EACF+H,EAAgB7H,YAAYH,GAE5BgI,EAAgB5H,YAAYJ,GDkZ5BkI,CAAqC1L,EAAQsL,EAAY7H,GAI7D,SAAS8H,GAAiFZ,GACxF,IAAMlC,EAAckC,EAAmBlC,YACjCmC,EAAcD,EAAmBC,YAKvC,OAAO,IAAID,EAAmBE,gBAC5BF,EAAmB9C,OAAQ8C,EAAmBnC,WAAYC,EAAcmC,GAG5E,SAASb,GAAgDjC,EACAD,EACAW,EACAJ,GACvDN,EAAWf,OAAO5H,KAAK,CAAE0I,SAAQW,aAAYJ,eAC7CN,EAAWd,iBAAmBoB,EAGhC,SAASuD,GAA4D7D,EACA6C,GACnE,IAAMC,EAAcD,EAAmBC,YAEjCgB,EAAsBjB,EAAmBlC,YAAckC,EAAmBlC,YAAcmC,EAExFiB,EAAiBlK,KAAKmK,IAAIhE,EAAWd,gBACX2D,EAAmBvC,WAAauC,EAAmBlC,aAC7EsD,EAAiBpB,EAAmBlC,YAAcoD,EAClDG,EAAkBD,EAAiBA,EAAiBnB,EAEtDqB,EAA4BJ,EAC5BK,GAAQ,EACRF,EAAkBJ,IACpBK,EAA4BD,EAAkBrB,EAAmBlC,YACjEyD,GAAQ,GAKV,IAFA,ID7eiCC,EACAC,EACAC,EACAC,EACAC,ECye3BC,EAAQ1E,EAAWf,OAElBkF,EAA4B,GAAG,CACpC,IAAMQ,EAAcD,EAAMjE,OAEpBmE,EAAc/K,KAAKmK,IAAIG,EAA2BQ,EAAYrE,YAE9DuE,EAAYhC,EAAmBnC,WAAamC,EAAmBlC,YDpftC0D,ECqfZxB,EAAmB9C,ODpfPuE,ECofeO,EDnffN,ECmf0BI,EAAY5E,ODlftCyE,ECkf8CG,EAAYjE,WDjf1D+D,ECifsEG,EDhfvG,IAAIzD,WAAWkD,GAAMS,IAAI,IAAI3D,WAAWoD,EAAKC,EAAWC,GAAIH,GCkftDK,EAAYrE,aAAesE,EAC7BF,EAAM9I,SAEN+I,EAAYjE,YAAckE,EAC1BD,EAAYrE,YAAcsE,GAE5B5E,EAAWd,iBAAmB0F,EAE9BG,GAAuD/E,EAAY4E,EAAa/B,GAEhFsB,GAA6BS,EAS/B,OAAOR,EAGT,SAASW,GAAuD/E,EACAb,EACA0D,GAG9DmC,GAAkDhF,GAClD6C,EAAmBlC,aAAexB,EAGpC,SAASsD,GAA6CzC,GAGjB,IAA/BA,EAAWd,iBAAyBc,EAAWyB,iBACjDI,GAA4C7B,GAC5C8B,GAAoB9B,EAAW2B,gCAE/BU,GAA6CrC,GAIjD,SAASgF,GAAkDhF,GACzB,OAA5BA,EAAWkB,eAIflB,EAAWkB,aAAapB,6CAA0C1K,EAClE4K,EAAWkB,aAAatB,MAAQ,KAChCI,EAAWkB,aAAe,MAG5B,SAASkB,GAAiEpC,GAGxE,KAAOA,EAAWQ,kBAAkBpJ,OAAS,GAAG,CAC9C,GAAmC,IAA/B4I,EAAWd,gBACb,OAGF,IAAM2D,EAAqB7C,EAAWQ,kBAAkBC,OAEpDoD,GAA4D7D,EAAY6C,KAC1EoC,GAAiDjF,GAEjDuD,GACEvD,EAAW2B,8BACXkB,KAsHR,SAAS5C,GAA4CD,EAA0CH,GAC7F,IAAMU,EAAkBP,EAAWQ,kBAAkBC,OAIrD,GAAc,WAFAT,EAAW2B,8BAA8BtJ,OAE/B,CACtB,GAAqB,IAAjBwH,EACF,MAAM,IAAI1J,UAAU,qEApD1B,SAA0D6J,EACAO,GACxDA,EAAgBR,OAA6BQ,EAAgBR,OAI7D,IAAM7H,EAAS8H,EAAW2B,8BAC1B,GAAIQ,GAA4BjK,GAC9B,KAAOgL,GAAqChL,GAAU,GAAG,CAEvDqL,GAAqDrL,EAD1B+M,GAAiDjF,KA8C9EkF,CAAiDlF,EAAYO,QAxCjE,SAA4DP,EACAH,EACAgD,GAC1D,GAAIA,EAAmBlC,YAAcd,EAAegD,EAAmBvC,WACrE,MAAM,IAAIjB,WAAW,6BAKvB,GAFA0F,GAAuD/E,EAAYH,EAAcgD,KAE7EA,EAAmBlC,YAAckC,EAAmBC,aAAxD,CAKAmC,GAAiDjF,GAEjD,IAAMmF,EAAgBtC,EAAmBlC,YAAckC,EAAmBC,YAC1E,GAAIqC,EAAgB,EAAG,CACrB,IAAMC,EAAMvC,EAAmBnC,WAAamC,EAAmBlC,YACzD0E,EAAYxC,EAAmB9C,OAAOP,MAAM4F,EAAMD,EAAeC,GACvEnD,GAAgDjC,EAAYqF,EAAW,EAAGA,EAAU/E,YAGtFuC,EAAmB9C,OAA6B8C,EAAmB9C,OACnE8C,EAAmBlC,aAAewE,EAClC5B,GAAqDvD,EAAW2B,8BAA+BkB,GAE/FT,GAAiEpC,IAiB/DsF,CAAmDtF,EAAYH,EAAcU,GAG/E8B,GAA6CrC,GAG/C,SAASiF,GAAiDjF,GACxD,IAAMuF,EAAavF,EAAWQ,kBAAkB5E,QAEhD,OADAoJ,GAAkDhF,GAC3CuF,EAmCT,SAAS1D,GAA4C7B,GACnDA,EAAWsD,oBAAiBlO,EAC5B4K,EAAWuC,sBAAmBnN,EAiEhC,SAASwM,GAAkC5B,EAA0CvD,GACnF,IAAMvE,EAAS8H,EAAW2B,8BAEJ,aAAlBzJ,EAAOG,UAhYb,SAA2D2H,GACzDgF,GAAkDhF,GAClDA,EAAWQ,kBAAoB,IAAIzJ,EAkYnCyO,CAAkDxF,GAElDV,GAAWU,GACX6B,GAA4C7B,GAC5CyF,GAAoBvN,EAAQuE,IAG9B,SAAS+E,GAA2CxB,GAClD,IAAM0B,EAAQ1B,EAAW2B,8BAA8BtJ,OAEvD,MAAc,YAAVqJ,EACK,KAEK,WAAVA,EACK,EAGF1B,EAAW0F,aAAe1F,EAAWd,yBAuF9ByG,GACdzN,EACA0N,EACAC,GAEA,IAAM7F,EAA2ClJ,OAAOuK,OAAON,GAA6B9M,WAExF6R,EAAiD,aACjDC,EAAqC,WAAM,OAAArR,OAAoBU,IAC/D4Q,EAAkD,WAAM,OAAAtR,OAAoBU,SAE7CA,IAA/BwQ,EAAqBK,QACvBH,EAAiB,WAAM,OAAAF,EAAqBK,MAAOjG,UAEnB5K,IAA9BwQ,EAAqBM,OACvBH,EAAgB,WAAM,OAAAH,EAAqBM,KAAMlG,UAEf5K,IAAhCwQ,EAAqB9I,SACvBkJ,EAAkB,SAAAnR,GAAU,OAAA+Q,EAAqB9I,OAAQjI,KAG3D,IAAM6N,EAAwBkD,EAAqBlD,gCA5EHxK,EACA8H,EACA8F,EACAC,EACAC,EACAH,EACAnD,GAOhD1C,EAAW2B,8BAAgCzJ,EAE3C8H,EAAWqD,YAAa,EACxBrD,EAAWoD,UAAW,EAEtBpD,EAAWkB,aAAe,KAG1BlB,EAAWf,OAASe,EAAWd,qBAAkB9J,EACjDkK,GAAWU,GAEXA,EAAWyB,iBAAkB,EAC7BzB,EAAWiD,UAAW,EAEtBjD,EAAW0F,aAAeG,EAE1B7F,EAAWsD,eAAiByC,EAC5B/F,EAAWuC,iBAAmByD,EAE9BhG,EAAW2C,uBAAyBD,EAEpC1C,EAAWQ,kBAAoB,IAAIzJ,EAEnCmB,EAAO0E,0BAA4BoD,EAGnC7K,EACET,EAFkBoR,MAGlB,WACE9F,EAAWiD,UAAW,EAKtBZ,GAA6CrC,MAE/C,SAAAmG,GACEvE,GAAkC5B,EAAYmG,MA4BlDC,CACElO,EAAQ8H,EAAY8F,EAAgBC,EAAeC,EAAiBH,EAAenD,GAiBvF,SAAS/C,GAA+B1G,GACtC,OAAO,IAAI9C,UACT,uCAAuC8C,sDAK3C,SAASgI,GAAwChI,GAC/C,OAAO,IAAI9C,UACT,0CAA0C8C,kEC/6B9BoN,GAA4DnO,EACAwL,GAIzExL,EAAOE,QAAsCuL,kBAAkBtM,KAAKqM,YAkBvDR,GAAqChL,GACnD,OAAQA,EAAOE,QAAqCuL,kBAAkBvM,gBAGxD+K,GAA4BjK,GAC1C,IAAMD,EAASC,EAAOE,QAEtB,YAAehD,IAAX6C,KAICqO,GAA2BrO,GDsSlCnB,OAAO+F,iBAAiBkE,GAA6B9M,UAAW,CAC9DsS,MAAO,CAAExJ,YAAY,GACrByJ,QAAS,CAAEzJ,YAAY,GACvB0J,MAAO,CAAE1J,YAAY,GACrBqE,YAAa,CAAErE,YAAY,GAC3B2J,YAAa,CAAE3J,YAAY,KAEK,iBAAvB5J,EAAOgK,aAChBrG,OAAOsG,eAAe2D,GAA6B9M,UAAWd,EAAOgK,YAAa,CAChFxI,MAAO,+BACP0I,cAAc,sBC9QhB,kCAAYnF,GAIV,GAHAsC,EAAuBtC,EAAQ,EAAG,4BAClCgD,EAAqBhD,EAAQ,mBAEzBiE,GAAuBjE,GACzB,MAAM,IAAI/B,UAAU,+EAGtB,IAAK6K,GAA+B9I,EAAO0E,2BACzC,MAAM,IAAIzG,UAAU,+FAItB6B,EAAsCzB,KAAM2B,GAE5C3B,KAAKoN,kBAAoB,IAAI5M,EA6FjC,OAtFED,sBAAI6P,iDAAJ,WACE,OAAKL,GAA2B/P,MAIzBA,KAAK2C,eAHHtE,EAAoBgS,GAA8B,4CAS7DD,0CAAA,SAAO9R,GACL,oBADKA,UACAyR,GAA2B/P,WAIEnB,IAA9BmB,KAAK4B,qBACAvD,EAAoBoE,EAAoB,WAG1CL,EAAkCpC,KAAM1B,GAPtCD,EAAoBgS,GAA8B,YAe7DD,wCAAA,SAAgCxG,GAC9B,IAAKmG,GAA2B/P,MAC9B,OAAO3B,EAAoBgS,GAA8B,SAG3D,IAAKxG,YAAYC,OAAOF,GACtB,OAAOvL,EAAoB,IAAIuB,UAAU,sCAE3C,GAAwB,IAApBgK,EAAKG,WACP,OAAO1L,EAAoB,IAAIuB,UAAU,uCAE3C,GAA+B,IAA3BgK,EAAKJ,OAAOO,WACd,OAAO1L,EAAoB,IAAIuB,UAAU,gDAG3C,QAAkCf,IAA9BmB,KAAK4B,qBACP,OAAOvD,EAAoBoE,EAAoB,cAGjD,IAAIqD,EACAC,EACEvH,EAAUP,GAA4C,SAACJ,EAASG,GACpE8H,EAAiBjI,EACjBkI,EAAgB/H,KAQlB,OAwDJ,SAAiE0D,EACAkI,EACAuD,GAC/D,IAAMxL,EAASD,EAAOE,qBAItBD,EAAOyE,YAAa,EAEE,YAAlBzE,EAAOG,OACTqL,EAAgBlH,YAAYtE,EAAOQ,uBD2UrCsH,EACAG,EACAuD,GAEA,IAAMxL,EAAS8H,EAAW2B,8BAEtBmB,EAAc,EACd3C,EAAK0G,cAAgBC,WACvBhE,EAAe3C,EAAK0G,YAA8CE,mBAGpE,IAAMC,EAAO7G,EAAK0G,YAGZhE,EAAgD,CACpD9C,OAFiCI,EAAKJ,OAGtCW,WAAYP,EAAKO,WACjBJ,WAAYH,EAAKG,WACjBK,YAAa,EACbmC,cACAC,gBAAiBiE,EACjBhE,WAAY,QAGd,GAAIhD,EAAWQ,kBAAkBpJ,OAAS,EAQxC,OAPA4I,EAAWQ,kBAAkBnJ,KAAKwL,QAMlCwD,GAAiCnO,EAAQwL,GAI3C,GAAsB,WAAlBxL,EAAOG,OAAX,CAMA,GAAI2H,EAAWd,gBAAkB,EAAG,CAClC,GAAI2E,GAA4D7D,EAAY6C,GAAqB,CAC/F,IAAMW,EAAaC,GAAyDZ,GAK5E,OAHAJ,GAA6CzC,QAE7C0D,EAAgB5H,YAAY0H,GAI9B,GAAIxD,EAAWyB,gBAAiB,CAC9B,IAAMhF,EAAI,IAAItG,UAAU,2DAIxB,OAHAyL,GAAkC5B,EAAYvD,QAE9CiH,EAAgBlH,YAAYC,IAKhCuD,EAAWQ,kBAAkBnJ,KAAKwL,GAElCwD,GAAoCnO,EAAQwL,GAC5CrB,GAA6CrC,OA5B7C,CACE,IAAMiH,EAAY,IAAID,EAAKnE,EAAmB9C,OAAQ8C,EAAmBnC,WAAY,GACrFgD,EAAgB7H,YAAYoL,IC9W5BC,CACEhP,EAAO0E,0BACPuD,EACAuD,GAxEFyD,CAA6B5Q,KAAM4J,EALS,CAC1CrE,YAAa,SAAAJ,GAAS,OAAAW,EAAe,CAAE1H,MAAO+G,EAAOC,MAAM,KAC3DE,YAAa,SAAAH,GAAS,OAAAW,EAAe,CAAE1H,MAAO+G,EAAOC,MAAM,KAC3Da,YAAa,SAAAC,GAAK,OAAAH,EAAcG,MAG3B1H,GAYT4R,+CAAA,WACE,IAAKL,GAA2B/P,MAC9B,MAAMqQ,GAA8B,eAGtC,QAAkCxR,IAA9BmB,KAAK4B,qBAAT,CAIA,GAAI5B,KAAKoN,kBAAkBvM,OAAS,EAClC,MAAM,IAAIjB,UAAU,uFAGtB0C,EAAmCtC,6CAmBvB+P,GAA2B1S,GACzC,QAAKD,EAAaC,MAIbkD,OAAO7C,UAAUyI,eAAexH,KAAKtB,EAAG,qBA6B/C,SAASgT,GAA8B3N,GACrC,OAAO,IAAI9C,UACT,sCAAsC8C,8DCvQ1BmO,GAAqBC,EAA2BC,GACtD,IAAAzB,EAAkBwB,gBAE1B,QAAsBjS,IAAlByQ,EACF,OAAOyB,EAGT,GAAI7I,GAAYoH,IAAkBA,EAAgB,EAChD,MAAM,IAAIxG,WAAW,yBAGvB,OAAOwG,WAGO0B,GAAwBF,GAC9B,IAAAlI,EAASkI,OAEjB,OAAKlI,GACI,WAAM,OAAA,YClBDqI,GAA0BC,EACArN,GACxCF,EAAiBuN,EAAMrN,GACvB,IAAMyL,EAAgB4B,MAAAA,SAAAA,EAAM5B,cACtB1G,EAAOsI,MAAAA,SAAAA,EAAMtI,KACnB,MAAO,CACL0G,mBAAiCzQ,IAAlByQ,OAA8BzQ,EAAYwF,EAA0BiL,GACnF1G,UAAe/J,IAAT+J,OAAqB/J,EAAYsS,GAA2BvI,EAAS/E,8BAI/E,SAASsN,GAA8B5R,EACAsE,GAErC,OADAC,EAAevE,EAAIsE,GACZ,SAAAsB,GAAS,OAAAd,EAA0B9E,EAAG4F,KCoB/C,SAASiM,GACP7R,EACA8R,EACAxN,GAGA,OADAC,EAAevE,EAAIsE,GACZ,SAACvF,GAAgB,OAAAyB,EAAYR,EAAI8R,EAAU,CAAC/S,KAGrD,SAASgT,GACP/R,EACA8R,EACAxN,GAGA,OADAC,EAAevE,EAAIsE,GACZ,WAAM,OAAA9D,EAAYR,EAAI8R,EAAU,KAGzC,SAASE,GACPhS,EACA8R,EACAxN,GAGA,OADAC,EAAevE,EAAIsE,GACZ,SAAC4F,GAAgD,OAAAjK,EAAYD,EAAI8R,EAAU,CAAC5H,KAGrF,SAAS+H,GACPjS,EACA8R,EACAxN,GAGA,OADAC,EAAevE,EAAIsE,GACZ,SAACsB,EAAUsE,GAAgD,OAAA1J,EAAYR,EAAI8R,EAAU,CAAClM,EAAOsE,cCpEtFgI,GAAqBpU,EAAYwG,GAC/C,IAAK6N,GAAiBrU,GACpB,MAAM,IAAIuC,UAAaiE,+BJmN3BtD,OAAO+F,iBAAiB8J,GAAyB1S,UAAW,CAC1D6I,OAAQ,CAAEC,YAAY,GACtBC,KAAM,CAAED,YAAY,GACpBE,YAAa,CAAEF,YAAY,GAC3BG,OAAQ,CAAEH,YAAY,KAEU,iBAAvB5J,EAAOgK,aAChBrG,OAAOsG,eAAeuJ,GAAyB1S,UAAWd,EAAOgK,YAAa,CAC5ExI,MAAO,2BACP0I,cAAc,sBKhJhB,wBAAY6K,EACAC,gBADAD,mBACAC,WACgB/S,IAAtB8S,EACFA,EAAoB,KAEpB5N,EAAa4N,EAAmB,mBAGlC,IAAMb,EAAWG,GAAuBW,EAAa,oBAC/CC,WF7E+BR,EACAxN,GACvCF,EAAiB0N,EAAUxN,GAC3B,IAAMiO,EAAQT,MAAAA,SAAAA,EAAUS,MAClB9B,EAAQqB,MAAAA,SAAAA,EAAUrB,MAClBN,EAAQ2B,MAAAA,SAAAA,EAAU3B,MAClBqC,EAAOV,MAAAA,SAAAA,EAAUU,KACjBC,EAAQX,MAAAA,SAAAA,EAAUW,MACxB,MAAO,CACLF,WAAiBjT,IAAViT,OACLjT,EACAuS,GAAmCU,EAAOT,EAAcxN,8BAC1DmM,WAAiBnR,IAAVmR,OACLnR,EACAyS,GAAmCtB,EAAOqB,EAAcxN,8BAC1D6L,WAAiB7Q,IAAV6Q,OACL7Q,EACA0S,GAAmC7B,EAAO2B,EAAcxN,8BAC1DmO,WAAiBnT,IAAVmT,OACLnT,EACA2S,GAAmCQ,EAAOX,EAAcxN,8BAC1DkO,QEwDuBE,CAAsBN,EAAmB,mBAKhE,GAHAO,GAAyBlS,WAGZnB,IADAgT,EAAeE,KAE1B,MAAM,IAAIjJ,WAAW,6BAGvB,IAAMqJ,EAAgBnB,GAAqBF,IAw7B/C,SAAmEnP,EACAkQ,EACAvC,EACA6C,GACjE,IAAM1I,EAAalJ,OAAOuK,OAAOsH,GAAgC1U,WAE7D6R,EAAiD,aACjD8C,EAA8C,WAAM,OAAAlU,OAAoBU,IACxEyT,EAAsC,WAAM,OAAAnU,OAAoBU,IAChE0T,EAAiD,WAAM,OAAApU,OAAoBU,SAElDA,IAAzBgT,EAAenC,QACjBH,EAAiB,WAAM,OAAAsC,EAAenC,MAAOjG,UAElB5K,IAAzBgT,EAAeG,QACjBK,EAAiB,SAAAlN,GAAS,OAAA0M,EAAeG,MAAO7M,EAAOsE,UAE5B5K,IAAzBgT,EAAe7B,QACjBsC,EAAiB,WAAM,OAAAT,EAAe7B,eAEXnR,IAAzBgT,EAAeC,QACjBS,EAAiB,SAAAjU,GAAU,OAAAuT,EAAeC,MAAOxT,KAGnDkU,GACE7Q,EAAQ8H,EAAY8F,EAAgB8C,EAAgBC,EAAgBC,EAAgBjD,EAAe6C,GA98BnGM,CAAuDzS,KAAM6R,EAFvChB,GAAqBC,EAAU,GAEuCqB,GA0EhG,OApEE5R,sBAAImS,uCAAJ,WACE,IAAKhB,GAAiB1R,MACpB,MAAM2S,GAA0B,UAGlC,OAAOC,GAAuB5S,uCAYhC0S,+BAAA,SAAMpU,GACJ,oBADIA,UACCoT,GAAiB1R,MAIlB4S,GAAuB5S,MAClB3B,EAAoB,IAAIuB,UAAU,oDAGpCiT,GAAoB7S,KAAM1B,GAPxBD,EAAoBsU,GAA0B,WAkBzDD,+BAAA,WACE,OAAKhB,GAAiB1R,MAIlB4S,GAAuB5S,MAClB3B,EAAoB,IAAIuB,UAAU,oDAGvCkT,GAAoC9S,MAC/B3B,EAAoB,IAAIuB,UAAU,2CAGpCmT,GAAoB/S,MAXlB3B,EAAoBsU,GAA0B,WAsBzDD,mCAAA,WACE,IAAKhB,GAAiB1R,MACpB,MAAM2S,GAA0B,aAGlC,OAAOK,GAAmChT,yBAsC9C,SAASgT,GAAsCrR,GAC7C,OAAO,IAAIsR,GAA4BtR,GAsBzC,SAASuQ,GAA4BvQ,GACnCA,EAAOG,OAAS,WAIhBH,EAAOQ,kBAAetD,EAEtB8C,EAAOuR,aAAUrU,EAIjB8C,EAAOwR,+BAA4BtU,EAInC8C,EAAOyR,eAAiB,IAAI5S,EAI5BmB,EAAO0R,2BAAwBxU,EAI/B8C,EAAO2R,mBAAgBzU,EAIvB8C,EAAO4R,2BAAwB1U,EAG/B8C,EAAO6R,0BAAuB3U,EAG9B8C,EAAO8R,eAAgB,EAGzB,SAAS/B,GAAiBrU,GACxB,QAAKD,EAAaC,MAIbkD,OAAO7C,UAAUyI,eAAexH,KAAKtB,EAAG,6BAO/C,SAASuV,GAAuBjR,GAG9B,YAAuB9C,IAAnB8C,EAAOuR,QAOb,SAASL,GAAoBlR,EAAwBrD,GACnD,IAAM6M,EAAQxJ,EAAOG,OACrB,GAAc,WAAVqJ,GAAgC,YAAVA,EACxB,OAAOhN,OAAoBU,GAE7B,QAAoCA,IAAhC8C,EAAO6R,qBACT,OAAO7R,EAAO6R,qBAAqBE,SAKrC,IAAIC,GAAqB,EACX,aAAVxI,IACFwI,GAAqB,EAErBrV,OAASO,GAGX,IAAML,EAAUP,GAAiB,SAACJ,EAASG,GACzC2D,EAAO6R,qBAAuB,CAC5BE,cAAU7U,EACV+U,SAAU/V,EACVgW,QAAS7V,EACT8V,QAASxV,EACTyV,oBAAqBJ,MASzB,OANAhS,EAAO6R,qBAAsBE,SAAWlV,EAEnCmV,GACHK,GAA4BrS,EAAQrD,GAG/BE,EAGT,SAASuU,GAAoBpR,GAC3B,IAAMwJ,EAAQxJ,EAAOG,OACrB,GAAc,WAAVqJ,GAAgC,YAAVA,EACxB,OAAO9M,EAAoB,IAAIuB,UAC7B,kBAAkBuL,gEAMtB,IA6uB+C1B,EA7uBzCjL,EAAUP,GAAiB,SAACJ,EAASG,GACzC,IAAMiW,EAA6B,CACjCL,SAAU/V,EACVgW,QAAS7V,GAGX2D,EAAO2R,cAAgBW,KAGnBC,EAASvS,EAAOuR,QAOtB,YANerU,IAAXqV,GAAwBvS,EAAO8R,eAA2B,aAAVtI,GAClDgJ,GAAiCD,GAmuBnCrL,GAD+CY,EA/tBV9H,EAAOwR,0BAguBXiB,GAAe,GAChDC,GAAoD5K,GA/tB7CjL,EAqBT,SAAS8V,GAAgC3S,EAAwBuO,GAGjD,aAFAvO,EAAOG,OAQrByS,GAA6B5S,GAL3BqS,GAA4BrS,EAAQuO,GAQxC,SAAS8D,GAA4BrS,EAAwBrD,GAI3D,IAAMmL,EAAa9H,EAAOwR,0BAG1BxR,EAAOG,OAAS,WAChBH,EAAOQ,aAAe7D,EACtB,IAAM4V,EAASvS,EAAOuR,aACPrU,IAAXqV,GACFM,GAAsDN,EAAQ5V,IAoHlE,SAAkDqD,GAChD,QAAqC9C,IAAjC8C,EAAO0R,4BAAwExU,IAAjC8C,EAAO4R,sBACvD,OAAO,EAGT,OAAO,EAtHFkB,CAAyC9S,IAAW8H,EAAWiD,UAClE6H,GAA6B5S,GAIjC,SAAS4S,GAA6B5S,GAGpCA,EAAOG,OAAS,UAChBH,EAAOwR,0BAA0BpQ,KAEjC,IAAM2R,EAAc/S,EAAOQ,aAM3B,GALAR,EAAOyR,eAAeuB,SAAQ,SAAAC,GAC5BA,EAAaf,QAAQa,MAEvB/S,EAAOyR,eAAiB,IAAI5S,OAEQ3B,IAAhC8C,EAAO6R,qBAAX,CAKA,IAAMqB,EAAelT,EAAO6R,qBAG5B,GAFA7R,EAAO6R,0BAAuB3U,EAE1BgW,EAAad,oBAGf,OAFAc,EAAahB,QAAQa,QACrBI,GAAkDnT,GAKpD/C,EADgB+C,EAAOwR,0BAA0BrQ,GAAY+R,EAAaf,UAGxE,WACEe,EAAajB,WACbkB,GAAkDnT,MAEpD,SAACrD,GACCuW,EAAahB,QAAQvV,GACrBwW,GAAkDnT,WAtBpDmT,GAAkDnT,GAuFtD,SAASmR,GAAoCnR,GAC3C,YAA6B9C,IAAzB8C,EAAO2R,oBAAgEzU,IAAjC8C,EAAO4R,sBA4BnD,SAASuB,GAAkDnT,QAE5B9C,IAAzB8C,EAAO2R,gBAGT3R,EAAO2R,cAAcO,QAAQlS,EAAOQ,cACpCR,EAAO2R,mBAAgBzU,GAEzB,IAAMqV,EAASvS,EAAOuR,aACPrU,IAAXqV,GACFa,GAAiCb,EAAQvS,EAAOQ,cAIpD,SAAS6S,GAAiCrT,EAAwBsT,GAIhE,IAAMf,EAASvS,EAAOuR,aACPrU,IAAXqV,GAAwBe,IAAiBtT,EAAO8R,gBAC9CwB,EAuwBR,SAAwCf,GAItCgB,GAAoChB,GA1wBhCiB,CAA+BjB,GAI/BC,GAAiCD,IAIrCvS,EAAO8R,cAAgBwB,EAnYzB1U,OAAO+F,iBAAiBoM,GAAehV,UAAW,CAChDoU,MAAO,CAAEtL,YAAY,GACrBwJ,MAAO,CAAExJ,YAAY,GACrB4O,UAAW,CAAE5O,YAAY,GACzB6O,OAAQ,CAAE7O,YAAY,KAEU,iBAAvB5J,EAAOgK,aAChBrG,OAAOsG,eAAe6L,GAAehV,UAAWd,EAAOgK,YAAa,CAClExI,MAAO,iBACP0I,cAAc,sBAsZhB,qCAAYnF,GAIV,GAHAsC,EAAuBtC,EAAQ,EAAG,+BAClC8P,GAAqB9P,EAAQ,mBAEzBiR,GAAuBjR,GACzB,MAAM,IAAI/B,UAAU,+EAGtBI,KAAKsV,qBAAuB3T,EAC5BA,EAAOuR,QAAUlT,KAEjB,IAmpBoDkU,EAnpB9C/I,EAAQxJ,EAAOG,OAErB,GAAc,aAAVqJ,GACG2H,GAAoCnR,IAAWA,EAAO8R,cACzDyB,GAAoClV,MAEpCuV,GAA8CvV,MAGhDwV,GAAqCxV,WAChC,GAAc,aAAVmL,EACTsK,GAA8CzV,KAAM2B,EAAOQ,cAC3DqT,GAAqCxV,WAChC,GAAc,WAAVmL,EACToK,GAA8CvV,MAsoBlDwV,GADsDtB,EApoBHlU,MAsoBnD0V,GAAkCxB,OAroBzB,CAGL,IAAMQ,EAAc/S,EAAOQ,aAC3BsT,GAA8CzV,KAAM0U,GACpDiB,GAA+C3V,KAAM0U,IAuI3D,OA/HEnU,sBAAI0S,oDAAJ,WACE,OAAK2C,GAA8B5V,MAI5BA,KAAK2C,eAHHtE,EAAoBwX,GAAiC,4CAchEtV,sBAAI0S,yDAAJ,WACE,IAAK2C,GAA8B5V,MACjC,MAAM6V,GAAiC,eAGzC,QAAkChX,IAA9BmB,KAAKsV,qBACP,MAAMQ,GAA2B,eAGnC,OA2LJ,SAAmD5B,GACjD,IAAMvS,EAASuS,EAAOoB,qBAChBnK,EAAQxJ,EAAOG,OAErB,GAAc,YAAVqJ,GAAiC,aAAVA,EACzB,OAAO,KAGT,GAAc,WAAVA,EACF,OAAO,EAGT,OAAO4K,GAA8CpU,EAAOwR,2BAvMnD6C,CAA0ChW,uCAWnDO,sBAAI0S,mDAAJ,WACE,OAAK2C,GAA8B5V,MAI5BA,KAAKiW,cAHH5X,EAAoBwX,GAAiC,2CAShE5C,4CAAA,SAAM3U,GACJ,oBADIA,UACCsX,GAA8B5V,WAIDnB,IAA9BmB,KAAKsV,qBACAjX,EAAoByX,GAA2B,UA4G5D,SAA0C5B,EAAqC5V,GAK7E,OAAOuU,GAJQqB,EAAOoB,qBAIahX,GA9G1B4X,CAAiClW,KAAM1B,GAPrCD,EAAoBwX,GAAiC,WAahE5C,4CAAA,WACE,IAAK2C,GAA8B5V,MACjC,OAAO3B,EAAoBwX,GAAiC,UAG9D,IAAMlU,EAAS3B,KAAKsV,qBAEpB,YAAezW,IAAX8C,EACKtD,EAAoByX,GAA2B,UAGpDhD,GAAoCnR,GAC/BtD,EAAoB,IAAIuB,UAAU,2CAGpCuW,GAAiCnW,OAa1CiT,kDAAA,WACE,IAAK2C,GAA8B5V,MACjC,MAAM6V,GAAiC,oBAK1BhX,IAFAmB,KAAKsV,sBAQpBc,GAAmCpW,OAarCiT,4CAAA,SAAM9N,GACJ,oBADIA,OAAWtG,GACV+W,GAA8B5V,WAIDnB,IAA9BmB,KAAKsV,qBACAjX,EAAoByX,GAA2B,aAGjDO,GAAiCrW,KAAMmF,GAPrC9G,EAAoBwX,GAAiC,0CA6BlE,SAASD,GAAuCvY,GAC9C,QAAKD,EAAaC,MAIbkD,OAAO7C,UAAUyI,eAAexH,KAAKtB,EAAG,wBAiB/C,SAAS8Y,GAAiCjC,GAKxC,OAAOnB,GAJQmB,EAAOoB,sBA0BxB,SAASgB,GAAuDpC,EAAqChE,GAChE,YAA/BgE,EAAOqC,oBACTxB,GAAiCb,EAAQhE,GAkc7C,SAAmDgE,EAAqC5V,GAKtFqX,GAA+CzB,EAAQ5V,GArcrDkY,CAA0CtC,EAAQhE,GAItD,SAASsE,GAAsDN,EAAqChE,GAChE,YAA9BgE,EAAOuC,mBACTC,GAAgCxC,EAAQhE,GAmf5C,SAAkDgE,EAAqC5V,GAIrFmX,GAA8CvB,EAAQ5V,GArfpDqY,CAAyCzC,EAAQhE,GAmBrD,SAASkG,GAAmClC,GAC1C,IAAMvS,EAASuS,EAAOoB,qBAIhBsB,EAAgB,IAAIhX,UACxB,oFAEF4U,GAAsDN,EAAQ0C,GAI9DN,GAAuDpC,EAAQ0C,GAE/DjV,EAAOuR,aAAUrU,EACjBqV,EAAOoB,0BAAuBzW,EAGhC,SAASwX,GAAoCnC,EAAwC/O,GACnF,IAAMxD,EAASuS,EAAOoB,qBAIhB7L,EAAa9H,EAAOwR,0BAEpB0D,EAgNR,SAAwDpN,EACAtE,GACtD,IACE,OAAOsE,EAAWqN,uBAAuB3R,GACzC,MAAO4R,GAEP,OADAC,GAA6CvN,EAAYsN,GAClD,GAtNSE,CAA4CxN,EAAYtE,GAE1E,GAAIxD,IAAWuS,EAAOoB,qBACpB,OAAOjX,EAAoByX,GAA2B,aAGxD,IAAM3K,EAAQxJ,EAAOG,OACrB,GAAc,YAAVqJ,EACF,OAAO9M,EAAoBsD,EAAOQ,cAEpC,GAAI2Q,GAAoCnR,IAAqB,WAAVwJ,EACjD,OAAO9M,EAAoB,IAAIuB,UAAU,6DAE3C,GAAc,aAAVuL,EACF,OAAO9M,EAAoBsD,EAAOQ,cAKpC,IAAM3D,EAhiBR,SAAuCmD,GAarC,OATgB1D,GAAiB,SAACJ,EAASG,GACzC,IAAM4W,EAA6B,CACjChB,SAAU/V,EACVgW,QAAS7V,GAGX2D,EAAOyR,eAAetS,KAAK8T,MAshBbsC,CAA8BvV,GAI9C,OAuMF,SAAiD8H,EACAtE,EACA0R,GAC/C,IACEhO,GAAqBY,EAAYtE,EAAO0R,GACxC,MAAOM,GAEP,YADAH,GAA6CvN,EAAY0N,GAI3D,IAAMxV,EAAS8H,EAAW2N,0BAC1B,IAAKtE,GAAoCnR,IAA6B,aAAlBA,EAAOG,OAAuB,CAChF,IAAMmT,EAAeoC,GAA+C5N,GACpEuL,GAAiCrT,EAAQsT,GAG3CZ,GAAoD5K,GAzNpD6N,CAAqC7N,EAAYtE,EAAO0R,GAEjDrY,EAlJT+B,OAAO+F,iBAAiB2M,GAA4BvV,UAAW,CAC7DoU,MAAO,CAAEtL,YAAY,GACrBwJ,MAAO,CAAExJ,YAAY,GACrBE,YAAa,CAAEF,YAAY,GAC3BwL,MAAO,CAAExL,YAAY,GACrBG,OAAQ,CAAEH,YAAY,GACtB2J,YAAa,CAAE3J,YAAY,GAC3BqH,MAAO,CAAErH,YAAY,KAEW,iBAAvB5J,EAAOgK,aAChBrG,OAAOsG,eAAeoM,GAA4BvV,UAAWd,EAAOgK,YAAa,CAC/ExI,MAAO,8BACP0I,cAAc,IAyIlB,IAAMsN,GAA+B,iBA6BnC,2CACE,MAAM,IAAIxU,UAAU,uBAoCxB,OA1BEwS,gDAAA,SAAMlM,GACJ,gBADIA,WAwCR,SAA2C7I,GACzC,IAAKD,EAAaC,GAChB,OAAO,EAGT,IAAKkD,OAAO7C,UAAUyI,eAAexH,KAAKtB,EAAG,6BAC3C,OAAO,EAGT,OAAO,EAhDAka,CAAkCvX,MACrC,MAAM,IAAIJ,UACR,yGAGU,aADAI,KAAKoX,0BAA0BtV,QAO7C0V,GAAqCxX,KAAMkG,IAI7CkM,0CAACtP,GAAD,SAAaxE,GACX,IAAMoJ,EAAS1H,KAAKyX,gBAAgBnZ,GAEpC,OADAoZ,GAA+C1X,MACxC0H,GAIT0K,0CAACrP,GAAD,WACEgG,GAAW/I,0CA4Bf,SAASwS,GAAwC7Q,EACA8H,EACA8F,EACA8C,EACAC,EACAC,EACAjD,EACA6C,GAI/C1I,EAAW2N,0BAA4BzV,EACvCA,EAAOwR,0BAA4B1J,EAGnCA,EAAWf,YAAS7J,EACpB4K,EAAWd,qBAAkB9J,EAC7BkK,GAAWU,GAEXA,EAAWiD,UAAW,EAEtBjD,EAAWqN,uBAAyB3E,EACpC1I,EAAW0F,aAAeG,EAE1B7F,EAAWkO,gBAAkBtF,EAC7B5I,EAAWmO,gBAAkBtF,EAC7B7I,EAAWgO,gBAAkBlF,EAE7B,IAAM0C,EAAeoC,GAA+C5N,GACpEuL,GAAiCrT,EAAQsT,GAIzCrW,EADqBT,EADDoR,MAIlB,WAEE9F,EAAWiD,UAAW,EACtB2H,GAAoD5K,MAEtD,SAAAmG,GAEEnG,EAAWiD,UAAW,EACtB4H,GAAgC3S,EAAQiO,MAmC9C,SAAS8H,GAA+CjO,GACtDA,EAAWkO,qBAAkB9Y,EAC7B4K,EAAWmO,qBAAkB/Y,EAC7B4K,EAAWgO,qBAAkB5Y,EAC7B4K,EAAWqN,4BAAyBjY,EAkBtC,SAASkX,GAA8CtM,GACrD,OAAOA,EAAW0F,aAAe1F,EAAWd,gBAwB9C,SAAS0L,GAAuD5K,GAC9D,IAAM9H,EAAS8H,EAAW2N,0BAE1B,GAAK3N,EAAWiD,eAIqB7N,IAAjC8C,EAAO0R,sBAMX,GAAc,aAFA1R,EAAOG,QAOrB,GAAiC,IAA7B2H,EAAWf,OAAO7H,OAAtB,CAIA,IAAMzC,EAAuBqL,ERllCNf,OAAOwB,OAClB9L,MQklCRA,IAAUgW,GAahB,SAAqD3K,GACnD,IAAM9H,EAAS8H,EAAW2N,2BAloB5B,SAAgDzV,GAG9CA,EAAO4R,sBAAwB5R,EAAO2R,cACtC3R,EAAO2R,mBAAgBzU,GAgoBvBgZ,CAAuClW,GAEvC4G,GAAakB,GAGb,IAAMqO,EAAmBrO,EAAWmO,kBACpCF,GAA+CjO,GAC/C7K,EACEkZ,GACA,YA1sBJ,SAA2CnW,GAEzCA,EAAO4R,sBAAuBK,cAAS/U,GACvC8C,EAAO4R,2BAAwB1U,EAMjB,aAJA8C,EAAOG,SAMnBH,EAAOQ,kBAAetD,OACcA,IAAhC8C,EAAO6R,uBACT7R,EAAO6R,qBAAqBI,WAC5BjS,EAAO6R,0BAAuB3U,IAIlC8C,EAAOG,OAAS,SAEhB,IAAMoS,EAASvS,EAAOuR,aACPrU,IAAXqV,GACFwB,GAAkCxB,GAqrBhC6D,CAAkCpW,MAEpC,SAAArD,IAhrBJ,SAAoDqD,EAAwBuO,GAE1EvO,EAAO4R,sBAAuBM,QAAQ3D,GACtCvO,EAAO4R,2BAAwB1U,OAKKA,IAAhC8C,EAAO6R,uBACT7R,EAAO6R,qBAAqBK,QAAQ3D,GACpCvO,EAAO6R,0BAAuB3U,GAEhCyV,GAAgC3S,EAAQuO,GAqqBpC8H,CAA2CrW,EAAQrD,MA5BrD2Z,CAA4CxO,GAiChD,SAAwDA,EAAgDtE,GACtG,IAAMxD,EAAS8H,EAAW2N,2BAhpB5B,SAAqDzV,GAGnDA,EAAO0R,sBAAwB1R,EAAOyR,eAAe/N,SA+oBrD6S,CAA4CvW,GAG5C/C,EADyB6K,EAAWkO,gBAAgBxS,IAGlD,YA3uBJ,SAA2CxD,GAEzCA,EAAO0R,sBAAuBO,cAAS/U,GACvC8C,EAAO0R,2BAAwBxU,EAyuB3BsZ,CAAkCxW,GAElC,IAAMwJ,EAAQxJ,EAAOG,OAKrB,GAFAyG,GAAakB,IAERqJ,GAAoCnR,IAAqB,aAAVwJ,EAAsB,CACxE,IAAM8J,EAAeoC,GAA+C5N,GACpEuL,GAAiCrT,EAAQsT,GAG3CZ,GAAoD5K,MAEtD,SAAAnL,GACwB,aAAlBqD,EAAOG,QACT4V,GAA+CjO,GAtvBvD,SAAoD9H,EAAwBuO,GAE1EvO,EAAO0R,sBAAuBQ,QAAQ3D,GACtCvO,EAAO0R,2BAAwBxU,EAI/ByV,GAAgC3S,EAAQuO,GAivBpCkI,CAA2CzW,EAAQrD,MA1DrD+Z,CAA4C5O,EAAYrL,SAZxDmW,GAA6B5S,GAgBjC,SAASqV,GAA6CvN,EAAkDyG,GAClD,aAAhDzG,EAAW2N,0BAA0BtV,QACvC0V,GAAqC/N,EAAYyG,GAyDrD,SAASmH,GAA+C5N,GAEtD,OADoBsM,GAA8CtM,IAC5C,EAKxB,SAAS+N,GAAqC/N,EAAkDyG,GAC9F,IAAMvO,EAAS8H,EAAW2N,0BAI1BM,GAA+CjO,GAC/CuK,GAA4BrS,EAAQuO,GAKtC,SAASyC,GAA0BjQ,GACjC,OAAO,IAAI9C,UAAU,4BAA4B8C,2CAKnD,SAASmT,GAAiCnT,GACxC,OAAO,IAAI9C,UACT,yCAAyC8C,wDAG7C,SAASoT,GAA2BpT,GAClC,OAAO,IAAI9C,UAAU,UAAY8C,EAAO,qCAG1C,SAAS8S,GAAqCtB,GAC5CA,EAAOvR,eAAiB1E,GAAW,SAACJ,EAASG,GAC3CkW,EAAOtR,uBAAyB/E,EAChCqW,EAAOrR,sBAAwB7E,EAC/BkW,EAAOqC,oBAAsB,aAIjC,SAASZ,GAA+CzB,EAAqC5V,GAC3FkX,GAAqCtB,GACrCa,GAAiCb,EAAQ5V,GAQ3C,SAASyW,GAAiCb,EAAqC5V,QACxCO,IAAjCqV,EAAOrR,wBAKX1D,EAA0B+U,EAAOvR,gBACjCuR,EAAOrR,sBAAsBvE,GAC7B4V,EAAOtR,4BAAyB/D,EAChCqV,EAAOrR,2BAAwBhE,EAC/BqV,EAAOqC,oBAAsB,YAW/B,SAASb,GAAkCxB,QACHrV,IAAlCqV,EAAOtR,yBAKXsR,EAAOtR,4BAAuB/D,GAC9BqV,EAAOtR,4BAAyB/D,EAChCqV,EAAOrR,2BAAwBhE,EAC/BqV,EAAOqC,oBAAsB,YAG/B,SAASrB,GAAoChB,GAC3CA,EAAO+B,cAAgBhY,GAAW,SAACJ,EAASG,GAC1CkW,EAAOoE,sBAAwBza,EAC/BqW,EAAOqE,qBAAuBva,KAEhCkW,EAAOuC,mBAAqB,UAG9B,SAAShB,GAA8CvB,EAAqC5V,GAC1F4W,GAAoChB,GACpCwC,GAAgCxC,EAAQ5V,GAG1C,SAASiX,GAA8CrB,GACrDgB,GAAoChB,GACpCC,GAAiCD,GAGnC,SAASwC,GAAgCxC,EAAqC5V,QACxCO,IAAhCqV,EAAOqE,uBAIXpZ,EAA0B+U,EAAO+B,eACjC/B,EAAOqE,qBAAqBja,GAC5B4V,EAAOoE,2BAAwBzZ,EAC/BqV,EAAOqE,0BAAuB1Z,EAC9BqV,EAAOuC,mBAAqB,YAiB9B,SAAStC,GAAiCD,QACHrV,IAAjCqV,EAAOoE,wBAIXpE,EAAOoE,2BAAsBzZ,GAC7BqV,EAAOoE,2BAAwBzZ,EAC/BqV,EAAOqE,0BAAuB1Z,EAC9BqV,EAAOuC,mBAAqB,aAtX9BlW,OAAO+F,iBAAiB8L,GAAgC1U,UAAW,CACjEwS,MAAO,CAAE1J,YAAY,KAEW,iBAAvB5J,EAAOgK,aAChBrG,OAAOsG,eAAeuL,GAAgC1U,UAAWd,EAAOgK,YAAa,CACnFxI,MAAO,kCACP0I,cAAc,ICv9BX,IAAM0R,GAA8E,oBAAjBC,aAA+BA,kBAAe5Z,ECqCxH,IAZQ4R,GAYFgI,GAzBN,SAAmChI,GACjC,GAAsB,mBAATA,GAAuC,iBAATA,EACzC,OAAO,EAET,IAEE,OADA,IAAKA,GACE,EACP,SACA,OAAO,GAkBTiI,CAA0BF,IAAsBA,KAb1C/H,GAAO,SAA0CkI,EAAkBjW,GACvE1C,KAAK2Y,QAAUA,GAAW,GAC1B3Y,KAAK0C,KAAOA,GAAQ,QAChBkW,MAAMC,mBACRD,MAAMC,kBAAkB7Y,KAAMA,KAAKsQ,eAGlC5S,UAAY6C,OAAOuK,OAAO8N,MAAMlb,WACrC6C,OAAOsG,eAAe4J,GAAK/S,UAAW,cAAe,CAAEU,MAAOqS,GAAMqI,UAAU,EAAMhS,cAAc,IAC3F2J,aCPOsI,GAAwBC,EACAlL,EACAmL,EACAC,EACAlS,EACAmS,GAUtC,IAAMzX,EAASmD,EAAsCmU,GAC/C9E,EAASlB,GAAsClF,GAErDkL,EAAO5S,YAAa,EAEpB,IAAIgT,GAAe,EAGfC,EAAelb,OAA0BU,GAE7C,OAAOZ,GAAW,SAACJ,EAASG,GAC1B,IAAIuU,EAqIuB5Q,EAAyCnD,EAAwB8a,EApI5F,QAAeza,IAAXsa,EAAsB,CAuBxB,GAtBA5G,EAAiB,WACf,IAAMrC,EAAQ,IAAIuI,GAAa,UAAW,cACpCc,EAAsC,GACvCL,GACHK,EAAQzY,MAAK,WACX,MAAoB,aAAhBgN,EAAKhM,OACA+Q,GAAoB/E,EAAMoC,GAE5B/R,OAAoBU,MAG1BmI,GACHuS,EAAQzY,MAAK,WACX,MAAsB,aAAlBkY,EAAOlX,OACFO,GAAqB2W,EAAQ9I,GAE/B/R,OAAoBU,MAG/B2a,GAAmB,WAAM,OAAAhc,QAAQic,IAAIF,EAAQG,KAAI,SAAAJ,GAAU,OAAAA,WAAY,EAAMpJ,IAG3EiJ,EAAOQ,QAET,YADApH,IAIF4G,EAAOS,iBAAiB,QAASrH,GAwEnC,GA3BAsH,EAAmBb,EAAQtX,EAAOiB,gBAAgB,SAAA+R,GAC3CwE,EAGHY,GAAS,EAAMpF,GAFf8E,GAAmB,WAAM,OAAA3G,GAAoB/E,EAAM4G,MAAc,EAAMA,MAO3EmF,EAAmB/L,EAAMoG,EAAOvR,gBAAgB,SAAA+R,GACzC1N,EAGH8S,GAAS,EAAMpF,GAFf8E,GAAmB,WAAM,OAAAnX,GAAqB2W,EAAQtE,MAAc,EAAMA,MAgDnD/S,EAzCTqX,EAyCkDxa,EAzC1CkD,EAAOiB,eAyC2D2W,EAzC3C,WAC1CL,EAGHa,IAFAN,GAAmB,WAAM,OHqpBjC,SAA8DtF,GAC5D,IAAMvS,EAASuS,EAAOoB,qBAIhBnK,EAAQxJ,EAAOG,OACrB,OAAIgR,GAAoCnR,IAAqB,WAAVwJ,EAC1ChN,OAAoBU,GAGf,YAAVsM,EACK9M,EAAoBsD,EAAOQ,cAK7BgU,GAAiCjC,GGrqBT6F,CAAqD7F,OAwC1D,WAAlBvS,EAAOG,OACTwX,IAEAxa,EAAgBN,EAAS8a,GApCzBxG,GAAoChF,IAAyB,WAAhBA,EAAKhM,OAAqB,CACzE,IAAMkY,EAAa,IAAIpa,UAAU,+EAE5BoH,EAGH8S,GAAS,EAAME,GAFfR,GAAmB,WAAM,OAAAnX,GAAqB2W,EAAQgB,MAAa,EAAMA,GAQ7E,SAASC,IAGP,IAAMC,EAAkBb,EACxB,OAAO9a,EACL8a,GACA,WAAM,OAAAa,IAAoBb,EAAeY,SAA0Bpb,KAIvE,SAASgb,EAAmBlY,EACAnD,EACA8a,GACJ,YAAlB3X,EAAOG,OACTwX,EAAO3X,EAAOQ,cAEdpD,EAAcP,EAAS8a,GAY3B,SAASE,EAAmBF,EAAgCa,EAA2BC,GAYrF,SAASC,IACPzb,EACE0a,KACA,WAAM,OAAAgB,EAASH,EAAiBC,MAChC,SAAAG,GAAY,OAAAD,GAAS,EAAMC,MAf3BnB,IAGJA,GAAe,EAEK,aAAhBtL,EAAKhM,QAA0BgR,GAAoChF,GAGrEuM,IAFAvb,EAAgBmb,IAAyBI,IAc7C,SAASP,EAASU,EAAmBtK,GAC/BkJ,IAGJA,GAAe,EAEK,aAAhBtL,EAAKhM,QAA0BgR,GAAoChF,GAGrEwM,EAASE,EAAStK,GAFlBpR,EAAgBmb,KAAyB,WAAM,OAAAK,EAASE,EAAStK,OAMrE,SAASoK,EAASE,EAAmBtK,GACnCkG,GAAmClC,GACnC5R,EAAmCZ,QAEpB7C,IAAXsa,GACFA,EAAOsB,oBAAoB,QAASlI,GAElCiI,EACFxc,EAAOkS,GAEPrS,OAAQgB,GA1EZM,EA3ESlB,GAAiB,SAACyc,EAAaC,IACpC,SAAS/S,EAAKxC,GACRA,EACFsV,IAIAnc,EASF6a,EACKjb,GAAoB,GAGtBI,EAAmB2V,EAAO+B,eAAe,WAC9C,OAAOhY,GAAoB,SAAC2c,EAAaC,GACvC7U,GACEtE,EACA,CACE6D,YAAa,SAAAJ,GACXkU,EAAe9a,EAAmB8X,GAAiCnC,EAAQ/O,QAAQtG,EAAW9B,GAC9F6d,GAAY,IAEdtV,YAAa,WAAM,OAAAsV,GAAY,IAC/B3U,YAAa4U,UAvBgBjT,EAAM+S,GAIzC/S,EAAK,4BCxDX,2CACE,MAAM,IAAIhI,UAAU,uBAsFxB,OA/EEW,sBAAIua,6DAAJ,WACE,IAAKC,GAAkC/a,MACrC,MAAMgb,GAAqC,eAG7C,OAAOC,GAA8Cjb,uCAOvD8a,gDAAA,WACE,IAAKC,GAAkC/a,MACrC,MAAMgb,GAAqC,SAG7C,IAAKE,GAAiDlb,MACpD,MAAM,IAAIJ,UAAU,mDAGtBub,GAAqCnb,OAOvC8a,kDAAA,SAAQ3V,GACN,gBADMA,OAAWtG,IACZkc,GAAkC/a,MACrC,MAAMgb,GAAqC,WAG7C,IAAKE,GAAiDlb,MACpD,MAAM,IAAIJ,UAAU,qDAGtB,OAAOwb,GAAuCpb,KAAMmF,IAMtD2V,gDAAA,SAAM5U,GACJ,gBADIA,WACC6U,GAAkC/a,MACrC,MAAMgb,GAAqC,SAG7CK,GAAqCrb,KAAMkG,IAI7C4U,0CAAC9X,GAAD,SAAc1E,GACZyK,GAAW/I,MACX,IAAM0H,EAAS1H,KAAKgM,iBAAiB1N,GAErC,OADAgd,GAA+Ctb,MACxC0H,GAIToT,0CAAC7X,GAAD,SAAY+B,GACV,IAAMrD,EAAS3B,KAAKub,0BAEpB,GAAIvb,KAAK0I,OAAO7H,OAAS,EAAG,CAC1B,IAAMsE,EAAQoD,GAAavI,MAEvBA,KAAKkL,iBAA0C,IAAvBlL,KAAK0I,OAAO7H,QACtCya,GAA+Ctb,MAC/CuL,GAAoB5J,IAEpB6Z,GAAgDxb,MAGlDgF,EAAYO,YAAYJ,QAExBJ,EAA6BpD,EAAQqD,GACrCwW,GAAgDxb,0CAoBtD,SAAS+a,GAA2C1d,GAClD,QAAKD,EAAaC,MAIbkD,OAAO7C,UAAUyI,eAAexH,KAAKtB,EAAG,6BAO/C,SAASme,GAAgD/R,GACpCgS,GAA8ChS,KAK7DA,EAAWoD,SACbpD,EAAWqD,YAAa,GAM1BrD,EAAWoD,UAAW,EAGtBjO,EADoB6K,EAAWsD,kBAG7B,WACEtD,EAAWoD,UAAW,EAElBpD,EAAWqD,aACbrD,EAAWqD,YAAa,EACxB0O,GAAgD/R,OAGpD,SAAAvD,GACEmV,GAAqC5R,EAAYvD,QAKvD,SAASuV,GAA8ChS,GACrD,IAAM9H,EAAS8H,EAAW8R,0BAE1B,QAAKL,GAAiDzR,OAIjDA,EAAWiD,cAIZ9G,GAAuBjE,IAAW6D,EAAiC7D,GAAU,IAI7DsZ,GAA8CxR,GAE/C,IAOrB,SAAS6R,GAA+C7R,GACtDA,EAAWsD,oBAAiBlO,EAC5B4K,EAAWuC,sBAAmBnN,EAC9B4K,EAAWqN,4BAAyBjY,WAKtBsc,GAAqC1R,GACnD,GAAKyR,GAAiDzR,GAAtD,CAIA,IAAM9H,EAAS8H,EAAW8R,0BAE1B9R,EAAWyB,iBAAkB,EAEI,IAA7BzB,EAAWf,OAAO7H,SACpBya,GAA+C7R,GAC/C8B,GAAoB5J,cAIRyZ,GAA0C3R,EAAgDtE,GACxG,GAAK+V,GAAiDzR,GAAtD,CAIA,IAAM9H,EAAS8H,EAAW8R,0BAE1B,GAAI3V,GAAuBjE,IAAW6D,EAAiC7D,GAAU,EAC/EuD,EAAiCvD,EAAQwD,GAAO,OAC3C,CACL,IAAI0R,SACJ,IACEA,EAAYpN,EAAWqN,uBAAuB3R,GAC9C,MAAO4R,GAEP,MADAsE,GAAqC5R,EAAYsN,GAC3CA,EAGR,IACElO,GAAqBY,EAAYtE,EAAO0R,GACxC,MAAOM,GAEP,MADAkE,GAAqC5R,EAAY0N,GAC3CA,GAIVqE,GAAgD/R,aAGlC4R,GAAqC5R,EAAkDvD,GACrG,IAAMvE,EAAS8H,EAAW8R,0BAEJ,aAAlB5Z,EAAOG,SAIXiH,GAAWU,GAEX6R,GAA+C7R,GAC/CyF,GAAoBvN,EAAQuE,aAGd+U,GAA8CxR,GAC5D,IAAM0B,EAAQ1B,EAAW8R,0BAA0BzZ,OAEnD,MAAc,YAAVqJ,EACK,KAEK,WAAVA,EACK,EAGF1B,EAAW0F,aAAe1F,EAAWd,yBAY9BuS,GAAiDzR,GAC/D,IAAM0B,EAAQ1B,EAAW8R,0BAA0BzZ,OAEnD,OAAK2H,EAAWyB,iBAA6B,aAAVC,WAOrBuQ,GAAwC/Z,EACA8H,EACA8F,EACAC,EACAC,EACAH,EACA6C,GAGtD1I,EAAW8R,0BAA4B5Z,EAEvC8H,EAAWf,YAAS7J,EACpB4K,EAAWd,qBAAkB9J,EAC7BkK,GAAWU,GAEXA,EAAWiD,UAAW,EACtBjD,EAAWyB,iBAAkB,EAC7BzB,EAAWqD,YAAa,EACxBrD,EAAWoD,UAAW,EAEtBpD,EAAWqN,uBAAyB3E,EACpC1I,EAAW0F,aAAeG,EAE1B7F,EAAWsD,eAAiByC,EAC5B/F,EAAWuC,iBAAmByD,EAE9B9N,EAAO0E,0BAA4BoD,EAGnC7K,EACET,EAFkBoR,MAGlB,WACE9F,EAAWiD,UAAW,EAKtB8O,GAAgD/R,MAElD,SAAAmG,GACEyL,GAAqC5R,EAAYmG,MAkCvD,SAASoL,GAAqCtY,GAC5C,OAAO,IAAI9C,UACT,6CAA6C8C,4DC1VjD,SAASiZ,GACPpc,EACA8R,EACAxN,GAGA,OADAC,EAAevE,EAAIsE,GACZ,SAACvF,GAAgB,OAAAyB,EAAYR,EAAI8R,EAAU,CAAC/S,KAGrD,SAASsd,GACPrc,EACA8R,EACAxN,GAGA,OADAC,EAAevE,EAAIsE,GACZ,SAAC4F,GAA4C,OAAA1J,EAAYR,EAAI8R,EAAU,CAAC5H,KAGjF,SAASoS,GACPtc,EACA8R,EACAxN,GAGA,OADAC,EAAevE,EAAIsE,GACZ,SAAC4F,GAA4C,OAAAjK,EAAYD,EAAI8R,EAAU,CAAC5H,KAGjF,SAASqS,GAA0B/J,EAAclO,GAE/C,GAAa,WADbkO,EAAO,GAAGA,GAER,MAAM,IAAInS,UAAaiE,OAAYkO,+DAErC,OAAOA,EChET,SAASgK,GAAgCC,EAAcnY,GAErD,GAAa,UADbmY,EAAO,GAAGA,GAER,MAAM,IAAIpc,UAAaiE,OAAYmY,qEAErC,OAAOA,WCbOC,GAAmBC,EACArY,GACjCF,EAAiBuY,EAASrY,GAC1B,IAAMqV,EAAegD,MAAAA,SAAAA,EAAShD,aACxBlS,EAAgBkV,MAAAA,SAAAA,EAASlV,cACzBiS,EAAeiD,MAAAA,SAAAA,EAASjD,aACxBE,EAAS+C,MAAAA,SAAAA,EAAS/C,OAIxB,YAHeta,IAAXsa,GAWN,SAA2BA,EAAiBtV,GAC1C,aCK4BzF,GAC5B,GAAqB,iBAAVA,GAAgC,OAAVA,EAC/B,OAAO,EAET,IACE,MAAiD,kBAAlCA,EAAsBub,QACrC,SAEA,OAAO,GDbJwC,CAAchD,GACjB,MAAM,IAAIvZ,UAAaiE,6BAZvBuY,CAAkBjD,EAAWtV,+BAExB,CACLqV,aAAcmD,QAAQnD,GACtBlS,cAAeqV,QAAQrV,GACvBiS,aAAcoD,QAAQpD,GACtBE,UHoHJ5Y,OAAO+F,iBAAiBwU,GAAgCpd,UAAW,CACjEsS,MAAO,CAAExJ,YAAY,GACrByJ,QAAS,CAAEzJ,YAAY,GACvB0J,MAAO,CAAE1J,YAAY,GACrB2J,YAAa,CAAE3J,YAAY,KAEK,iBAAvB5J,EAAOgK,aAChBrG,OAAOsG,eAAeiU,GAAgCpd,UAAWd,EAAOgK,YAAa,CACnFxI,MAAO,kCACP0I,cAAc,sBKvDhB,wBAAYwV,EACA1K,gBADA0K,mBACA1K,WACkB/S,IAAxByd,EACFA,EAAsB,KAEtBvY,EAAauY,EAAqB,mBAGpC,IAAMxL,EAAWG,GAAuBW,EAAa,oBAC/C2K,WJnFRvD,EACAnV,GAEAF,EAAiBqV,EAAQnV,GACzB,IAAMwN,EAAW2H,EACX7M,EAAwBkF,MAAAA,SAAAA,EAAUlF,sBAClC5F,EAAS8K,MAAAA,SAAAA,EAAU9K,OACnBoJ,EAAO0B,MAAAA,SAAAA,EAAU1B,KACjBD,EAAQ2B,MAAAA,SAAAA,EAAU3B,MAClBqC,EAAOV,MAAAA,SAAAA,EAAUU,KACvB,MAAO,CACL5F,2BAAiDtN,IAA1BsN,OACrBtN,EACA0F,EACE4H,EACGtI,8CAEP0C,YAAmB1H,IAAX0H,OACN1H,EACA8c,GAAsCpV,EAAQ8K,EAAcxN,+BAC9D8L,UAAe9Q,IAAT8Q,OACJ9Q,EACA+c,GAAoCjM,EAAM0B,EAAcxN,6BAC1D6L,WAAiB7Q,IAAV6Q,OACL7Q,EACAgd,GAAqCnM,EAAO2B,EAAcxN,8BAC5DkO,UAAelT,IAATkT,OAAqBlT,EAAYid,GAA0B/J,EAASlO,8BIyDjD2Y,CAAqCF,EAAqB,mBAInF,GAFAG,GAAyBzc,MAEK,UAA1Buc,EAAiBxK,KAAkB,CACrC,QAAsBlT,IAAlBiS,EAASlI,KACX,MAAM,IAAIE,WAAW,8DAGvBsG,GACEpP,KACAuc,EAHoB1L,GAAqBC,EAAU,QAMhD,CAEL,IAAMqB,EAAgBnB,GAAqBF,aLuP/CnP,EACA4a,EACAjN,EACA6C,GAEA,IAAM1I,EAAiDlJ,OAAOuK,OAAOgQ,GAAgCpd,WAEjG6R,EAAiD,aACjDC,EAAqC,WAAM,OAAArR,OAAoBU,IAC/D4Q,EAAkD,WAAM,OAAAtR,OAAoBU,SAEjDA,IAA3B0d,EAAiB7M,QACnBH,EAAiB,WAAM,OAAAgN,EAAiB7M,MAAOjG,UAEnB5K,IAA1B0d,EAAiB5M,OACnBH,EAAgB,WAAM,OAAA+M,EAAiB5M,KAAMlG,UAEf5K,IAA5B0d,EAAiBhW,SACnBkJ,EAAkB,SAAAnR,GAAU,OAAAie,EAAiBhW,OAAQjI,KAGvDod,GACE/Z,EAAQ8H,EAAY8F,EAAgBC,EAAeC,EAAiBH,EAAe6C,GK3QjFuK,CACE1c,KACAuc,EAHoB1L,GAAqBC,EAAU,GAKnDqB,IAmMR,OA3LE5R,sBAAIoc,uCAAJ,WACE,IAAK/X,GAAiB5E,MACpB,MAAM2S,GAA0B,UAGlC,OAAO/M,GAAuB5F,uCAShC2c,gCAAA,SAAOre,GACL,oBADKA,UACAsG,GAAiB5E,MAIlB4F,GAAuB5F,MAClB3B,EAAoB,IAAIuB,UAAU,qDAGpCyC,GAAqBrC,KAAM1B,GAPzBD,EAAoBsU,GAA0B,YA6BzDgK,mCAAA,SACEC,GAEA,gBAFAA,WAEKhY,GAAiB5E,MACpB,MAAM2S,GAA0B,aAKlC,YAAqB9T,aHlLYqd,EACArY,GACnCF,EAAiBuY,EAASrY,GAC1B,IAAMmY,EAAOE,MAAAA,SAAAA,EAASF,KACtB,MAAO,CACLA,UAAend,IAATmd,OAAqBnd,EAAYkd,GAAgCC,EAASnY,8BG2KhEgZ,CAAqBD,EAAY,mBAErCZ,KACHnX,EAAmC7E,MdtJvC,IAAIoQ,Gc0J8BpQ,OAWzC2c,qCAAA,SAAeG,EACAF,GACb,gBADaA,OACRhY,GAAiB5E,MACpB,MAAM2S,GAA0B,eAElC1O,EAAuB6Y,EAAc,EAAG,eAExC,IAAMC,WCvMwCtU,EACA5E,GAChDF,EAAiB8E,EAAM5E,GAEvB,IAAMmZ,EAAWvU,MAAAA,SAAAA,EAAMuU,SACvB7Y,EAAoB6Y,EAAU,WAAY,wBAC1CrY,EAAqBqY,EAAanZ,iCAElC,IAAMiV,EAAWrQ,MAAAA,SAAAA,EAAMqQ,SAIvB,OAHA3U,EAAoB2U,EAAU,WAAY,wBAC1CrH,GAAqBqH,EAAajV,iCAE3B,CAAEmZ,WAAUlE,YD2LCmE,CAA4BH,EAAc,mBACtDZ,EAAUD,GAAmBW,EAAY,oBAE/C,GAAIhX,GAAuB5F,MACzB,MAAM,IAAIJ,UAAU,kFAEtB,GAAIgT,GAAuBmK,EAAUjE,UACnC,MAAM,IAAIlZ,UAAU,kFAStB,OAFAT,EAJgB4Z,GACd/Y,KAAM+c,EAAUjE,SAAUoD,EAAQjD,aAAciD,EAAQhD,aAAcgD,EAAQlV,cAAekV,EAAQ/C,SAKhG4D,EAAUC,UAWnBL,gCAAA,SAAOO,EACAN,GACL,gBADKA,OACAhY,GAAiB5E,MACpB,OAAO3B,EAAoBsU,GAA0B,WAGvD,QAAoB9T,IAAhBqe,EACF,OAAO7e,EAAoB,wCAE7B,IAAKqT,GAAiBwL,GACpB,OAAO7e,EACL,IAAIuB,UAAU,8EAIlB,IAAIsc,EACJ,IACEA,EAAUD,GAAmBW,EAAY,oBACzC,MAAO1W,GACP,OAAO7H,EAAoB6H,GAG7B,OAAIN,GAAuB5F,MAClB3B,EACL,IAAIuB,UAAU,8EAGdgT,GAAuBsK,GAClB7e,EACL,IAAIuB,UAAU,8EAIXmZ,GACL/Y,KAAMkd,EAAahB,EAAQjD,aAAciD,EAAQhD,aAAcgD,EAAQlV,cAAekV,EAAQ/C,SAelGwD,6BAAA,WACE,IAAK/X,GAAiB5E,MACpB,MAAM2S,GAA0B,OAGlC,IAAMwK,WEjR2Bxb,EACAyb,GAInC,IAKIC,EACAC,EACAC,EACAC,EAEAC,EAVE/b,EAASmD,EAAsClD,GAEjD+b,GAAU,EACVC,GAAY,EACZC,GAAY,EAOVC,EAAgB5f,GAAgB,SAAAJ,GACpC4f,EAAuB5f,KAGzB,SAAS2R,IACP,OAAIkO,IAIJA,GAAU,EAgDV1X,GAAgCtE,EA9CI,CAClC6D,YAAa,SAAAnH,GAIXgB,GAAe,WACbse,GAAU,EACV,IAAMI,EAAS1f,EACT2f,EAAS3f,EAQVuf,GACHvC,GACEmC,EAAQlX,0BACRyX,GAICF,GACHxC,GACEoC,EAAQnX,0BACR0X,GAIJN,OAAqB5e,OAGzByG,YAAa,WACXoY,GAAU,EACLC,GACHxC,GAAqCoC,EAAQlX,2BAE1CuX,GACHzC,GAAqCqC,EAAQnX,4BAGjDJ,YAAa,WACXyX,GAAU,MAhDLvf,OAAoBU,GA8E/B,SAAS0Q,KAaT,OATAgO,EAAUS,GAAqBzO,EAAgBC,GA1B/C,SAA0BlR,GAGxB,GAFAqf,GAAY,EACZN,EAAU/e,EACNsf,EAAW,CACb,IAAMK,EAAkBjV,GAAoB,CAACqU,EAASC,IAChDY,EAAe7b,GAAqBV,EAAQsc,GAClDR,EAAqBS,GAEvB,OAAOL,KAmBTL,EAAUQ,GAAqBzO,EAAgBC,GAhB/C,SAA0BlR,GAGxB,GAFAsf,GAAY,EACZN,EAAUhf,EACNqf,EAAW,CACb,IAAMM,EAAkBjV,GAAoB,CAACqU,EAASC,IAChDY,EAAe7b,GAAqBV,EAAQsc,GAClDR,EAAqBS,GAEvB,OAAOL,KAUT9e,EAAc2C,EAAOiB,gBAAgB,SAACiN,GACpCyL,GAAqCkC,EAAQlX,0BAAiEuJ,GAC9GyL,GAAqCmC,EAAQnX,0BAAiEuJ,GAC9G6N,OAAqB5e,MAGhB,CAAC0e,EAASC,GFgKEW,CAAkBne,MACnC,OAAOgJ,GAAoBmU,IAe7BR,gCAAA,SAAOC,GACL,gBADKA,WACAhY,GAAiB5E,MACpB,MAAM2S,GAA0B,UAGlC,IpBrJkDhR,EACAqF,EAC9CtF,EACA0c,EACAvhB,EoBiJEqf,WG5S6BA,EACArY,GACrCF,EAAiBuY,EAASrY,GAC1B,IAAMmD,EAAgBkV,MAAAA,SAAAA,EAASlV,cAC/B,MAAO,CAAEA,cAAeqV,QAAQrV,IHwSdqX,CAAuBzB,EAAY,mBACnD,OpBtJkDjb,EoBsJL3B,KpBrJKgH,EoBqJCkV,EAAQlV,cpBpJvDtF,EAASmD,EAAsClD,GAC/Cyc,EAAO,IAAIlX,GAAgCxF,EAAQsF,IACnDnK,EAAmD0D,OAAOuK,OAAOnD,KAC9DG,mBAAqBsW,EACvBvhB,8BoBmMOmhB,GAAwBzO,EACAC,EACAC,EACAH,EACA6C,gBADA7C,kBACA6C,aAAsD,OAAA,IAG5F,IAAMxQ,EAA4BpB,OAAOuK,OAAO6R,GAAejf,WAQ/D,OAPA+e,GAAyB9a,GAGzB+Z,GACE/Z,EAFqDpB,OAAOuK,OAAOgQ,GAAgCpd,WAE/E6R,EAAgBC,EAAeC,EAAiBH,EAAe6C,GAG9ExQ,EA0BT,SAAS8a,GAAyB9a,GAChCA,EAAOG,OAAS,WAChBH,EAAOE,aAAUhD,EACjB8C,EAAOQ,kBAAetD,EACtB8C,EAAOyE,YAAa,WAGNxB,GAAiBvH,GAC/B,QAAKD,EAAaC,MAIbkD,OAAO7C,UAAUyI,eAAexH,KAAKtB,EAAG,sCAa/BuI,GAAuBjE,GAGrC,YAAuB9C,IAAnB8C,EAAOE,iBASGQ,GAAwBV,EAA2BrD,GAGjE,OAFAqD,EAAOyE,YAAa,EAEE,WAAlBzE,EAAOG,OACF3D,OAAoBU,GAEP,YAAlB8C,EAAOG,OACFzD,EAAoBsD,EAAOQ,eAGpCoJ,GAAoB5J,GAGb3C,EADqB2C,EAAO0E,0BAA0BrD,GAAa1E,GACzBvB,aAGnCwO,GAAuB5J,GAGrCA,EAAOG,OAAS,SAEhB,IAAMJ,EAASC,EAAOE,aAEPhD,IAAX6C,IAIAgE,GAAiChE,KACnCA,EAAOuD,cAAc0P,SAAQ,SAAA3P,GAC3BA,EAAYM,iBAEd5D,EAAOuD,cAAgB,IAAIzE,GAG7BwB,EAAkCN,aAGpBwN,GAAuBvN,EAA2BuE,GAIhEvE,EAAOG,OAAS,UAChBH,EAAOQ,aAAe+D,EAEtB,IAAMxE,EAASC,EAAOE,aAEPhD,IAAX6C,IAIAgE,GAAiChE,IACnCA,EAAOuD,cAAc0P,SAAQ,SAAA3P,GAC3BA,EAAYiB,YAAYC,MAG1BxE,EAAOuD,cAAgB,IAAIzE,IAI3BkB,EAAO0L,kBAAkBuH,SAAQ,SAAAxH,GAC/BA,EAAgBlH,YAAYC,MAG9BxE,EAAO0L,kBAAoB,IAAI5M,GAGjC+B,EAAiCb,EAAQwE,IAsB3C,SAASyM,GAA0BjQ,GACjC,OAAO,IAAI9C,UAAU,4BAA4B8C,oDI1gBnC4b,GAA2BpN,EACArN,GACzCF,EAAiBuN,EAAMrN,GACvB,IAAMyL,EAAgB4B,MAAAA,SAAAA,EAAM5B,cAE5B,OADAnL,EAAoBmL,EAAe,gBAAiB,uBAC7C,CACLA,cAAejL,EAA0BiL,IJmT7C/O,OAAO+F,iBAAiBqW,GAAejf,UAAW,CAChD6I,OAAQ,CAAEC,YAAY,GACtB+X,UAAW,CAAE/X,YAAY,GACzBgY,YAAa,CAAEhY,YAAY,GAC3BiY,OAAQ,CAAEjY,YAAY,GACtBkY,IAAK,CAAElY,YAAY,GACnBmY,OAAQ,CAAEnY,YAAY,GACtB6O,OAAQ,CAAE7O,YAAY,KAEU,iBAAvB5J,EAAOgK,aAChBrG,OAAOsG,eAAe8V,GAAejf,UAAWd,EAAOgK,YAAa,CAClExI,MAAO,iBACP0I,cAAc,IAGkB,iBAAzBlK,EAAOmK,eAChBxG,OAAOsG,eAAe8V,GAAejf,UAAWd,EAAOmK,cAAe,CACpE3I,MAAOue,GAAejf,UAAUihB,OAChC7F,UAAU,EACVhS,cAAc,IK1UlB,IAAM8X,GAAyB,SAAShW,KAAKzD,GAC3C,OAAOA,EAAM4E,0BAYb,mCAAYmS,GACVjY,EAAuBiY,EAAS,EAAG,6BACnCA,EAAUoC,GAA2BpC,EAAS,mBAC9Clc,KAAK6e,wCAA0C3C,EAAQ5M,cAsB3D,OAhBE/O,sBAAIue,yDAAJ,WACE,IAAKC,GAA4B/e,MAC/B,MAAMgf,GAA8B,iBAEtC,OAAOhf,KAAK6e,yEAMdte,sBAAIue,gDAAJ,WACE,IAAKC,GAA4B/e,MAC/B,MAAMgf,GAA8B,QAEtC,OAAOJ,iEAiBX,SAASI,GAA8Btc,GACrC,OAAO,IAAI9C,UAAU,uCAAuC8C,+DAG9Cqc,GAA4B1hB,GAC1C,QAAKD,EAAaC,MAIbkD,OAAO7C,UAAUyI,eAAexH,KAAKtB,EAAG,2CAtB/CkD,OAAO+F,iBAAiBwY,GAA0BphB,UAAW,CAC3D4R,cAAe,CAAE9I,YAAY,GAC7BoC,KAAM,CAAEpC,YAAY,KAEY,iBAAvB5J,EAAOgK,aAChBrG,OAAOsG,eAAeiY,GAA0BphB,UAAWd,EAAOgK,YAAa,CAC7ExI,MAAO,4BACP0I,cAAc,IC/ClB,IAAMmY,GAAoB,SAASrW,OACjC,OAAO,iBAYP,8BAAYsT,GACVjY,EAAuBiY,EAAS,EAAG,wBACnCA,EAAUoC,GAA2BpC,EAAS,mBAC9Clc,KAAKkf,mCAAqChD,EAAQ5M,cAuBtD,OAjBE/O,sBAAI4e,oDAAJ,WACE,IAAKC,GAAuBpf,MAC1B,MAAMqf,GAAyB,iBAEjC,OAAOrf,KAAKkf,oEAOd3e,sBAAI4e,2CAAJ,WACE,IAAKC,GAAuBpf,MAC1B,MAAMqf,GAAyB,QAEjC,OAAOJ,4DAiBX,SAASI,GAAyB3c,GAChC,OAAO,IAAI9C,UAAU,kCAAkC8C,0DAGzC0c,GAAuB/hB,GACrC,QAAKD,EAAaC,MAIbkD,OAAO7C,UAAUyI,eAAexH,KAAKtB,EAAG,sCClC/C,SAASiiB,GACP/f,EACA8R,EACAxN,GAGA,OADAC,EAAevE,EAAIsE,GACZ,SAAC4F,GAAoD,OAAA1J,EAAYR,EAAI8R,EAAU,CAAC5H,KAGzF,SAAS8V,GACPhgB,EACA8R,EACAxN,GAGA,OADAC,EAAevE,EAAIsE,GACZ,SAAC4F,GAAoD,OAAAjK,EAAYD,EAAI8R,EAAU,CAAC5H,KAGzF,SAAS+V,GACPjgB,EACA8R,EACAxN,GAGA,OADAC,EAAevE,EAAIsE,GACZ,SAACsB,EAAUsE,GAAoD,OAAA1J,EAAYR,EAAI8R,EAAU,CAAClM,EAAOsE,KDZ1GlJ,OAAO+F,iBAAiB6Y,GAAqBzhB,UAAW,CACtD4R,cAAe,CAAE9I,YAAY,GAC7BoC,KAAM,CAAEpC,YAAY,KAEY,iBAAvB5J,EAAOgK,aAChBrG,OAAOsG,eAAesY,GAAqBzhB,UAAWd,EAAOgK,YAAa,CACxExI,MAAO,uBACP0I,cAAc,sBEEhB,yBAAY2Y,EACAC,EACAC,gBAFAF,mBACAC,mBACAC,WACa9gB,IAAnB4gB,IACFA,EAAiB,MAGnB,IAAMG,EAAmB3O,GAAuByO,EAAqB,oBAC/DG,EAAmB5O,GAAuB0O,EAAqB,mBAE/DG,WDtD+BzO,EACAxN,GACvCF,EAAiB0N,EAAUxN,GAC3B,IAAMkc,EAAQ1O,MAAAA,SAAAA,EAAU0O,MAClBC,EAAe3O,MAAAA,SAAAA,EAAU2O,aACzBtQ,EAAQ2B,MAAAA,SAAAA,EAAU3B,MAClBqN,EAAY1L,MAAAA,SAAAA,EAAU0L,UACtBkD,EAAe5O,MAAAA,SAAAA,EAAU4O,aAC/B,MAAO,CACLF,WAAiBlhB,IAAVkhB,OACLlhB,EACAygB,GAAgCS,EAAO1O,EAAcxN,8BACvDmc,eACAtQ,WAAiB7Q,IAAV6Q,OACL7Q,EACA0gB,GAAgC7P,EAAO2B,EAAcxN,8BACvDkZ,eAAyBle,IAAdke,OACTle,EACA2gB,GAAoCzC,EAAW1L,EAAcxN,kCAC/Doc,gBCmCoBC,CAAmBT,EAAgB,mBACvD,QAAiC5gB,IAA7BihB,EAAYE,aACd,MAAM,IAAIlX,WAAW,kCAEvB,QAAiCjK,IAA7BihB,EAAYG,aACd,MAAM,IAAInX,WAAW,kCAGvB,IAKIqX,EALEC,EAAwBvP,GAAqBgP,EAAkB,GAC/DQ,EAAwBrP,GAAqB6O,GAC7CS,EAAwBzP,GAAqB+O,EAAkB,GAC/DW,EAAwBvP,GAAqB4O,IA2FvD,SAAyCje,EACA6e,EACAF,EACAC,EACAH,EACAC,GACvC,SAAS9Q,IACP,OAAOiR,EAeT7e,EAAO8e,UjB2BT,SAAiClR,EACA8C,EACAC,EACAC,EACAjD,EACA6C,gBADA7C,kBACA6C,aAAsD,OAAA,IAGrF,IAAMxQ,EAA4BpB,OAAOuK,OAAO4H,GAAehV,WAO/D,OANAwU,GAAyBvQ,GAIzB6Q,GAAqC7Q,EAFkBpB,OAAOuK,OAAOsH,GAAgC1U,WAE5C6R,EAAgB8C,EAAgBC,EACpDC,EAAgBjD,EAAe6C,GAC7DxQ,EiB1CY+e,CAAqBnR,GAZxC,SAAwBpK,GACtB,OA8QJ,SAAwDxD,EAA+BwD,GAGrF,IAAMsE,EAAa9H,EAAOgf,2BAE1B,GAAIhf,EAAO8R,cAAe,CAGxB,OAAOzU,EAF2B2C,EAAOif,4BAEc,WACrD,IAAM9H,EAAWnX,EAAO8e,UAExB,GAAc,aADA3H,EAAShX,OAErB,MAAMgX,EAAS3W,aAGjB,OAAO0e,GAAuDpX,EAAYtE,MAI9E,OAAO0b,GAAuDpX,EAAYtE,GAjSjE2b,CAAyCnf,EAAQwD,MAO1D,WACE,OAmSJ,SAAwDxD,GAEtD,IAAMqb,EAAWrb,EAAOof,UAElBtX,EAAa9H,EAAOgf,2BACpBK,EAAevX,EAAWwX,kBAIhC,OAHAC,GAAgDzX,GAGzCzK,EAAqBgiB,GAAc,WACxC,GAAwB,YAApBhE,EAASlb,OACX,MAAMkb,EAAS7a,aAEjBgZ,GAAqC6B,EAAS3W,8BAC7C,SAAAuJ,GAED,MADAuR,GAAqBxf,EAAQiO,GACvBoN,EAAS7a,gBAnTRif,CAAyCzf,MALlD,SAAwBrD,GACtB,OAgSJ,SAAkDqD,EAAyBrD,GAIzE,OADA6iB,GAAqBxf,EAAQrD,GACtBH,OAAoBU,GApSlBwiB,CAAyC1f,EAAQrD,KAQlBgiB,EAAuBC,GAW/D5e,EAAOof,UAAY/C,GAAqBzO,GATxC,WACE,OAkTJ,SAAmD5N,GASjD,OAHA2f,GAA+B3f,GAAQ,GAGhCA,EAAOif,2BA3TLW,CAA0C5f,MAGnD,SAAyBrD,GAEvB,OADAkjB,GAA4C7f,EAAQrD,GAC7CH,OAAoBU,KAG2DuhB,EAChDC,GAGxC1e,EAAO8R,mBAAgB5U,EACvB8C,EAAOif,gCAA6B/hB,EACpC8C,EAAO8f,wCAAqC5iB,EAC5CyiB,GAA+B3f,GAAQ,GAEvCA,EAAOgf,gCAA6B9hB,EA/HlC6iB,CACE1hB,KALmB/B,GAAiB,SAAAJ,GACpCsiB,EAAuBtiB,KAIHyiB,EAAuBC,EAAuBH,EAAuBC,GA2R/F,SAAoE1e,EACAme,GAClE,IAAMrW,EAAkDlJ,OAAOuK,OAAO6W,GAAiCjkB,WAEnGkkB,EAAqB,SAACzc,GACxB,IAEE,OADA0c,GAAwCpY,EAAYtE,GAC7ChH,OAAoBU,GAC3B,MAAOijB,GACP,OAAOzjB,EAAoByjB,KAI3BC,EAAsC,WAAM,OAAA5jB,OAAoBU,SAEtCA,IAA1BihB,EAAY/C,YACd6E,EAAqB,SAAAzc,GAAS,OAAA2a,EAAY/C,UAAW5X,EAAOsE,UAEpC5K,IAAtBihB,EAAYC,QACdgC,EAAiB,WAAM,OAAAjC,EAAYC,MAAOtW,MAjC9C,SAAqD9H,EACA8H,EACAmY,EACAG,GAInDtY,EAAWuY,2BAA6BrgB,EACxCA,EAAOgf,2BAA6BlX,EAEpCA,EAAWwY,oBAAsBL,EACjCnY,EAAWwX,gBAAkBc,EAyB7BG,CAAsCvgB,EAAQ8H,EAAYmY,EAAoBG,GA/S5EI,CAAqDniB,KAAM8f,QAEjCjhB,IAAtBihB,EAAYpQ,MACdyQ,EAAqBL,EAAYpQ,MAAM1P,KAAK2gB,6BAE5CR,OAAqBthB,GAyB3B,OAlBE0B,sBAAI6hB,0CAAJ,WACE,IAAKC,GAAkBriB,MACrB,MAAM2S,GAA0B,YAGlC,OAAO3S,KAAK+gB,2CAMdxgB,sBAAI6hB,0CAAJ,WACE,IAAKC,GAAkBriB,MACrB,MAAM2S,GAA0B,YAGlC,OAAO3S,KAAKygB,8DAmGhB,SAAS4B,GAAkBhlB,GACzB,QAAKD,EAAaC,MAIbkD,OAAO7C,UAAUyI,eAAexH,KAAKtB,EAAG,8BAQ/C,SAAS8jB,GAAqBxf,EAAyBuE,GACrDmV,GAAqC1Z,EAAOof,UAAU1a,0BACjBH,GACrCsb,GAA4C7f,EAAQuE,GAGtD,SAASsb,GAA4C7f,EAAyBuE,GAC5Egb,GAAgDvf,EAAOgf,4BACvD3J,GAA6CrV,EAAO8e,UAAUtN,0BAA2BjN,GACrFvE,EAAO8R,eAIT6N,GAA+B3f,GAAQ,GAI3C,SAAS2f,GAA+B3f,EAAyBsT,QAIrBpW,IAAtC8C,EAAOif,4BACTjf,EAAO8f,qCAGT9f,EAAOif,2BAA6B3iB,GAAW,SAAAJ,GAC7C8D,EAAO8f,mCAAqC5jB,KAG9C8D,EAAO8R,cAAgBwB,EAzIzB1U,OAAO+F,iBAAiB8b,GAAgB1kB,UAAW,CACjDsf,SAAU,CAAExW,YAAY,GACxBsS,SAAU,CAAEtS,YAAY,KAEQ,iBAAvB5J,EAAOgK,aAChBrG,OAAOsG,eAAeub,GAAgB1kB,UAAWd,EAAOgK,YAAa,CACnExI,MAAO,kBACP0I,cAAc,sBAoJhB,4CACE,MAAM,IAAIlH,UAAU,uBAkDxB,OA5CEW,sBAAIohB,8DAAJ,WACE,IAAKW,GAAmCtiB,MACtC,MAAMgb,GAAqC,eAI7C,OAAOC,GADoBjb,KAAKgiB,2BAA2BjB,UAAU1a,4DAQvEsb,mDAAA,SAAQxc,GACN,gBADMA,OAAWtG,IACZyjB,GAAmCtiB,MACtC,MAAMgb,GAAqC,WAG7C6G,GAAwC7hB,KAAMmF,IAOhDwc,iDAAA,SAAMrjB,GACJ,gBADIA,WACCgkB,GAAmCtiB,MACtC,MAAMgb,GAAqC,SAoHjD,IAAkG9U,EAAAA,EAjHlD5H,EAkH9C6iB,GAlHwCnhB,KAkHRgiB,2BAA4B9b,IA3G5Dyb,qDAAA,WACE,IAAKW,GAAmCtiB,MACtC,MAAMgb,GAAqC,cAqHjD,SAAsDvR,GACpD,IAAM9H,EAAS8H,EAAWuY,2BAG1B7G,GAF2BxZ,EAAOof,UAAU1a,2BAI5C,IAAM6J,EAAQ,IAAItQ,UAAU,8BAC5B4hB,GAA4C7f,EAAQuO,GAzHlDqS,CAA0CviB,2CAmB9C,SAASsiB,GAA4CjlB,GACnD,QAAKD,EAAaC,MAIbkD,OAAO7C,UAAUyI,eAAexH,KAAKtB,EAAG,8BA8C/C,SAAS6jB,GAAgDzX,GACvDA,EAAWwY,yBAAsBpjB,EACjC4K,EAAWwX,qBAAkBpiB,EAG/B,SAASgjB,GAA2CpY,EAAiDtE,GACnG,IAAMxD,EAAS8H,EAAWuY,2BACpBQ,EAAqB7gB,EAAOof,UAAU1a,0BAC5C,IAAK6U,GAAiDsH,GACpD,MAAM,IAAI5iB,UAAU,wDAMtB,IACEwb,GAAuCoH,EAAoBrd,GAC3D,MAAOe,GAIP,MAFAsb,GAA4C7f,EAAQuE,GAE9CvE,EAAOof,UAAU5e,uBbrHoCsH,GAC7D,OAAIgS,GAA8ChS,IauH7BgZ,CAA+CD,KAC/C7gB,EAAO8R,eAE1B6N,GAA+B3f,GAAQ,GAQ3C,SAASkf,GAAuDpX,EACAtE,GAE9D,OAAOnG,EADkByK,EAAWwY,oBAAoB9c,QACVtG,GAAW,SAAA+Q,GAEvD,MADAuR,GAAqB1X,EAAWuY,2BAA4BpS,GACtDA,KAiFV,SAASoL,GAAqCtY,GAC5C,OAAO,IAAI9C,UACT,8CAA8C8C,6DAKlD,SAASiQ,GAA0BjQ,GACjC,OAAO,IAAI9C,UACT,6BAA6B8C,4CApMjCnC,OAAO+F,iBAAiBqb,GAAiCjkB,UAAW,CAClEuS,QAAS,CAAEzJ,YAAY,GACvB0J,MAAO,CAAE1J,YAAY,GACrBkc,UAAW,CAAElc,YAAY,GACzB2J,YAAa,CAAE3J,YAAY,KAEK,iBAAvB5J,EAAOgK,aAChBrG,OAAOsG,eAAe8a,GAAiCjkB,UAAWd,EAAOgK,YAAa,CACpFxI,MAAO,mCACP0I,cAAc,IC3TlB,IAAM6b,GAAU,CACdhG,kBACA7B,mCACAtQ,gCACAtB,6BACApE,8BACAsL,4BAEAsC,kBACAN,mCACAa,+BAEA6L,6BACAK,wBAEAiD,mBACAT,qCAIF,QAAuB,IAAZ3kB,EACT,IAAK,IAAM4lB,MAAQD,GACbpiB,OAAO7C,UAAUyI,eAAexH,KAAKgkB,GAASC,KAChDriB,OAAOsG,eAAe7J,EAAS4lB,GAAM,CACnCxkB,MAAOukB,GAAQC,IACf9J,UAAU,EACVhS,cAAc"}