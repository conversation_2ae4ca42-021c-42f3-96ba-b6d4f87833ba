p2p is a sample of how to use vdo.ninja as a data transport tunneling service

twitch is an example of how to have a twitch live chat side-by-side with VDO.NInja on the same screen

dual is an example of how to have two VDO.Ninja windows (or any windows really) open on the same page; Picture-in-Picture style

sensors is an example of how to transmit sensor and video data from a phone to a computer, drawing it to canvas: youtube video for this exists

midi demonstrates the MIDI API for VDO.Ninja

draggable demonstrates how to drag multiple windows around, if you wanted to create a custom layout of elements. (experimental)

chat.html is an example of a chat-only interface for VDO.NInja; maybe dockable into OBS even

iframe.outbound-stats.html demostrates how to get stats from VDO.<PERSON> using the IFRAME API

changepass lets you create passwords and related HASH values for VDO.NInja rooms

webhid demonstrates how to interface with a USB device, like a streamdeck (mouse/keyboard not supported)

zoom.html is a tool for letting you publish into VDO.Ninja, but then full-screen the window once setup, allowing for window-capturing into zoom.

obs_remote is also hosted on github elsewhere, but it's an example of how to remotely control OBS using VDO.<PERSON>'s tunneling abilities
