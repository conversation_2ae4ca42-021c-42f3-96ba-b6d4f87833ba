<html>
<head><title>overlay + Video</title>
<meta name="viewport" content="width=device-width, initial-scale=0.7, maximum-scale=1.0, user-scalable=yes" />
<meta content="text/html;charset=utf-8" http-equiv="Content-Type" />
<style>
body{
	padding:0;
	margin:0;
	background-color:#003;
	width:100%;
	height:100%;
	color:white;
}

iframe {
	width:100vw;
	height:100vh;
	border:0;
	margin:0;
	padding:0;
	position:fixed;
	top:0;
	left:0
}


input{
	padding:10px;
	width:80%;
	font-size:1.2em;
	z-index: 1000;
}

h1{
	color: white;
    font-family: verdana;
    margin: 10px;
}


</style>
</head>
<body>


<div id="clean">
	<h1>Apply an Overlay to VDO.Ninja</h1>
	<input placeholder="Enter a VDON URL here" id="viewlink" type="text" />
	<input placeholder="Enter the Overlay page here" id="overlay" type="text" />
	<button onclick="loadIframes()" style="display:block;padding:10px;margin:10px;">START</button>(Leave blank and press start to see a default sample result)
</div>
<script>

window.addEventListener("orientationchange", function() {
  // Announce the new orientation number
 // alert(window.orientation);
}, false);

function removeStorage(cname){
	localStorage.removeItem(cname);
}

function setStorage(cname, cvalue, hours=9999){ // not actually a cookie
	var now = new Date();
	var item = {
		value: cvalue,
		expiry: now.getTime() + (hours * 60 * 60 * 1000),
	};
	try{
		localStorage.setItem(cname, JSON.stringify(item));
	}catch(e){errorlog(e);}
}

function getStorage(cname) {
	try {
		var itemStr = localStorage.getItem(cname);
	} catch(e){
		errorlog(e);
		return;
	}
	if (!itemStr) {
		return "";
	}
	var item = JSON.parse(itemStr);
	var now = new Date();
	if (now.getTime() > item.expiry) {
		localStorage.removeItem(cname);
		return "";
	}
	return item.value;
}
if (getStorage("overlayChatLink")){
	document.getElementById("overlay").value = getStorage("overlayChatLink");
}
if (getStorage("vdoNinjaoverlayURL")){
	document.getElementById("viewlink").value = getStorage("vdoNinjaoverlayURL");
}

function loadIframes(url=false){ 

	var roomname = document.getElementById("viewlink").value;
	var overlay = document.getElementById("overlay").value;
	
	document.getElementById("clean").parentNode.removeChild(document.getElementById("clean"));

	if (!roomname){
		var room1 = "../";
	} else if (roomname.startsWith("https://")){
		var room1 = roomname;
	} else {
		var room1 = "https://"+roomname;
	}
	
	var iframe = document.createElement("iframe");
	iframe.allow = "document-domain;encrypted-media;sync-xhr;usb;web-share;cross-origin-isolated;accelerometer;midi;geolocation;autoplay;camera;microphone;fullscreen;picture-in-picture;display-capture;";
	iframe.src = room1;
	document.body.appendChild(iframe);
	
	
	if (!overlay){
		var room2 = "./test_overlay";
	} else if (overlay.startsWith("https://")){
		var room2 = overlay;
	} else {
		var room2 = "https://"+overlay;
	}
	
	var iframe = document.createElement("iframe");
	iframe.allow = "document-domain;encrypted-media;sync-xhr;usb;web-share;cross-origin-isolated;accelerometer;midi;geolocation;autoplay;camera;microphone;fullscreen;picture-in-picture;display-capture;";
	iframe.src = room2;
	iframe.style.pointerEvents = "none";
	iframe.style.backgroundColor = "#0000";
	iframe.style.width = "25vw";
	iframe.style.height = "25vh";
	iframe.style.overflow = "hidden";
	document.body.appendChild(iframe);

	if (roomname && overlay){
		setStorage("overlayChatLink", room2);
		setStorage("vdoNinjaoverlayURL", room1);
	}
}

</script>
</body>
</html>