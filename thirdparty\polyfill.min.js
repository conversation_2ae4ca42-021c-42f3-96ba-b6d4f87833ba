// The MIT License (MIT)
// web-streams-polyfill
// https://github.com/MattiasBuelens/web-streams-polyfill
// https://raw.githubusercontent.com/MattiasBuelens/web-streams-polyfill/master/LICENSE
//# sourceMappingURL=polyfill.min.js.map
!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports):"function"==typeof define&&define.amd?define(["exports"],r):r((e=e||self).WebStreamsPolyfill={})}(this,(function(e){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol:function(e){return"Symbol("+e+")"};function t(){}var o="undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:void 0;function n(e){return"object"==typeof e&&null!==e||"function"==typeof e}var i=t,a=Promise,u=Promise.prototype.then,l=Promise.resolve.bind(a),s=Promise.reject.bind(a);function c(e){return new a(e)}function d(e){return l(e)}function f(e){return s(e)}function b(e,r,t){return u.call(e,r,t)}function p(e,r,t){b(b(e,r,t),void 0,i)}function h(e,r){p(e,r)}function _(e,r){p(e,void 0,r)}function m(e,r,t){return b(e,r,t)}function y(e){b(e,void 0,i)}var v=function(){var e=o&&o.queueMicrotask;if("function"==typeof e)return e;var r=d(void 0);return function(e){return b(r,e)}}();function g(e,r,t){if("function"!=typeof e)throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,r,t)}function S(e,r,t){try{return d(g(e,r,t))}catch(e){return f(e)}}var w=function(){function e(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}return Object.defineProperty(e.prototype,"length",{get:function(){return this._size},enumerable:!1,configurable:!0}),e.prototype.push=function(e){var r=this._back,t=r;16383===r._elements.length&&(t={_elements:[],_next:void 0}),r._elements.push(e),t!==r&&(this._back=t,r._next=t),++this._size},e.prototype.shift=function(){var e=this._front,r=e,t=this._cursor,o=t+1,n=e._elements,i=n[t];return 16384===o&&(r=e._next,o=0),--this._size,this._cursor=o,e!==r&&(this._front=r),n[t]=void 0,i},e.prototype.forEach=function(e){for(var r=this._cursor,t=this._front,o=t._elements;!(r===o.length&&void 0===t._next||r===o.length&&(r=0,0===(o=(t=t._next)._elements).length));)e(o[r]),++r},e.prototype.peek=function(){var e=this._front,r=this._cursor;return e._elements[r]},e}();function R(e,r){e._ownerReadableStream=r,r._reader=e,"readable"===r._state?C(e):"closed"===r._state?function(e){C(e),W(e)}(e):E(e,r._storedError)}function T(e,r){return st(e._ownerReadableStream,r)}function P(e){"readable"===e._ownerReadableStream._state?O(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):function(e,r){E(e,r)}(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),e._ownerReadableStream._reader=void 0,e._ownerReadableStream=void 0}function q(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function C(e){e._closedPromise=c((function(r,t){e._closedPromise_resolve=r,e._closedPromise_reject=t}))}function E(e,r){C(e),O(e,r)}function O(e,r){void 0!==e._closedPromise_reject&&(y(e._closedPromise),e._closedPromise_reject(r),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function W(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}var j=r("[[AbortSteps]]"),B=r("[[ErrorSteps]]"),k=r("[[CancelSteps]]"),A=r("[[PullSteps]]"),z=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},D=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function F(e,r){if(void 0!==e&&("object"!=typeof(t=e)&&"function"!=typeof t))throw new TypeError(r+" is not an object.");var t}function L(e,r){if("function"!=typeof e)throw new TypeError(r+" is not a function.")}function I(e,r){if(!function(e){return"object"==typeof e&&null!==e||"function"==typeof e}(e))throw new TypeError(r+" is not an object.")}function M(e,r,t){if(void 0===e)throw new TypeError("Parameter "+r+" is required in '"+t+"'.")}function Q(e,r,t){if(void 0===e)throw new TypeError(r+" is required in '"+t+"'.")}function Y(e){return Number(e)}function x(e){return 0===e?0:e}function N(e,r){var t=Number.MAX_SAFE_INTEGER,o=Number(e);if(o=x(o),!z(o))throw new TypeError(r+" is not a finite number");if((o=function(e){return x(D(e))}(o))<0||o>t)throw new TypeError(r+" is outside the accepted range of 0 to "+t+", inclusive");return z(o)&&0!==o?o:0}function H(e,r){if(!ut(e))throw new TypeError(r+" is not a ReadableStream.")}function V(e){return new $(e)}function U(e,r){e._reader._readRequests.push(r)}function G(e,r,t){var o=e._reader._readRequests.shift();t?o._closeSteps():o._chunkSteps(r)}function X(e){return e._reader._readRequests.length}function J(e){var r=e._reader;return void 0!==r&&!!ee(r)}var K,Z,$=function(){function ReadableStreamDefaultReader(e){if(M(e,1,"ReadableStreamDefaultReader"),H(e,"First parameter"),lt(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");R(this,e),this._readRequests=new w}return Object.defineProperty(ReadableStreamDefaultReader.prototype,"closed",{get:function(){return ee(this)?this._closedPromise:f(te("closed"))},enumerable:!1,configurable:!0}),ReadableStreamDefaultReader.prototype.cancel=function(e){return void 0===e&&(e=void 0),ee(this)?void 0===this._ownerReadableStream?f(q("cancel")):T(this,e):f(te("cancel"))},ReadableStreamDefaultReader.prototype.read=function(){if(!ee(this))return f(te("read"));if(void 0===this._ownerReadableStream)return f(q("read from"));var e,r,t=c((function(t,o){e=t,r=o}));return re(this,{_chunkSteps:function(r){return e({value:r,done:!1})},_closeSteps:function(){return e({value:void 0,done:!0})},_errorSteps:function(e){return r(e)}}),t},ReadableStreamDefaultReader.prototype.releaseLock=function(){if(!ee(this))throw te("releaseLock");if(void 0!==this._ownerReadableStream){if(this._readRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");P(this)}},ReadableStreamDefaultReader}();function ee(e){return!!n(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readRequests")}function re(e,r){var t=e._ownerReadableStream;t._disturbed=!0,"closed"===t._state?r._closeSteps():"errored"===t._state?r._errorSteps(t._storedError):t._readableStreamController[A](r)}function te(e){return new TypeError("ReadableStreamDefaultReader.prototype."+e+" can only be used on a ReadableStreamDefaultReader")}Object.defineProperties($.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty($.prototype,r.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0}),"symbol"==typeof r.asyncIterator&&((K={})[r.asyncIterator]=function(){return this},Z=K,Object.defineProperty(Z,r.asyncIterator,{enumerable:!1}));var oe=function(){function e(e,r){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=r}return e.prototype.next=function(){var e=this,r=function(){return e._nextSteps()};return this._ongoingPromise=this._ongoingPromise?m(this._ongoingPromise,r,r):r(),this._ongoingPromise},e.prototype.return=function(e){var r=this,t=function(){return r._returnSteps(e)};return this._ongoingPromise?m(this._ongoingPromise,t,t):t()},e.prototype._nextSteps=function(){var e=this;if(this._isFinished)return Promise.resolve({value:void 0,done:!0});var r,t,o=this._reader;if(void 0===o._ownerReadableStream)return f(q("iterate"));var n=c((function(e,o){r=e,t=o}));return re(o,{_chunkSteps:function(t){e._ongoingPromise=void 0,v((function(){return r({value:t,done:!1})}))},_closeSteps:function(){e._ongoingPromise=void 0,e._isFinished=!0,P(o),r({value:void 0,done:!0})},_errorSteps:function(r){e._ongoingPromise=void 0,e._isFinished=!0,P(o),t(r)}}),n},e.prototype._returnSteps=function(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;var r=this._reader;if(void 0===r._ownerReadableStream)return f(q("finish iterating"));if(!this._preventCancel){var t=T(r,e);return P(r),m(t,(function(){return{value:e,done:!0}}))}return P(r),d({value:e,done:!0})},e}(),ne={next:function(){return ie(this)?this._asyncIteratorImpl.next():f(ae("next"))},return:function(e){return ie(this)?this._asyncIteratorImpl.return(e):f(ae("return"))}};function ie(e){return!!n(e)&&!!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl")}function ae(e){return new TypeError("ReadableStreamAsyncIterator."+e+" can only be used on a ReadableSteamAsyncIterator")}void 0!==Z&&Object.setPrototypeOf(ne,Z);var ue=Number.isNaN||function(e){return e!=e};function le(e){return!!function(e){if("number"!=typeof e)return!1;if(ue(e))return!1;if(e<0)return!1;return!0}(e)&&e!==1/0}function se(e){var r=e._queue.shift();return e._queueTotalSize-=r.size,e._queueTotalSize<0&&(e._queueTotalSize=0),r.value}function ce(e,r,t){if(!le(t=Number(t)))throw new RangeError("Size must be a finite, non-NaN, non-negative number.");e._queue.push({value:r,size:t}),e._queueTotalSize+=t}function de(e){e._queue=new w,e._queueTotalSize=0}function fe(e){return e.slice()}var be=function(){function ReadableStreamBYOBRequest(){throw new TypeError("Illegal constructor")}return Object.defineProperty(ReadableStreamBYOBRequest.prototype,"view",{get:function(){if(!_e(this))throw Be("view");return this._view},enumerable:!1,configurable:!0}),ReadableStreamBYOBRequest.prototype.respond=function(e){if(!_e(this))throw Be("respond");if(M(e,1,"respond"),e=N(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");this._view.buffer,function(e,r){if(!le(r=Number(r)))throw new RangeError("bytesWritten must be a finite");qe(e,r)}(this._associatedReadableByteStreamController,e)},ReadableStreamBYOBRequest.prototype.respondWithNewView=function(e){if(!_e(this))throw Be("respondWithNewView");if(M(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw new TypeError("You can only respond with array buffer views");if(0===e.byteLength)throw new TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw new TypeError("chunk's buffer must have non-zero byteLength");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");!function(e,r){var t=e._pendingPullIntos.peek();if(t.byteOffset+t.bytesFilled!==r.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(t.byteLength!==r.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");t.buffer=r.buffer,qe(e,r.byteLength)}(this._associatedReadableByteStreamController,e)},ReadableStreamBYOBRequest}();Object.defineProperties(be.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(be.prototype,r.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});var pe=function(){function ReadableByteStreamController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(ReadableByteStreamController.prototype,"byobRequest",{get:function(){if(!he(this))throw ke("byobRequest");if(null===this._byobRequest&&this._pendingPullIntos.length>0){var e=this._pendingPullIntos.peek(),r=new Uint8Array(e.buffer,e.byteOffset+e.bytesFilled,e.byteLength-e.bytesFilled),t=Object.create(be.prototype);!function(e,r,t){e._associatedReadableByteStreamController=r,e._view=t}(t,this,r),this._byobRequest=t}return this._byobRequest},enumerable:!1,configurable:!0}),Object.defineProperty(ReadableByteStreamController.prototype,"desiredSize",{get:function(){if(!he(this))throw ke("desiredSize");return We(this)},enumerable:!1,configurable:!0}),ReadableByteStreamController.prototype.close=function(){if(!he(this))throw ke("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");var e=this._controlledReadableByteStream._state;if("readable"!==e)throw new TypeError("The stream (in "+e+" state) is not in the readable state and cannot be closed");!function(e){var r=e._controlledReadableByteStream;if(e._closeRequested||"readable"!==r._state)return;if(e._queueTotalSize>0)return void(e._closeRequested=!0);if(e._pendingPullIntos.length>0){if(e._pendingPullIntos.peek().bytesFilled>0){var t=new TypeError("Insufficient bytes to fill elements in the given buffer");throw Oe(e,t),t}}Ee(e),ct(r)}(this)},ReadableByteStreamController.prototype.enqueue=function(e){if(!he(this))throw ke("enqueue");if(M(e,1,"enqueue"),!ArrayBuffer.isView(e))throw new TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw new TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");var r=this._controlledReadableByteStream._state;if("readable"!==r)throw new TypeError("The stream (in "+r+" state) is not in the readable state and cannot be enqueued to");!function(e,r){var t=e._controlledReadableByteStream;if(e._closeRequested||"readable"!==t._state)return;var o=r.buffer,n=r.byteOffset,i=r.byteLength,a=o;if(J(t))if(0===X(t))ge(e,a,n,i);else{var u=new Uint8Array(a,n,i);G(t,u,!1)}else De(t)?(ge(e,a,n,i),Pe(e)):ge(e,a,n,i);me(e)}(this,e)},ReadableByteStreamController.prototype.error=function(e){if(void 0===e&&(e=void 0),!he(this))throw ke("error");Oe(this,e)},ReadableByteStreamController.prototype[k]=function(e){this._pendingPullIntos.length>0&&(this._pendingPullIntos.peek().bytesFilled=0);de(this);var r=this._cancelAlgorithm(e);return Ee(this),r},ReadableByteStreamController.prototype[A]=function(e){var r=this._controlledReadableByteStream;if(this._queueTotalSize>0){var t=this._queue.shift();this._queueTotalSize-=t.byteLength,Re(this);var o=new Uint8Array(t.buffer,t.byteOffset,t.byteLength);e._chunkSteps(o)}else{var n=this._autoAllocateChunkSize;if(void 0!==n){var i=void 0;try{i=new ArrayBuffer(n)}catch(r){return void e._errorSteps(r)}var a={buffer:i,byteOffset:0,byteLength:n,bytesFilled:0,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(a)}U(r,e),me(this)}},ReadableByteStreamController}();function he(e){return!!n(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")}function _e(e){return!!n(e)&&!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")}function me(e){(function(e){var r=e._controlledReadableByteStream;if("readable"!==r._state)return!1;if(e._closeRequested)return!1;if(!e._started)return!1;if(J(r)&&X(r)>0)return!0;if(De(r)&&ze(r)>0)return!0;if(We(e)>0)return!0;return!1})(e)&&(e._pulling?e._pullAgain=!0:(e._pulling=!0,p(e._pullAlgorithm(),(function(){e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,me(e))}),(function(r){Oe(e,r)}))))}function ye(e,r){var t=!1;"closed"===e._state&&(t=!0);var o=ve(r);"default"===r.readerType?G(e,o,t):function(e,r,t){var o=e._reader._readIntoRequests.shift();t?o._closeSteps(r):o._chunkSteps(r)}(e,o,t)}function ve(e){var r=e.bytesFilled,t=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,r/t)}function ge(e,r,t,o){e._queue.push({buffer:r,byteOffset:t,byteLength:o}),e._queueTotalSize+=o}function Se(e,r){var t=r.elementSize,o=r.bytesFilled-r.bytesFilled%t,n=Math.min(e._queueTotalSize,r.byteLength-r.bytesFilled),i=r.bytesFilled+n,a=i-i%t,u=n,l=!1;a>o&&(u=a-r.bytesFilled,l=!0);for(var s,c,d,f,b,p=e._queue;u>0;){var h=p.peek(),_=Math.min(u,h.byteLength),m=r.byteOffset+r.bytesFilled;s=r.buffer,c=m,d=h.buffer,f=h.byteOffset,b=_,new Uint8Array(s).set(new Uint8Array(d,f,b),c),h.byteLength===_?p.shift():(h.byteOffset+=_,h.byteLength-=_),e._queueTotalSize-=_,we(e,_,r),u-=_}return l}function we(e,r,t){Te(e),t.bytesFilled+=r}function Re(e){0===e._queueTotalSize&&e._closeRequested?(Ee(e),ct(e._controlledReadableByteStream)):me(e)}function Te(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function Pe(e){for(;e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;var r=e._pendingPullIntos.peek();Se(e,r)&&(Ce(e),ye(e._controlledReadableByteStream,r))}}function qe(e,r){var t=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==r)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream");!function(e,r){r.buffer=r.buffer;var t=e._controlledReadableByteStream;if(De(t))for(;ze(t)>0;){ye(t,Ce(e))}}(e,t)}else!function(e,r,t){if(t.bytesFilled+r>t.byteLength)throw new RangeError("bytesWritten out of range");if(we(e,r,t),!(t.bytesFilled<t.elementSize)){Ce(e);var o=t.bytesFilled%t.elementSize;if(o>0){var n=t.byteOffset+t.bytesFilled,i=t.buffer.slice(n-o,n);ge(e,i,0,i.byteLength)}t.buffer=t.buffer,t.bytesFilled-=o,ye(e._controlledReadableByteStream,t),Pe(e)}}(e,r,t);me(e)}function Ce(e){var r=e._pendingPullIntos.shift();return Te(e),r}function Ee(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function Oe(e,r){var t=e._controlledReadableByteStream;"readable"===t._state&&(!function(e){Te(e),e._pendingPullIntos=new w}(e),de(e),Ee(e),dt(t,r))}function We(e){var r=e._controlledReadableByteStream._state;return"errored"===r?null:"closed"===r?0:e._strategyHWM-e._queueTotalSize}function je(e,r,t){var o=Object.create(pe.prototype),n=function(){},i=function(){return d(void 0)},a=function(){return d(void 0)};void 0!==r.start&&(n=function(){return r.start(o)}),void 0!==r.pull&&(i=function(){return r.pull(o)}),void 0!==r.cancel&&(a=function(e){return r.cancel(e)});var u=r.autoAllocateChunkSize;!function(e,r,t,o,n,i,a){r._controlledReadableByteStream=e,r._pullAgain=!1,r._pulling=!1,r._byobRequest=null,r._queue=r._queueTotalSize=void 0,de(r),r._closeRequested=!1,r._started=!1,r._strategyHWM=i,r._pullAlgorithm=o,r._cancelAlgorithm=n,r._autoAllocateChunkSize=a,r._pendingPullIntos=new w,e._readableStreamController=r,p(d(t()),(function(){r._started=!0,me(r)}),(function(e){Oe(r,e)}))}(e,o,n,i,a,t,u)}function Be(e){return new TypeError("ReadableStreamBYOBRequest.prototype."+e+" can only be used on a ReadableStreamBYOBRequest")}function ke(e){return new TypeError("ReadableByteStreamController.prototype."+e+" can only be used on a ReadableByteStreamController")}function Ae(e,r){e._reader._readIntoRequests.push(r)}function ze(e){return e._reader._readIntoRequests.length}function De(e){var r=e._reader;return void 0!==r&&!!Le(r)}Object.defineProperties(pe.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(pe.prototype,r.toStringTag,{value:"ReadableByteStreamController",configurable:!0});var Fe=function(){function ReadableStreamBYOBReader(e){if(M(e,1,"ReadableStreamBYOBReader"),H(e,"First parameter"),lt(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!he(e._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");R(this,e),this._readIntoRequests=new w}return Object.defineProperty(ReadableStreamBYOBReader.prototype,"closed",{get:function(){return Le(this)?this._closedPromise:f(Ie("closed"))},enumerable:!1,configurable:!0}),ReadableStreamBYOBReader.prototype.cancel=function(e){return void 0===e&&(e=void 0),Le(this)?void 0===this._ownerReadableStream?f(q("cancel")):T(this,e):f(Ie("cancel"))},ReadableStreamBYOBReader.prototype.read=function(e){if(!Le(this))return f(Ie("read"));if(!ArrayBuffer.isView(e))return f(new TypeError("view must be an array buffer view"));if(0===e.byteLength)return f(new TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return f(new TypeError("view's buffer must have non-zero byteLength"));if(void 0===this._ownerReadableStream)return f(q("read from"));var r,t,o=c((function(e,o){r=e,t=o}));return function(e,r,t){var o=e._ownerReadableStream;o._disturbed=!0,"errored"===o._state?t._errorSteps(o._storedError):function(e,r,t){var o=e._controlledReadableByteStream,n=1;r.constructor!==DataView&&(n=r.constructor.BYTES_PER_ELEMENT);var i=r.constructor,a={buffer:r.buffer,byteOffset:r.byteOffset,byteLength:r.byteLength,bytesFilled:0,elementSize:n,viewConstructor:i,readerType:"byob"};if(e._pendingPullIntos.length>0)return e._pendingPullIntos.push(a),void Ae(o,t);if("closed"!==o._state){if(e._queueTotalSize>0){if(Se(e,a)){var u=ve(a);return Re(e),void t._chunkSteps(u)}if(e._closeRequested){var l=new TypeError("Insufficient bytes to fill elements in the given buffer");return Oe(e,l),void t._errorSteps(l)}}e._pendingPullIntos.push(a),Ae(o,t),me(e)}else{var s=new i(a.buffer,a.byteOffset,0);t._closeSteps(s)}}(o._readableStreamController,r,t)}(this,e,{_chunkSteps:function(e){return r({value:e,done:!1})},_closeSteps:function(e){return r({value:e,done:!0})},_errorSteps:function(e){return t(e)}}),o},ReadableStreamBYOBReader.prototype.releaseLock=function(){if(!Le(this))throw Ie("releaseLock");if(void 0!==this._ownerReadableStream){if(this._readIntoRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");P(this)}},ReadableStreamBYOBReader}();function Le(e){return!!n(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")}function Ie(e){return new TypeError("ReadableStreamBYOBReader.prototype."+e+" can only be used on a ReadableStreamBYOBReader")}function Me(e,r){var t=e.highWaterMark;if(void 0===t)return r;if(ue(t)||t<0)throw new RangeError("Invalid highWaterMark");return t}function Qe(e){var r=e.size;return r||function(){return 1}}function Ye(e,r){F(e,r);var t=null==e?void 0:e.highWaterMark,o=null==e?void 0:e.size;return{highWaterMark:void 0===t?void 0:Y(t),size:void 0===o?void 0:xe(o,r+" has member 'size' that")}}function xe(e,r){return L(e,r),function(r){return Y(e(r))}}function Ne(e,r,t){return L(e,t),function(t){return S(e,r,[t])}}function He(e,r,t){return L(e,t),function(){return S(e,r,[])}}function Ve(e,r,t){return L(e,t),function(t){return g(e,r,[t])}}function Ue(e,r,t){return L(e,t),function(t,o){return S(e,r,[t,o])}}function Ge(e,r){if(!Ze(e))throw new TypeError(r+" is not a WritableStream.")}Object.defineProperties(Fe.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(Fe.prototype,r.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});var Xe=function(){function WritableStream(e,r){void 0===e&&(e={}),void 0===r&&(r={}),void 0===e?e=null:I(e,"First parameter");var t=Ye(r,"Second parameter"),o=function(e,r){F(e,r);var t=null==e?void 0:e.abort,o=null==e?void 0:e.close,n=null==e?void 0:e.start,i=null==e?void 0:e.type,a=null==e?void 0:e.write;return{abort:void 0===t?void 0:Ne(t,e,r+" has member 'abort' that"),close:void 0===o?void 0:He(o,e,r+" has member 'close' that"),start:void 0===n?void 0:Ve(n,e,r+" has member 'start' that"),write:void 0===a?void 0:Ue(a,e,r+" has member 'write' that"),type:i}}(e,"First parameter");if(Ke(this),void 0!==o.type)throw new RangeError("Invalid type is specified");var n=Qe(t);!function(e,r,t,o){var n=Object.create(_r.prototype),i=function(){},a=function(){return d(void 0)},u=function(){return d(void 0)},l=function(){return d(void 0)};void 0!==r.start&&(i=function(){return r.start(n)});void 0!==r.write&&(a=function(e){return r.write(e,n)});void 0!==r.close&&(u=function(){return r.close()});void 0!==r.abort&&(l=function(e){return r.abort(e)});mr(e,n,i,a,u,l,t,o)}(this,o,Me(t,1),n)}return Object.defineProperty(WritableStream.prototype,"locked",{get:function(){if(!Ze(this))throw Tr("locked");return $e(this)},enumerable:!1,configurable:!0}),WritableStream.prototype.abort=function(e){return void 0===e&&(e=void 0),Ze(this)?$e(this)?f(new TypeError("Cannot abort a stream that already has a writer")):er(this,e):f(Tr("abort"))},WritableStream.prototype.close=function(){return Ze(this)?$e(this)?f(new TypeError("Cannot close a stream that already has a writer")):ir(this)?f(new TypeError("Cannot close an already-closing stream")):rr(this):f(Tr("close"))},WritableStream.prototype.getWriter=function(){if(!Ze(this))throw Tr("getWriter");return Je(this)},WritableStream}();function Je(e){return new lr(e)}function Ke(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new w,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function Ze(e){return!!n(e)&&!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")}function $e(e){return void 0!==e._writer}function er(e,r){var t=e._state;if("closed"===t||"errored"===t)return d(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;var o=!1;"erroring"===t&&(o=!0,r=void 0);var n=c((function(t,n){e._pendingAbortRequest={_promise:void 0,_resolve:t,_reject:n,_reason:r,_wasAlreadyErroring:o}}));return e._pendingAbortRequest._promise=n,o||or(e,r),n}function rr(e){var r=e._state;if("closed"===r||"errored"===r)return f(new TypeError("The stream (in "+r+" state) is not in the writable state and cannot be closed"));var t,o=c((function(r,t){var o={_resolve:r,_reject:t};e._closeRequest=o})),n=e._writer;return void 0!==n&&e._backpressure&&"writable"===r&&zr(n),ce(t=e._writableStreamController,hr,0),gr(t),o}function tr(e,r){"writable"!==e._state?nr(e):or(e,r)}function or(e,r){var t=e._writableStreamController;e._state="erroring",e._storedError=r;var o=e._writer;void 0!==o&&fr(o,r),!function(e){if(void 0===e._inFlightWriteRequest&&void 0===e._inFlightCloseRequest)return!1;return!0}(e)&&t._started&&nr(e)}function nr(e){e._state="errored",e._writableStreamController[B]();var r=e._storedError;if(e._writeRequests.forEach((function(e){e._reject(r)})),e._writeRequests=new w,void 0!==e._pendingAbortRequest){var t=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,t._wasAlreadyErroring)return t._reject(r),void ar(e);p(e._writableStreamController[j](t._reason),(function(){t._resolve(),ar(e)}),(function(r){t._reject(r),ar(e)}))}else ar(e)}function ir(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function ar(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);var r=e._writer;void 0!==r&&Or(r,e._storedError)}function ur(e,r){var t=e._writer;void 0!==t&&r!==e._backpressure&&(r?function(e){jr(e)}(t):zr(t)),e._backpressure=r}Object.defineProperties(Xe.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(Xe.prototype,r.toStringTag,{value:"WritableStream",configurable:!0});var lr=function(){function WritableStreamDefaultWriter(e){if(M(e,1,"WritableStreamDefaultWriter"),Ge(e,"First parameter"),$e(e))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;var r,t=e._state;if("writable"===t)!ir(e)&&e._backpressure?jr(this):kr(this),Cr(this);else if("erroring"===t)Br(this,e._storedError),Cr(this);else if("closed"===t)kr(this),Cr(r=this),Wr(r);else{var o=e._storedError;Br(this,o),Er(this,o)}}return Object.defineProperty(WritableStreamDefaultWriter.prototype,"closed",{get:function(){return sr(this)?this._closedPromise:f(Pr("closed"))},enumerable:!1,configurable:!0}),Object.defineProperty(WritableStreamDefaultWriter.prototype,"desiredSize",{get:function(){if(!sr(this))throw Pr("desiredSize");if(void 0===this._ownerWritableStream)throw qr("desiredSize");return function(e){var r=e._ownerWritableStream,t=r._state;if("errored"===t||"erroring"===t)return null;if("closed"===t)return 0;return vr(r._writableStreamController)}(this)},enumerable:!1,configurable:!0}),Object.defineProperty(WritableStreamDefaultWriter.prototype,"ready",{get:function(){return sr(this)?this._readyPromise:f(Pr("ready"))},enumerable:!1,configurable:!0}),WritableStreamDefaultWriter.prototype.abort=function(e){return void 0===e&&(e=void 0),sr(this)?void 0===this._ownerWritableStream?f(qr("abort")):function(e,r){return er(e._ownerWritableStream,r)}(this,e):f(Pr("abort"))},WritableStreamDefaultWriter.prototype.close=function(){if(!sr(this))return f(Pr("close"));var e=this._ownerWritableStream;return void 0===e?f(qr("close")):ir(e)?f(new TypeError("Cannot close an already-closing stream")):cr(this)},WritableStreamDefaultWriter.prototype.releaseLock=function(){if(!sr(this))throw Pr("releaseLock");void 0!==this._ownerWritableStream&&br(this)},WritableStreamDefaultWriter.prototype.write=function(e){return void 0===e&&(e=void 0),sr(this)?void 0===this._ownerWritableStream?f(qr("write to")):pr(this,e):f(Pr("write"))},WritableStreamDefaultWriter}();function sr(e){return!!n(e)&&!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")}function cr(e){return rr(e._ownerWritableStream)}function dr(e,r){"pending"===e._closedPromiseState?Or(e,r):function(e,r){Er(e,r)}(e,r)}function fr(e,r){"pending"===e._readyPromiseState?Ar(e,r):function(e,r){Br(e,r)}(e,r)}function br(e){var r=e._ownerWritableStream,t=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");fr(e,t),dr(e,t),r._writer=void 0,e._ownerWritableStream=void 0}function pr(e,r){var t=e._ownerWritableStream,o=t._writableStreamController,n=function(e,r){try{return e._strategySizeAlgorithm(r)}catch(r){return Sr(e,r),1}}(o,r);if(t!==e._ownerWritableStream)return f(qr("write to"));var i=t._state;if("errored"===i)return f(t._storedError);if(ir(t)||"closed"===i)return f(new TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===i)return f(t._storedError);var a=function(e){return c((function(r,t){var o={_resolve:r,_reject:t};e._writeRequests.push(o)}))}(t);return function(e,r,t){try{ce(e,r,t)}catch(r){return void Sr(e,r)}var o=e._controlledWritableStream;if(!ir(o)&&"writable"===o._state){var n=wr(e);ur(o,n)}gr(e)}(o,r,n),a}Object.defineProperties(lr.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(lr.prototype,r.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});var hr={},_r=function(){function WritableStreamDefaultController(){throw new TypeError("Illegal constructor")}return WritableStreamDefaultController.prototype.error=function(e){if(void 0===e&&(e=void 0),!function(e){if(!n(e))return!1;if(!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream"))return!1;return!0}(this))throw new TypeError("WritableStreamDefaultController.prototype.error can only be used on a WritableStreamDefaultController");"writable"===this._controlledWritableStream._state&&Rr(this,e)},WritableStreamDefaultController.prototype[j]=function(e){var r=this._abortAlgorithm(e);return yr(this),r},WritableStreamDefaultController.prototype[B]=function(){de(this)},WritableStreamDefaultController}();function mr(e,r,t,o,n,i,a,u){r._controlledWritableStream=e,e._writableStreamController=r,r._queue=void 0,r._queueTotalSize=void 0,de(r),r._started=!1,r._strategySizeAlgorithm=u,r._strategyHWM=a,r._writeAlgorithm=o,r._closeAlgorithm=n,r._abortAlgorithm=i;var l=wr(r);ur(e,l),p(d(t()),(function(){r._started=!0,gr(r)}),(function(t){r._started=!0,tr(e,t)}))}function yr(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function vr(e){return e._strategyHWM-e._queueTotalSize}function gr(e){var r=e._controlledWritableStream;if(e._started&&void 0===r._inFlightWriteRequest)if("erroring"!==r._state){if(0!==e._queue.length){var t=e._queue.peek().value;t===hr?function(e){var r=e._controlledWritableStream;(function(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0})(r),se(e);var t=e._closeAlgorithm();yr(e),p(t,(function(){!function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,"erroring"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";var r=e._writer;void 0!==r&&Wr(r)}(r)}),(function(e){!function(e,r){e._inFlightCloseRequest._reject(r),e._inFlightCloseRequest=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._reject(r),e._pendingAbortRequest=void 0),tr(e,r)}(r,e)}))}(e):function(e,r){var t=e._controlledWritableStream;(function(e){e._inFlightWriteRequest=e._writeRequests.shift()})(t),p(e._writeAlgorithm(r),(function(){!function(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}(t);var r=t._state;if(se(e),!ir(t)&&"writable"===r){var o=wr(e);ur(t,o)}gr(e)}),(function(r){"writable"===t._state&&yr(e),function(e,r){e._inFlightWriteRequest._reject(r),e._inFlightWriteRequest=void 0,tr(e,r)}(t,r)}))}(e,t)}}else nr(r)}function Sr(e,r){"writable"===e._controlledWritableStream._state&&Rr(e,r)}function wr(e){return vr(e)<=0}function Rr(e,r){var t=e._controlledWritableStream;yr(e),or(t,r)}function Tr(e){return new TypeError("WritableStream.prototype."+e+" can only be used on a WritableStream")}function Pr(e){return new TypeError("WritableStreamDefaultWriter.prototype."+e+" can only be used on a WritableStreamDefaultWriter")}function qr(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function Cr(e){e._closedPromise=c((function(r,t){e._closedPromise_resolve=r,e._closedPromise_reject=t,e._closedPromiseState="pending"}))}function Er(e,r){Cr(e),Or(e,r)}function Or(e,r){void 0!==e._closedPromise_reject&&(y(e._closedPromise),e._closedPromise_reject(r),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function Wr(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function jr(e){e._readyPromise=c((function(r,t){e._readyPromise_resolve=r,e._readyPromise_reject=t})),e._readyPromiseState="pending"}function Br(e,r){jr(e),Ar(e,r)}function kr(e){jr(e),zr(e)}function Ar(e,r){void 0!==e._readyPromise_reject&&(y(e._readyPromise),e._readyPromise_reject(r),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function zr(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}Object.defineProperties(_r.prototype,{error:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(_r.prototype,r.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});var Dr="undefined"!=typeof DOMException?DOMException:void 0;var Fr,Lr=function(e){if("function"!=typeof e&&"object"!=typeof e)return!1;try{return new e,!0}catch(e){return!1}}(Dr)?Dr:((Fr=function(e,r){this.message=e||"",this.name=r||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}).prototype=Object.create(Error.prototype),Object.defineProperty(Fr.prototype,"constructor",{value:Fr,writable:!0,configurable:!0}),Fr);function Ir(e,r,o,n,i,a){var u=V(e),l=Je(r);e._disturbed=!0;var s=!1,m=d(void 0);return c((function(v,g){var S,w,R,T;if(void 0!==a){if(S=function(){var t=new Lr("Aborted","AbortError"),o=[];n||o.push((function(){return"writable"===r._state?er(r,t):d(void 0)})),i||o.push((function(){return"readable"===e._state?st(e,t):d(void 0)})),O((function(){return Promise.all(o.map((function(e){return e()})))}),!0,t)},a.aborted)return void S();a.addEventListener("abort",S)}if(E(e,u._closedPromise,(function(e){n?W(!0,e):O((function(){return er(r,e)}),!0,e)})),E(r,l._closedPromise,(function(r){i?W(!0,r):O((function(){return st(e,r)}),!0,r)})),w=e,R=u._closedPromise,T=function(){o?W():O((function(){return function(e){var r=e._ownerWritableStream,t=r._state;return ir(r)||"closed"===t?d(void 0):"errored"===t?f(r._storedError):cr(e)}(l)}))},"closed"===w._state?T():h(R,T),ir(r)||"closed"===r._state){var q=new TypeError("the destination writable stream closed before all data could be piped to it");i?W(!0,q):O((function(){return st(e,q)}),!0,q)}function C(){var e=m;return b(m,(function(){return e!==m?C():void 0}))}function E(e,r,t){"errored"===e._state?t(e._storedError):_(r,t)}function O(e,t,o){function n(){p(e(),(function(){return j(t,o)}),(function(e){return j(!0,e)}))}s||(s=!0,"writable"!==r._state||ir(r)?n():h(C(),n))}function W(e,t){s||(s=!0,"writable"!==r._state||ir(r)?j(e,t):h(C(),(function(){return j(e,t)})))}function j(e,r){br(l),P(u),void 0!==a&&a.removeEventListener("abort",S),e?g(r):v(void 0)}y(c((function(e,r){!function o(n){n?e():b(s?d(!0):b(l._readyPromise,(function(){return c((function(e,r){re(u,{_chunkSteps:function(r){m=b(pr(l,r),void 0,t),e(!1)},_closeSteps:function(){return e(!0)},_errorSteps:r})}))})),o,r)}(!1)})))}))}var Mr=function(){function ReadableStreamDefaultController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(ReadableStreamDefaultController.prototype,"desiredSize",{get:function(){if(!Qr(this))throw Kr("desiredSize");return Gr(this)},enumerable:!1,configurable:!0}),ReadableStreamDefaultController.prototype.close=function(){if(!Qr(this))throw Kr("close");if(!Xr(this))throw new TypeError("The stream is not in a state that permits close");Hr(this)},ReadableStreamDefaultController.prototype.enqueue=function(e){if(void 0===e&&(e=void 0),!Qr(this))throw Kr("enqueue");if(!Xr(this))throw new TypeError("The stream is not in a state that permits enqueue");return Vr(this,e)},ReadableStreamDefaultController.prototype.error=function(e){if(void 0===e&&(e=void 0),!Qr(this))throw Kr("error");Ur(this,e)},ReadableStreamDefaultController.prototype[k]=function(e){de(this);var r=this._cancelAlgorithm(e);return Nr(this),r},ReadableStreamDefaultController.prototype[A]=function(e){var r=this._controlledReadableStream;if(this._queue.length>0){var t=se(this);this._closeRequested&&0===this._queue.length?(Nr(this),ct(r)):Yr(this),e._chunkSteps(t)}else U(r,e),Yr(this)},ReadableStreamDefaultController}();function Qr(e){return!!n(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")}function Yr(e){xr(e)&&(e._pulling?e._pullAgain=!0:(e._pulling=!0,p(e._pullAlgorithm(),(function(){e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,Yr(e))}),(function(r){Ur(e,r)}))))}function xr(e){var r=e._controlledReadableStream;return!!Xr(e)&&(!!e._started&&(!!(lt(r)&&X(r)>0)||Gr(e)>0))}function Nr(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function Hr(e){if(Xr(e)){var r=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(Nr(e),ct(r))}}function Vr(e,r){if(Xr(e)){var t=e._controlledReadableStream;if(lt(t)&&X(t)>0)G(t,r,!1);else{var o=void 0;try{o=e._strategySizeAlgorithm(r)}catch(r){throw Ur(e,r),r}try{ce(e,r,o)}catch(r){throw Ur(e,r),r}}Yr(e)}}function Ur(e,r){var t=e._controlledReadableStream;"readable"===t._state&&(de(e),Nr(e),dt(t,r))}function Gr(e){var r=e._controlledReadableStream._state;return"errored"===r?null:"closed"===r?0:e._strategyHWM-e._queueTotalSize}function Xr(e){var r=e._controlledReadableStream._state;return!e._closeRequested&&"readable"===r}function Jr(e,r,t,o,n,i,a){r._controlledReadableStream=e,r._queue=void 0,r._queueTotalSize=void 0,de(r),r._started=!1,r._closeRequested=!1,r._pullAgain=!1,r._pulling=!1,r._strategySizeAlgorithm=a,r._strategyHWM=i,r._pullAlgorithm=o,r._cancelAlgorithm=n,e._readableStreamController=r,p(d(t()),(function(){r._started=!0,Yr(r)}),(function(e){Ur(r,e)}))}function Kr(e){return new TypeError("ReadableStreamDefaultController.prototype."+e+" can only be used on a ReadableStreamDefaultController")}function Zr(e,r,t){return L(e,t),function(t){return S(e,r,[t])}}function $r(e,r,t){return L(e,t),function(t){return S(e,r,[t])}}function et(e,r,t){return L(e,t),function(t){return g(e,r,[t])}}function rt(e,r){if("bytes"!==(e=""+e))throw new TypeError(r+" '"+e+"' is not a valid enumeration value for ReadableStreamType");return e}function tt(e,r){if("byob"!==(e=""+e))throw new TypeError(r+" '"+e+"' is not a valid enumeration value for ReadableStreamReaderMode");return e}function ot(e,r){F(e,r);var t=null==e?void 0:e.preventAbort,o=null==e?void 0:e.preventCancel,n=null==e?void 0:e.preventClose,i=null==e?void 0:e.signal;return void 0!==i&&function(e,r){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw new TypeError(r+" is not an AbortSignal.")}(i,r+" has member 'signal' that"),{preventAbort:Boolean(t),preventCancel:Boolean(o),preventClose:Boolean(n),signal:i}}Object.defineProperties(Mr.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(Mr.prototype,r.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});var nt=function(){function ReadableStream(e,r){void 0===e&&(e={}),void 0===r&&(r={}),void 0===e?e=null:I(e,"First parameter");var t=Ye(r,"Second parameter"),o=function(e,r){F(e,r);var t=e,o=null==t?void 0:t.autoAllocateChunkSize,n=null==t?void 0:t.cancel,i=null==t?void 0:t.pull,a=null==t?void 0:t.start,u=null==t?void 0:t.type;return{autoAllocateChunkSize:void 0===o?void 0:N(o,r+" has member 'autoAllocateChunkSize' that"),cancel:void 0===n?void 0:Zr(n,t,r+" has member 'cancel' that"),pull:void 0===i?void 0:$r(i,t,r+" has member 'pull' that"),start:void 0===a?void 0:et(a,t,r+" has member 'start' that"),type:void 0===u?void 0:rt(u,r+" has member 'type' that")}}(e,"First parameter");if(at(this),"bytes"===o.type){if(void 0!==t.size)throw new RangeError("The strategy for a byte stream cannot have a size function");je(this,o,Me(t,0))}else{var n=Qe(t);!function(e,r,t,o){var n=Object.create(Mr.prototype),i=function(){},a=function(){return d(void 0)},u=function(){return d(void 0)};void 0!==r.start&&(i=function(){return r.start(n)}),void 0!==r.pull&&(a=function(){return r.pull(n)}),void 0!==r.cancel&&(u=function(e){return r.cancel(e)}),Jr(e,n,i,a,u,t,o)}(this,o,Me(t,1),n)}}return Object.defineProperty(ReadableStream.prototype,"locked",{get:function(){if(!ut(this))throw ft("locked");return lt(this)},enumerable:!1,configurable:!0}),ReadableStream.prototype.cancel=function(e){return void 0===e&&(e=void 0),ut(this)?lt(this)?f(new TypeError("Cannot cancel a stream that already has a reader")):st(this,e):f(ft("cancel"))},ReadableStream.prototype.getReader=function(e){if(void 0===e&&(e=void 0),!ut(this))throw ft("getReader");return void 0===function(e,r){F(e,r);var t=null==e?void 0:e.mode;return{mode:void 0===t?void 0:tt(t,r+" has member 'mode' that")}}(e,"First parameter").mode?V(this):new Fe(this)},ReadableStream.prototype.pipeThrough=function(e,r){if(void 0===r&&(r={}),!ut(this))throw ft("pipeThrough");M(e,1,"pipeThrough");var t=function(e,r){F(e,r);var t=null==e?void 0:e.readable;Q(t,"readable","ReadableWritablePair"),H(t,r+" has member 'readable' that");var o=null==e?void 0:e.writable;return Q(o,"writable","ReadableWritablePair"),Ge(o,r+" has member 'writable' that"),{readable:t,writable:o}}(e,"First parameter"),o=ot(r,"Second parameter");if(lt(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if($e(t.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return y(Ir(this,t.writable,o.preventClose,o.preventAbort,o.preventCancel,o.signal)),t.readable},ReadableStream.prototype.pipeTo=function(e,r){if(void 0===r&&(r={}),!ut(this))return f(ft("pipeTo"));if(void 0===e)return f("Parameter 1 is required in 'pipeTo'.");if(!Ze(e))return f(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));var t;try{t=ot(r,"Second parameter")}catch(e){return f(e)}return lt(this)?f(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):$e(e)?f(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):Ir(this,e,t.preventClose,t.preventAbort,t.preventCancel,t.signal)},ReadableStream.prototype.tee=function(){if(!ut(this))throw ft("tee");var e=function(e,r){var t,o,n,i,a,u=V(e),l=!1,s=!1,f=!1,b=c((function(e){a=e}));function p(){return l||(l=!0,re(u,{_chunkSteps:function(e){v((function(){l=!1;var r=e,t=e;s||Vr(n._readableStreamController,r),f||Vr(i._readableStreamController,t),a(void 0)}))},_closeSteps:function(){l=!1,s||Hr(n._readableStreamController),f||Hr(i._readableStreamController)},_errorSteps:function(){l=!1}})),d(void 0)}function h(){}return n=it(h,p,(function(r){if(s=!0,t=r,f){var n=fe([t,o]),i=st(e,n);a(i)}return b})),i=it(h,p,(function(r){if(f=!0,o=r,s){var n=fe([t,o]),i=st(e,n);a(i)}return b})),_(u._closedPromise,(function(e){Ur(n._readableStreamController,e),Ur(i._readableStreamController,e),a(void 0)})),[n,i]}(this);return fe(e)},ReadableStream.prototype.values=function(e){if(void 0===e&&(e=void 0),!ut(this))throw ft("values");var r,t,o,n,i,a=function(e,r){F(e,r);var t=null==e?void 0:e.preventCancel;return{preventCancel:Boolean(t)}}(e,"First parameter");return r=this,t=a.preventCancel,o=V(r),n=new oe(o,t),(i=Object.create(ne))._asyncIteratorImpl=n,i},ReadableStream}();function it(e,r,t,o,n){void 0===o&&(o=1),void 0===n&&(n=function(){return 1});var i=Object.create(nt.prototype);return at(i),Jr(i,Object.create(Mr.prototype),e,r,t,o,n),i}function at(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function ut(e){return!!n(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")}function lt(e){return void 0!==e._reader}function st(e,r){return e._disturbed=!0,"closed"===e._state?d(void 0):"errored"===e._state?f(e._storedError):(ct(e),m(e._readableStreamController[k](r),t))}function ct(e){e._state="closed";var r=e._reader;void 0!==r&&(ee(r)&&(r._readRequests.forEach((function(e){e._closeSteps()})),r._readRequests=new w),W(r))}function dt(e,r){e._state="errored",e._storedError=r;var t=e._reader;void 0!==t&&(ee(t)?(t._readRequests.forEach((function(e){e._errorSteps(r)})),t._readRequests=new w):(t._readIntoRequests.forEach((function(e){e._errorSteps(r)})),t._readIntoRequests=new w),O(t,r))}function ft(e){return new TypeError("ReadableStream.prototype."+e+" can only be used on a ReadableStream")}function bt(e,r){F(e,r);var t=null==e?void 0:e.highWaterMark;return Q(t,"highWaterMark","QueuingStrategyInit"),{highWaterMark:Y(t)}}Object.defineProperties(nt.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(nt.prototype,r.toStringTag,{value:"ReadableStream",configurable:!0}),"symbol"==typeof r.asyncIterator&&Object.defineProperty(nt.prototype,r.asyncIterator,{value:nt.prototype.values,writable:!0,configurable:!0});var pt=function size(e){return e.byteLength},ht=function(){function ByteLengthQueuingStrategy(e){M(e,1,"ByteLengthQueuingStrategy"),e=bt(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}return Object.defineProperty(ByteLengthQueuingStrategy.prototype,"highWaterMark",{get:function(){if(!mt(this))throw _t("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark},enumerable:!1,configurable:!0}),Object.defineProperty(ByteLengthQueuingStrategy.prototype,"size",{get:function(){if(!mt(this))throw _t("size");return pt},enumerable:!1,configurable:!0}),ByteLengthQueuingStrategy}();function _t(e){return new TypeError("ByteLengthQueuingStrategy.prototype."+e+" can only be used on a ByteLengthQueuingStrategy")}function mt(e){return!!n(e)&&!!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")}Object.defineProperties(ht.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(ht.prototype,r.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});var yt=function size(){return 1},vt=function(){function CountQueuingStrategy(e){M(e,1,"CountQueuingStrategy"),e=bt(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}return Object.defineProperty(CountQueuingStrategy.prototype,"highWaterMark",{get:function(){if(!St(this))throw gt("highWaterMark");return this._countQueuingStrategyHighWaterMark},enumerable:!1,configurable:!0}),Object.defineProperty(CountQueuingStrategy.prototype,"size",{get:function(){if(!St(this))throw gt("size");return yt},enumerable:!1,configurable:!0}),CountQueuingStrategy}();function gt(e){return new TypeError("CountQueuingStrategy.prototype."+e+" can only be used on a CountQueuingStrategy")}function St(e){return!!n(e)&&!!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")}function wt(e,r,t){return L(e,t),function(t){return S(e,r,[t])}}function Rt(e,r,t){return L(e,t),function(t){return g(e,r,[t])}}function Tt(e,r,t){return L(e,t),function(t,o){return S(e,r,[t,o])}}Object.defineProperties(vt.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(vt.prototype,r.toStringTag,{value:"CountQueuingStrategy",configurable:!0});var Pt=function(){function TransformStream(e,r,t){void 0===e&&(e={}),void 0===r&&(r={}),void 0===t&&(t={}),void 0===e&&(e=null);var o=Ye(r,"Second parameter"),n=Ye(t,"Third parameter"),i=function(e,r){F(e,r);var t=null==e?void 0:e.flush,o=null==e?void 0:e.readableType,n=null==e?void 0:e.start,i=null==e?void 0:e.transform,a=null==e?void 0:e.writableType;return{flush:void 0===t?void 0:wt(t,e,r+" has member 'flush' that"),readableType:o,start:void 0===n?void 0:Rt(n,e,r+" has member 'start' that"),transform:void 0===i?void 0:Tt(i,e,r+" has member 'transform' that"),writableType:a}}(e,"First parameter");if(void 0!==i.readableType)throw new RangeError("Invalid readableType specified");if(void 0!==i.writableType)throw new RangeError("Invalid writableType specified");var a,u=Me(n,0),l=Qe(n),s=Me(o,1),b=Qe(o);!function(e,r,t,o,n,i){function a(){return r}e._writable=function(e,r,t,o,n,i){void 0===n&&(n=1),void 0===i&&(i=function(){return 1});var a=Object.create(Xe.prototype);return Ke(a),mr(a,Object.create(_r.prototype),e,r,t,o,n,i),a}(a,(function(r){return function(e,r){var t=e._transformStreamController;if(e._backpressure){return m(e._backpressureChangePromise,(function(){var o=e._writable;if("erroring"===o._state)throw o._storedError;return At(t,r)}))}return At(t,r)}(e,r)}),(function(){return function(e){var r=e._readable,t=e._transformStreamController,o=t._flushAlgorithm();return Bt(t),m(o,(function(){if("errored"===r._state)throw r._storedError;Hr(r._readableStreamController)}),(function(t){throw Ct(e,t),r._storedError}))}(e)}),(function(r){return function(e,r){return Ct(e,r),d(void 0)}(e,r)}),t,o),e._readable=it(a,(function(){return function(e){return Ot(e,!1),e._backpressureChangePromise}(e)}),(function(r){return Et(e,r),d(void 0)}),n,i),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,Ot(e,!0),e._transformStreamController=void 0}(this,c((function(e){a=e})),s,b,u,l),function(e,r){var t=Object.create(Wt.prototype),o=function(e){try{return kt(t,e),d(void 0)}catch(e){return f(e)}},n=function(){return d(void 0)};void 0!==r.transform&&(o=function(e){return r.transform(e,t)});void 0!==r.flush&&(n=function(){return r.flush(t)});!function(e,r,t,o){r._controlledTransformStream=e,e._transformStreamController=r,r._transformAlgorithm=t,r._flushAlgorithm=o}(e,t,o,n)}(this,i),void 0!==i.start?a(i.start(this._transformStreamController)):a(void 0)}return Object.defineProperty(TransformStream.prototype,"readable",{get:function(){if(!qt(this))throw Dt("readable");return this._readable},enumerable:!1,configurable:!0}),Object.defineProperty(TransformStream.prototype,"writable",{get:function(){if(!qt(this))throw Dt("writable");return this._writable},enumerable:!1,configurable:!0}),TransformStream}();function qt(e){return!!n(e)&&!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")}function Ct(e,r){Ur(e._readable._readableStreamController,r),Et(e,r)}function Et(e,r){Bt(e._transformStreamController),Sr(e._writable._writableStreamController,r),e._backpressure&&Ot(e,!1)}function Ot(e,r){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=c((function(r){e._backpressureChangePromise_resolve=r})),e._backpressure=r}Object.defineProperties(Pt.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(Pt.prototype,r.toStringTag,{value:"TransformStream",configurable:!0});var Wt=function(){function TransformStreamDefaultController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(TransformStreamDefaultController.prototype,"desiredSize",{get:function(){if(!jt(this))throw zt("desiredSize");return Gr(this._controlledTransformStream._readable._readableStreamController)},enumerable:!1,configurable:!0}),TransformStreamDefaultController.prototype.enqueue=function(e){if(void 0===e&&(e=void 0),!jt(this))throw zt("enqueue");kt(this,e)},TransformStreamDefaultController.prototype.error=function(e){if(void 0===e&&(e=void 0),!jt(this))throw zt("error");var r;r=e,Ct(this._controlledTransformStream,r)},TransformStreamDefaultController.prototype.terminate=function(){if(!jt(this))throw zt("terminate");!function(e){var r=e._controlledTransformStream;Hr(r._readable._readableStreamController);var t=new TypeError("TransformStream terminated");Et(r,t)}(this)},TransformStreamDefaultController}();function jt(e){return!!n(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")}function Bt(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0}function kt(e,r){var t=e._controlledTransformStream,o=t._readable._readableStreamController;if(!Xr(o))throw new TypeError("Readable side is not in a state that permits enqueue");try{Vr(o,r)}catch(e){throw Et(t,e),t._readable._storedError}(function(e){return!xr(e)})(o)!==t._backpressure&&Ot(t,!0)}function At(e,r){return m(e._transformAlgorithm(r),void 0,(function(r){throw Ct(e._controlledTransformStream,r),r}))}function zt(e){return new TypeError("TransformStreamDefaultController.prototype."+e+" can only be used on a TransformStreamDefaultController")}function Dt(e){return new TypeError("TransformStream.prototype."+e+" can only be used on a TransformStream")}Object.defineProperties(Wt.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(Wt.prototype,r.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});var Ft={ReadableStream:nt,ReadableStreamDefaultController:Mr,ReadableByteStreamController:pe,ReadableStreamBYOBRequest:be,ReadableStreamDefaultReader:$,ReadableStreamBYOBReader:Fe,WritableStream:Xe,WritableStreamDefaultController:_r,WritableStreamDefaultWriter:lr,ByteLengthQueuingStrategy:ht,CountQueuingStrategy:vt,TransformStream:Pt,TransformStreamDefaultController:Wt};if(void 0!==o)for(var Lt in Ft)Object.prototype.hasOwnProperty.call(Ft,Lt)&&Object.defineProperty(o,Lt,{value:Ft[Lt],writable:!0,configurable:!0});e.ByteLengthQueuingStrategy=ht,e.CountQueuingStrategy=vt,e.ReadableByteStreamController=pe,e.ReadableStream=nt,e.ReadableStreamBYOBReader=Fe,e.ReadableStreamBYOBRequest=be,e.ReadableStreamDefaultController=Mr,e.ReadableStreamDefaultReader=$,e.TransformStream=Pt,e.TransformStreamDefaultController=Wt,e.WritableStream=Xe,e.WritableStreamDefaultController=_r,e.WritableStreamDefaultWriter=lr,Object.defineProperty(e,"__esModule",{value:!0})}));
