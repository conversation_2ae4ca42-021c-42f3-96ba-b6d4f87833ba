name: Update Advanced Settings Markdown TOC
on:
  workflow_dispatch:
  schedule:
    - cron: '0 0 29 2 1'
  
jobs:
  update-toc:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          repository: ${{github.repository}}.wiki
      - name: Run github-markdown-toc
        run: |
          curl https://raw.githubusercontent.com/ekalinin/github-markdown-toc/master/gh-md-toc -o gh-md-toc
          chmod a+x gh-md-toc
          ./gh-md-toc --insert --no-backup Advanced-Settings.md
      - uses: stefanzweifel/git-auto-commit-action@v4
        with:
          commit_message: "Auto update markdown TOC"
